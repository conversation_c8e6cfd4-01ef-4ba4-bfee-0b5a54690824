openapi: 3.0.1
info:
  title: Choreo Observability API
  description: API for retrieving telemetry data of choreo user applications.
  contact:
    email: <EMAIL>
  version: 0.2.0
servers:
  - url: https://{hostname}/obsapi
    variables:
      hostname:
        default: app.choreo.dev
security:
  - default: []
paths:
  /healthz:
    get:
      summary: Retrieve health of the obsapi
      description: 'Retrieve the status of the API. This may not reflect exactly the current state of the API. '
      operationId: checkHealth
      responses:
        "200":
          description: Response indicating an ok status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthOkResponse'
        "503":
          description: Response indicating a service unavailable status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /*:
    post:
      summary: GraphQL endpoint for obsapi
      description: 'GraphQL endpoint used to query data from the obsapi. '
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GraphQLQuery'
      responses:
        "200":
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GraphQLResponse'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    GraphQLQuery:
      type: object
      properties:
        query:
          type: string
    GraphQLResponse:
      type: object
      properties:
        data:
          type: object
        error:
          type: object
    HealthOkResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - OK
    HealthErrorResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - ERROR
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://choreo-obsapi.choreo-observability.svc.cluster.local:9095/obsapi
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-obsapi.choreo-observability.svc.cluster.local:9095/obsapi
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreoobsapi/0.2.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
