{"id": "6694dfcb9c63ed2c4b1fdbd4", "name": "Choreo Observability API", "displayName": "Choreo Observability API", "description": "API for retrieving telemetry data of choreo user applications.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreoobsapi", "version": "0.3.0", "provider": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Bronze"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": [], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["Authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1721032651279", "lastUpdatedTime": "2025-02-10 08:23:36.01", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://choreo-obsapi-v2.choreo-observability.svc.cluster.local:9095"}, "production_endpoints": {"url": "http://choreo-obsapi-v2.choreo-observability.svc.cluster.local:9095"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": "urn:choreosystem:choreoobsapi:", "operations": [{"id": "", "target": "/healthz", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/metrics/component/http", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/metrics/component/usage", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/metrics/project/http", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/tasks/executions", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/tasks/executions/{id}", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/tasks/executions/{id}/attempts", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/tasks/executions/count", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/metrics/organization/usage", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/metrics/project/usage", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": "https://5659b6b7-1063-41ed-8e39-d91857699255-systemapis.e1-us-east-azure.st.choreoapis.dev/systemapis/choreoobsapi/0.3.0", "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": null, "componentId": null, "versionId": "6694dfcb9c63ed2c4b1fdbd4"}}