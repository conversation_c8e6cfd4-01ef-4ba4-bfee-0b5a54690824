swagger: "2.0"
info:
  description: API used to provided observability data to within the Choreo platform
  version: 0.3.0
  title: Choreo Observability API
  contact: {}
basePath: /systemapis/choreoobsapi/0.3.0
security:
  - default: []
paths:
  /healthz:
    get:
      tags:
        - health
      summary: Check the health of the service
      description: Check the health of the service and its dependencies
      produces:
        - text/plain
      parameters: []
      responses:
        "200":
          description: OK
          schema:
            type: string
        "500":
          description: obsmanager health check failed
          schema:
            type: string
      security:
        - Bearer: []
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /metrics/component/http:
    get:
      parameters: []
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /metrics/component/usage:
    get:
      parameters: []
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.CadvisorMetrics'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /metrics/project/http:
    get:
      parameters: []
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /tasks/executions:
    get:
      description: Get executions by component release ID, paginated by cursor and limit
      produces:
        - application/json
      parameters:
        - name: releaseId
          in: query
          description: Component release ID
          required: true
          type: string
          x-example: 12345678-1234-abcd-qwer-asdfghjklzxc
        - name: cursor
          in: query
          description: 'Upper bound time in Unix timestamp (default: current time)'
          required: false
          type: integer
          x-example: 1727955000
        - name: limit
          in: query
          description: 'Maximum number of jobs to return (default: 10)'
          required: false
          type: integer
          x-example: 10
        - name: verbose
          in: query
          description: 'Verbose mode (default: false)'
          required: false
          type: boolean
          x-example: true
      responses:
        "200":
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/model.Job'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /tasks/executions/{id}:
    get:
      description: Get details of an execution by execution ID and release ID
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: Execution ID
          required: true
          type: string
          x-example: 12345678-1234-abcd-qwer-asdfghjklzxc
        - name: releaseId
          in: query
          description: Component release ID
          required: true
          type: string
          x-example: sample-task-1551604060-287471187
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.Job'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /tasks/executions/{id}/attempts:
    get:
      description: Get attempts of an execution by execution ID and release ID
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: Execution ID
          required: true
          type: string
          x-example: sample-task-1551604060-287471187
        - name: releaseId
          in: query
          description: Component release ID
          required: true
          type: string
          x-example: 12345678-1234-abcd-qwer-asdfghjklzxc
      responses:
        "200":
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/model.Pod'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /tasks/executions/count:
    get:
      description: Get executions count by component release ID for given time period
      produces:
        - application/json
      parameters:
        - name: releaseId
          in: query
          description: Component release ID
          required: true
          type: string
          x-example: 12345678-1234-abcd-qwer-asdfghjklzxc
        - name: from
          in: query
          description: Start time (2024-05-08T08:43:55.748Z)
          required: true
          type: string
          x-example: "2024-05-08T08:43:55.748Z"
        - name: to
          in: query
          description: End time (2024-05-08T08:43:55.748Z)
          required: true
          type: string
          x-example: "2024-05-08T08:43:55.748Z"
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.JobsCount'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /metrics/organization/usage:
    get:
      parameters: []
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.CadvisorMetrics'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /metrics/project/usage:
    get:
      parameters: []
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/model.CadvisorMetrics'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
securityDefinitions:
  Bearer:
    description: Type "Bearer" followed by a space and JWT token.
    type: apiKey
    name: Authorization
    in: header
  default:
    type: oauth2
    authorizationUrl: https://test.com
    flow: implicit
definitions:
  model.CadvisorMetrics:
    type: object
    properties:
      bytesReceived:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      bytesSent:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      cpuLimits:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      cpuRequests:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      cpuUsage:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      diskIOReads:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      diskIOWrites:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      memory:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      memoryLimits:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      memoryRequests:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
  model.CadvisorResourceAllocations:
    type: object
    properties:
      cpuAllocation:
        $ref: '#/definitions/model.ResourceEntry'
      memoryAllocation:
        $ref: '#/definitions/model.ResourceEntry'
  model.ErrorResponse:
    type: object
    properties:
      error:
        type: string
        example: error message
  model.HistogramEntry:
    type: object
    properties:
      time:
        type: string
        example: "2021-08-25T10:00:00Z"
      value:
        type: number
        example: 10.0
  model.HttpMetrics:
    type: object
    properties:
      failedRequestCountHistogram:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      latencyMeanHistogram:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      latencyPercentiles:
        type: array
        example:
          - "50"
          - "90"
          - "99"
        items:
          type: string
      latencyPercentilesHistogram:
        type: array
        items:
          $ref: '#/definitions/model.PercentilesHistogramEntry'
      successfulRequestCountHistogram:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      totalRequestCountHistogram:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
  model.HubbleComponentMetricsResponse:
    type: object
    properties:
      data:
        $ref: '#/definitions/model.HttpMetrics'
  model.HubbleProjectDiagram:
    type: object
    properties:
      linkList:
        type: array
        items:
          $ref: '#/definitions/model.Link'
      nodeList:
        type: array
        items:
          $ref: '#/definitions/model.Node'
  model.HubbleProjectMetricsResponse:
    type: object
    properties:
      data:
        $ref: '#/definitions/model.HubbleProjectDiagram'
  model.Link:
    type: object
    properties:
      avgLatency:
        type: number
        example: 791117.0
      destinationNodeId:
        type: integer
        example: 1
      errorCount:
        type: number
        example: 2.0
      p50Latency:
        type: number
        example: 604049.0
      p90Latency:
        type: number
        example: 1169871.0
      p99Latency:
        type: number
        example: 1169871.0
      requestCount:
        type: number
        example: 4.0
      sourceNodeId:
        type: integer
        example: 0
  model.Node:
    type: object
    properties:
      apiVersion:
        type: string
        example: v1.0
      componentId:
        type: string
        example: 113e048e-9f79-43fc-ad83-f1dcd660a74e
      componentName:
        type: string
        example: ExampleComponent
      nodeId:
        type: integer
        example: 1
      nodeType:
        type: string
        example: Component
      releaseId:
        type: string
        example: 413e048e-9f79-43fc-ad83-f1dcd660a74e
  model.PercentilesHistogramEntry:
    type: object
    properties:
      time:
        type: string
        example: "2021-08-25T10:00:00Z"
      values:
        type: array
        example:
          - 10
          - 20
          - 30
        items:
          type: number
  model.ResourceEntry:
    type: object
    properties:
      limits:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
      requests:
        type: array
        items:
          $ref: '#/definitions/model.HistogramEntry'
  model.Job:
    type: object
    properties:
      completionTime:
        type: string
        example: "2024-05-08T08:43:55.748Z"
      controllerName:
        type: string
        example: cronjob-1
      failedReason:
        type: string
        example: BackoffLimitExceeded
      id:
        type: string
        example: job-1
      revisionId:
        type: string
        example: commit-sha
      runId:
        type: string
        example: uuid
      startTime:
        type: string
        example: "2024-05-08T08:43:55.748Z"
      status:
        type: string
        example: Succeeded
  model.Pod:
    type: object
    properties:
      Name:
        type: string
        example: pod-1
      completionTime:
        type: string
        example: "2024-05-08T08:43:55.748Z"
      id:
        type: string
        example: pod-1
      startTime:
        type: string
        example: "2024-05-08T08:43:55.748Z"
      status:
        type: string
        example: Succeeded
  model.JobsCount:
    type: object
    properties:
      count:
        type: integer
        example: 10
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - Authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://choreo-obsapi-v2.choreo-observability.svc.cluster.local:9095
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-obsapi-v2.choreo-observability.svc.cluster.local:9095
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreoobsapi/0.3.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
