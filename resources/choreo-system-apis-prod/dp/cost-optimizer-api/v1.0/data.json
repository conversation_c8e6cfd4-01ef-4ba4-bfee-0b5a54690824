{"id": "67c8046dfc952316d8178f8a", "name": "Cost Optimizer", "displayName": "Cost Optimizer", "description": "Cost optimizer provide the cost insights and recommendations.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/systemapis/cost-optimizer", "version": "v1.0", "provider": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Bronze"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>", "x-request-id", "correlation-id"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1741161581228", "lastUpdatedTime": "2025-06-09 06:46:56.559", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://cost-optimizer.choreo-ai:8000/"}, "production_endpoints": {"config": {"actionDuration": "180000"}, "url": "http://cost-optimizer.choreo-ai:8000/"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": "", "operations": [{"id": "", "target": "/health", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/health", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/annual-and-missed-savings", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/annual-and-missed-savings", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/plot", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/plot", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/plot_optimized", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/plot_optimized", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/annual-and-missed-savings", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/annual-and-missed-savings", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/plot", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/plot", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/plot_optimized", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/plot_optimized", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/recommendation-algorithm-configurations", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/recommendation-algorithm-configurations", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/recommendation-algorithm-configurations", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/recommendation-algorithm-configurations", "verb": "POST"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/high-availability", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/high-availability", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/high-availability", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/high-availability", "verb": "POST"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/recommended-cost", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/recommended-cost", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/plots", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/plots", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/plots_optimized", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/plots_optimized", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/max_pod_plots", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/max_pod_plots", "verb": "GET"}}, "operationProxyMapping": null}, {"id": "", "target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/recommendations", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "83805508-6905-429b-86b1-59c6d26ff3d2", "backendOperation": {"target": "/organization/{org_id}/project/{proj_id}/component/{release_id}/recommendations", "verb": "GET"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": "http://cost-optimizer.choreo-ai:8000/", "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": null, "componentId": null, "versionId": "678e21b71cd57f65a1cc021b"}}