openapi: 3.0.0
info:
  title: Cost Optimizer
  contact: {}
  version: v1.0
servers:
  - url: https://choreoapis.dev/systemapis/cost-optimizer/v1.0
security:
  - default: []
paths:
  /health:
    get:
      summary: Health Check
      description: Returns the health status of the service.
      operationId: health_health_get
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: UP
              example:
                status: UP
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/annual-and-missed-savings:
    get:
      tags:
        - Organization
      summary: Get organization-level card totals
      description: Fetches total annual savings and recommended cost (for missed savings calculation) for this month by env filtering.
      operationId: get_organization_annual_and_missed_savings_organization__org_id__annual_and_missed_savings_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: 123e4567-e89b-12d3-a456-426614174000
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnnualAndMissedSavingsResponse'
              example:
                data:
                  - env_id: 98765432-abcd-efgh-ijkl-123456789012
                    recommended_cost: 0.0
                    annual_savings: 0.0
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/plot:
    get:
      tags:
        - Organization
      summary: Get organization-level cost analysis plot
      operationId: get_organization_plot_organization__org_id__plot_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: f5a8c7e6-d4b3-a2c1-9876-543210abcdef
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationMetricsResponse'
              example:
                organization_id: f5a8c7e6-d4b3-a2c1-9876-543210abcdef
                total_monthly_cpu_cost: 0.0
                total_monthly_memory_cost: 0.0
                total_monthly_cost: 0.0
                total_last_month_cost: 0.0
                cpu_efficiency_this_month:
                  utilization: 0.0
                  allocation: 0.0
                memory_efficiency_this_month:
                  utilization: 0.0
                  allocation: 0.0
                cpu_efficiency_last_month:
                  utilization: 0.0
                  allocation: 0.0
                memory_efficiency_last_month:
                  utilization: 0.0
                  allocation: 0.0
                estimated_costs_by_env:
                  additionalProp1: 0.0
                  additionalProp2: 0.0
                  additionalProp3: 0.0
                metrics_by_env_proj:
                  - environment_id: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                    data:
                      table:
                        - cpu:
                            allocation: 0.0
                            utilization: 0.0
                            cost: 0.0
                          memory:
                            allocation: 0.0
                            utilization: 0.0
                            cost: 0.0
                          total_cost: 0.0
                          saved_cost: 0.0
                          project_id: 9876fedc-ba98-7654-3210-fedcba987654
                plot:
                  - date: "2025-03-06T00:00:00+00:00"
                    cpu_allocation_cost: 0.0
                    cpu_utilization_cost: 0.0
                    memory_allocation_cost: 0.0
                    memory_utilization_cost: 0.0
                    is_projected: string
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/plot_optimized:
    get:
      tags:
        - Organization
      summary: Get organization-level cost analysis optimized plot
      operationId: get_organization_optimized_plot_organization__org_id__plot_optimized_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: 72e4d591-fb8c-4aef-b7d2-1598c567a3e9
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationOptimizedPlot'
              example:
                organization_id: 72e4d591-fb8c-4aef-b7d2-1598c567a3e9
                plot:
                  - date: "2025-03-06T00:00:00+00:00"
                    cpu_optimized_cost: 0.0
                    memory_optimized_cost: 0.0
                    is_projected: string
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/annual-and-missed-savings:
    get:
      tags:
        - Project
      summary: Get project-level card totals
      description: Fetches metrics for card totals such as automated percentage, missed savings, and resource efficiency.
      operationId: get_project_annual_and_missed_savings_organization__org_id__project__proj_id__annual_and_missed_savings_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: b3a47d9c-8e1f-42d6-a08c-e6f2159db453
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
          example: 5f7c91a3-d6b8-47e2-9a3c-8d1fe426b790
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnnualAndMissedSavingsResponse'
              example:
                data:
                  - env_id: c4f87e23-91ab-4d5c-8e67-f213a908b5d6
                    recommended_cost: 0.0
                    annual_savings: 0.0
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/plot:
    get:
      tags:
        - Project
      summary: Get project-level cost analysis plot
      operationId: get_project_plot_organization__org_id__project__proj_id__plot_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: a6c9b2d4-e5f8-47a1-b3c6-d2e9f0a8b7c5
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
          example: d7e1f3a5-c8b9-42d6-e0f2-a3b7c5d9e1f2
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectMetricsResponse'
              example:
                organization_id: a6c9b2d4-e5f8-47a1-b3c6-d2e9f0a8b7c5
                project_id: d7e1f3a5-c8b9-42d6-e0f2-a3b7c5d9e1f2
                total_monthly_cpu_cost: 0.0
                total_monthly_memory_cost: 0.0
                total_monthly_cost: 0.0
                total_last_month_cost: 0.0
                cpu_efficiency_this_month:
                  utilization: 0.0
                  allocation: 0.0
                memory_efficiency_this_month:
                  utilization: 0.0
                  allocation: 0.0
                cpu_efficiency_last_month:
                  utilization: 0.0
                  allocation: 0.0
                memory_efficiency_last_month:
                  utilization: 0.0
                  allocation: 0.0
                estimated_costs_by_env:
                  additionalProp1: 0.0
                  additionalProp2: 0.0
                  additionalProp3: 0.0
                metrics_by_env_comp:
                  - environment_id: e2f4a6b8-c0d2-47e9-b1a3-f5e7d9c1b3a5
                    data:
                      table:
                        - cpu:
                            allocation: 0.0
                            utilization: 0.0
                            cost: 0.0
                          memory:
                            allocation: 0.0
                            utilization: 0.0
                            cost: 0.0
                          total_cost: 0.0
                          saved_cost: 0.0
                          component_id: f8e6d4b2-a0c9-4e7d-b5a3-c1f3e5d7b9a0
                plot:
                  - date: "2025-03-06T00:00:00+00:00"
                    cpu_allocation_cost: 0.0
                    cpu_utilization_cost: 0.0
                    memory_allocation_cost: 0.0
                    memory_utilization_cost: 0.0
                    is_projected: string
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/plot_optimized:
    get:
      tags:
        - Project
      summary: Get project-level cost analysis optimized plot
      operationId: get_project_optimized_plot_organization__org_id__project__proj_id__plot_optimized_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: c7b2a1d8-e3f5-4096-b9c8-a7e5d2f1b3c4
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
          example: a3b9c7d5-e1f3-4872-b6a4-c8d2e0f4a6b8
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectOptimizedPlot'
              example:
                organization_id: c7b2a1d8-e3f5-4096-b9c8-a7e5d2f1b3c4
                project_id: a3b9c7d5-e1f3-4872-b6a4-c8d2e0f4a6b8
                plot:
                  - date: "2025-03-06T00:00:00+00:00"
                    cpu_optimized_cost: 0.0
                    memory_optimized_cost: 0.0
                    is_projected: string
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/component/{release_id}/recommendation-algorithm-configurations:
    get:
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema: {}
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema: {}
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema: {}
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema: {}
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema: {}
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema: {}
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /organization/{org_id}/project/{proj_id}/component/{release_id}/high-availability:
    get:
      tags:
        - Component
      summary: Get high-availability status
      description: Fetches Auto-Optimized Enabled/Disabled status and High Availability Enabled/Disabled.
      operationId: get_component_status_organization__org_id__project__proj_id__component__release_id__high_availability_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Release Id
            type: string
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentStatus'
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - Component
      summary: Update high-availability status
      description: Updates the High Availability Enabled/Disabled status of the component.
      operationId: update_high_availability_status_organization__org_id__project__proj_id__component__release_id__high_availability_post
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Release Id
            type: string
        - name: high_availability
          in: query
          description: High Availability status to be updated
          required: true
          style: form
          explode: true
          schema:
            title: High Availability
            type: boolean
            description: High Availability status to be updated
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/component/{release_id}/recommended-cost:
    get:
      tags:
        - Component
      summary: Get component level missed savings
      description: Fetches missed savings card of component release
      operationId: get_component_missed_savings_organization__org_id__project__proj_id__component__release_id__recommended_cost_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: e4d5c6b7-a8b9-40c1-d2e3-f4a5b6c7d8e9
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
          example: f5e6d7c8-b9a0-41e2-d3f4-e5a6b7c8d9e0
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Release Id
            type: string
          example: a7b8c9d0-e1f2-43a4-b5c6-d7e8f9a0b1c2
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentMissedSavings'
              example:
                recommended_cost_this_month_cpu: 0.0
                recommended_cost_this_month_memory: 0.0
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/component/{release_id}/plots:
    get:
      tags:
        - Component
      summary: Get component level plots
      description: Fetches detailed cost, efficiency, and plot data for a component.
      operationId: get_component_plots_organization__org_id__project__proj_id__component__release_id__plots_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: d8e9f0a1-b2c3-44d5-e6f7-a8b9c0d1e2f3
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
          example: c7d8e9f0-a1b2-43c4-d5e6-f7a8b9c0d1e2
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Release Id
            type: string
          example: b6c7d8e9-f0a1-42b3-c4d5-e6f7a8b9c0d1
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentPlotResponse'
              example:
                organization_id: d8e9f0a1-b2c3-44d5-e6f7-a8b9c0d1e2f3
                project_id: c7d8e9f0-a1b2-43c4-d5e6-f7a8b9c0d1e2
                release_id: b6c7d8e9-f0a1-42b3-c4d5-e6f7a8b9c0d1
                total_monthly_cpu_cost: 0.0
                total_monthly_memory_cost: 0.0
                total_monthly_cost: 0.0
                total_saved_this_month: 0.0
                total_last_month_cost: 0.0
                cpu_efficiency_this_month:
                  utilization: 0.0
                  allocation: 0.0
                memory_efficiency_this_month:
                  utilization: 0.0
                  allocation: 0.0
                cpu_efficiency_last_month:
                  utilization: 0.0
                  allocation: 0.0
                memory_efficiency_last_month:
                  utilization: 0.0
                  allocation: 0.0
                estimated_monthly_cpu_cost: 0.0
                estimated_monthly_memory_cost: 0.0
                estimated_monthly_cost: 0.0
                plot_cpu:
                  - date: "2025-03-06T00:00:00+00:00"
                    allocation: 0.0
                    utilization: 0.0
                    is_projected: string
                plot_memory:
                  - date: "2025-03-06T00:00:00+00:00"
                    allocation: 0.0
                    utilization: 0.0
                    is_projected: string
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/component/{release_id}/plots_optimized:
    get:
      tags:
        - Component
      summary: Get component level optimized plots
      description: Fetches detailed optimized plot data for a component.
      operationId: get_component_plots_optimized_organization__org_id__project__proj_id__component__release_id__plots_optimized_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
          example: 1b3e4567-e89b-12d3-a456-426614174001
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Release Id
            type: string
          example: 9f8e1234-d567-4abc-9012-abcdef123456
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentOptimizedPlot'
              example:
                organization_id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
                project_id: 1b3e4567-e89b-12d3-a456-426614174001
                release_id: 9f8e1234-d567-4abc-9012-abcdef123456
                plot_cpu:
                  - date: "2025-03-04T05:48:38.273Z"
                    optimized_max_pod: 0
                    optimized_all_pods: 0
                    is_projected: string
                plot_memory:
                  - date: "2025-03-04T05:48:38.273Z"
                    optimized_max_pod: 0
                    optimized_all_pods: 0
                    is_projected: string
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/component/{release_id}/max_pod_plots:
    get:
      tags:
        - Component
      summary: Get component level max pod plots
      description: Fetches detailed cost, efficiency, and max pod plot data for a component.
      operationId: get_component_max_pod_plots_organization__org_id__project__proj_id__component__release_id__max_pod_plots_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
          example: 1b3e4567-e89b-12d3-a456-426614174001
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Release Id
            type: string
          example: 9f8e1234-d567-4abc-9012-abcdef123456
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentPlotResponseMaxPod'
              example:
                organization_id: 3fa85f64-5717-4562-b3fc-2c963f66afa6
                project_id: 1b3e4567-e89b-12d3-a456-426614174001
                release_id: 9f8e1234-d567-4abc-9012-abcdef123456
                plot_cpu:
                  - date: "2025-03-04T05:53:06.058Z"
                    allocation: 0
                    utilization: 0
                    is_projected: string
                plot_memory:
                  - date: "2025-03-04T05:53:06.058Z"
                    allocation: 0
                    utilization: 0
                    is_projected: string
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: string
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /organization/{org_id}/project/{proj_id}/component/{release_id}/recommendations:
    get:
      tags:
        - Component
      summary: Get component level recommendations
      description: Fetches resource right sizing, enable s2z, and stop deployment recommendations.
      operationId: get_component_recommendations_organization__org_id__project__proj_id__component__release_id__recommendations_get
      parameters:
        - name: org_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Org Id
            type: string
          example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        - name: proj_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Proj Id
            type: string
          example: 1b3e4567-e89b-12d3-a456-426614174001
        - name: release_id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            title: Release Id
            type: string
          example: 9f8e1234-d567-4abc-9012-abcdef123456
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentRecommendations'
              example:
                resource_right_size_savings:
                  apply: true
                  save_upto: 100
                  cpu:
                    recommended_limit: 500
                    recommended_request: 250
                    savings: 50
                  memory:
                    recommended_limit: 1024
                    recommended_request: 512
                    savings: 100
                enable_s2z:
                  apply: true
                  save_upto: 200
                stop_deployment:
                  apply: true
                  save_upto: 300
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
              example:
                detail:
                  - loc:
                      - string
                      - 0
                    msg: Invalid input
                    type: value_error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    AnnualAndMissedSavingsByEnv:
      title: AnnualAndMissedSavingsByEnv
      required:
        - annual_savings
        - env_id
        - recommended_cost
      type: object
      properties:
        env_id:
          title: Env Id
          type: string
        recommended_cost:
          title: Recommended Cost
          type: number
        annual_savings:
          title: Annual Savings
          type: number
    AnnualAndMissedSavingsResponse:
      title: AnnualAndMissedSavingsResponse
      required:
        - data
      type: object
      properties:
        data:
          title: Data
          type: array
          items:
            $ref: '#/components/schemas/AnnualAndMissedSavingsByEnv'
    ComponentMetricsTableEntry:
      title: ComponentMetricsTableEntry
      required:
        - component_id
        - cpu
        - memory
        - saved_cost
        - total_cost
      type: object
      properties:
        cpu:
          $ref: '#/components/schemas/ResourceMetrics'
        memory:
          $ref: '#/components/schemas/ResourceMetrics'
        total_cost:
          title: Total Cost
          minimum: 0
          type: number
        saved_cost:
          title: Saved Cost
          type: number
        component_id:
          title: Component Id
          type: string
    ComponentMissedSavings:
      title: ComponentMissedSavings
      required:
        - recommended_cost_this_month_cpu
        - recommended_cost_this_month_memory
      type: object
      properties:
        recommended_cost_this_month_cpu:
          title: Recommended Cost This Month Cpu
          minimum: 0
          type: number
        recommended_cost_this_month_memory:
          title: Recommended Cost This Month Memory
          minimum: 0
          type: number
    ComponentOptimizedPlot:
      title: ComponentOptimizedPlot
      required:
        - organization_id
        - plot_cpu
        - plot_memory
        - project_id
        - release_id
      type: object
      properties:
        organization_id:
          title: Organization Id
          type: string
        project_id:
          title: Project Id
          type: string
        release_id:
          title: Release Id
          type: string
        plot_cpu:
          title: Plot Cpu
          type: array
          items:
            $ref: '#/components/schemas/PlotDataOptimized'
        plot_memory:
          title: Plot Memory
          type: array
          items:
            $ref: '#/components/schemas/PlotDataOptimized'
    ComponentPlotResponse:
      title: ComponentPlotResponse
      required:
        - cpu_efficiency_last_month
        - cpu_efficiency_this_month
        - estimated_monthly_cost
        - estimated_monthly_cpu_cost
        - estimated_monthly_memory_cost
        - memory_efficiency_last_month
        - memory_efficiency_this_month
        - missed_savings_last_month
        - organization_id
        - plot_cpu
        - plot_memory
        - project_id
        - release_id
        - total_last_month_cost
        - total_monthly_cost
        - total_monthly_cpu_cost
        - total_monthly_memory_cost
        - total_saved_this_month
      type: object
      properties:
        organization_id:
          title: Organization Id
          type: string
        project_id:
          title: Project Id
          type: string
        release_id:
          title: Release Id
          type: string
        total_monthly_cpu_cost:
          title: Total Monthly Cpu Cost
          minimum: 0
          type: number
        total_monthly_memory_cost:
          title: Total Monthly Memory Cost
          minimum: 0
          type: number
        total_monthly_cost:
          title: Total Monthly Cost
          minimum: 0
          type: number
        total_saved_this_month:
          title: Total Saved This Month
          type: number
        total_last_month_cost:
          title: Total Last Month Cost
          minimum: 0
          type: number
        cpu_efficiency_this_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        memory_efficiency_this_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        cpu_efficiency_last_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        memory_efficiency_last_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        missed_savings_last_month:
          title: Missed Savings Last Month
          type: number
        estimated_monthly_cpu_cost:
          title: Estimated Monthly Cpu Cost
          minimum: 0
          type: number
        estimated_monthly_memory_cost:
          title: Estimated Monthly Memory Cost
          minimum: 0
          type: number
        estimated_monthly_cost:
          title: Estimated Monthly Cost
          minimum: 0
          type: number
        plot_cpu:
          title: Plot Cpu
          type: array
          items:
            $ref: '#/components/schemas/PlotData'
        plot_memory:
          title: Plot Memory
          type: array
          items:
            $ref: '#/components/schemas/PlotData'
    ComponentPlotResponseMaxPod:
      title: ComponentPlotResponseMaxPod
      required:
        - organization_id
        - plot_cpu
        - plot_memory
        - project_id
        - release_id
      type: object
      properties:
        organization_id:
          title: Organization Id
          type: string
        project_id:
          title: Project Id
          type: string
        release_id:
          title: Release Id
          type: string
        plot_cpu:
          title: Plot Cpu
          type: array
          items:
            $ref: '#/components/schemas/PlotData'
        plot_memory:
          title: Plot Memory
          type: array
          items:
            $ref: '#/components/schemas/PlotData'
    ComponentRecommendations:
      title: ComponentRecommendations
      required:
        - enable_s2z
        - resource_right_size_savings
        - stop_deployment
      type: object
      properties:
        resource_right_size_savings:
          $ref: '#/components/schemas/ResourceRightSizeSavings'
        enable_s2z:
          $ref: '#/components/schemas/OptimizationOption'
        stop_deployment:
          $ref: '#/components/schemas/OptimizationOption'
    ComponentStatus:
      title: ComponentStatus
      required:
        - high_availability
      type: object
      properties:
        high_availability:
          title: High Availability
          type: boolean
    DateRangeRequest:
      title: DateRangeRequest
      required:
        - dates
      type: object
      properties:
        dates:
          title: Dates
          type: array
          description: List of dates in ISO format (YYYY-MM-DD)
          items:
            type: string
    DeployRecommendation:
      title: DeployRecommendation
      required:
        - apply
        - save_upto
      type: object
      properties:
        apply:
          title: Apply
          type: boolean
        save_upto:
          title: Save Upto
          type: number
    EfficiencyMetrics:
      title: EfficiencyMetrics
      required:
        - allocation
        - utilization
      type: object
      properties:
        utilization:
          title: Utilization
          minimum: 0
          type: number
        allocation:
          title: Allocation
          minimum: 0
          type: number
    EnvironmentDataOrgLevel:
      title: EnvironmentDataOrgLevel
      required:
        - table
      type: object
      properties:
        table:
          title: Table
          type: array
          items:
            $ref: '#/components/schemas/ProjectMetricsTableEntry'
    EnvironmentDataProjLevel:
      title: EnvironmentDataProjLevel
      required:
        - table
      type: object
      properties:
        table:
          title: Table
          type: array
          items:
            $ref: '#/components/schemas/ComponentMetricsTableEntry'
    EnvironmentMetricsOrgLevel:
      title: EnvironmentMetricsOrgLevel
      required:
        - data
        - environment_id
      type: object
      properties:
        environment_id:
          title: Environment Id
          type: string
        data:
          $ref: '#/components/schemas/EnvironmentDataOrgLevel'
    EnvironmentMetricsProjLevel:
      title: EnvironmentMetricsProjLevel
      required:
        - data
        - environment_id
      type: object
      properties:
        environment_id:
          title: Environment Id
          type: string
        data:
          $ref: '#/components/schemas/EnvironmentDataProjLevel'
    HTTPValidationError:
      title: HTTPValidationError
      type: object
      properties:
        detail:
          title: Detail
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
    MetricConfig:
      title: MetricConfig
      type: object
      properties:
        percentile:
          title: Percentile
          maximum: 1
          minimum: 0.9
          type: number
          description: Percentile to use when sampling historical usage (90–100 %).
        buffer:
          title: Buffer
          maximum: 0.4
          minimum: 0
          type: number
          description: Safety buffer added on top of the recommended request (0.0–0.40).
        limit_scale:
          title: Limit Scale
          maximum: 2.5
          minimum: 1.5
          type: number
          description: Factor by which the recommended *limit* is scaled above the request (1.5–2.5).
      description: Shared configuration for either CPU or memory resources.
    OptimizationOption:
      title: OptimizationOption
      required:
        - apply
        - save_upto
      type: object
      properties:
        apply:
          title: Apply
          type: boolean
        save_upto:
          title: Save Upto
          type: number
    OrganizationMetricsResponse:
      title: OrganizationMetricsResponse
      required:
        - cpu_efficiency_last_month
        - cpu_efficiency_this_month
        - estimated_costs_by_env
        - memory_efficiency_last_month
        - memory_efficiency_this_month
        - metrics_by_env_proj
        - missed_savings_last_month
        - organization_id
        - plot
        - total_last_month_cost
        - total_monthly_cost
        - total_monthly_cpu_cost
        - total_monthly_memory_cost
      type: object
      properties:
        organization_id:
          title: Organization Id
          type: string
        total_monthly_cpu_cost:
          title: Total Monthly Cpu Cost
          minimum: 0
          type: number
        total_monthly_memory_cost:
          title: Total Monthly Memory Cost
          minimum: 0
          type: number
        total_monthly_cost:
          title: Total Monthly Cost
          minimum: 0
          type: number
        total_last_month_cost:
          title: Total Last Month Cost
          minimum: 0
          type: number
        cpu_efficiency_this_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        memory_efficiency_this_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        cpu_efficiency_last_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        memory_efficiency_last_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        missed_savings_last_month:
          title: Missed Savings Last Month
          type: number
        estimated_costs_by_env:
          title: Estimated Costs By Env
          type: object
          additionalProperties:
            type: number
        metrics_by_env_proj:
          title: Metrics By Env Proj
          type: array
          items:
            $ref: '#/components/schemas/EnvironmentMetricsOrgLevel'
        plot:
          title: Plot
          type: array
          items:
            $ref: '#/components/schemas/PlotDataPoint'
    OrganizationOptimizedPlot:
      title: OrganizationOptimizedPlot
      required:
        - organization_id
        - plot
      type: object
      properties:
        organization_id:
          title: Organization Id
          type: string
        plot:
          title: Plot
          type: array
          items:
            $ref: '#/components/schemas/PlotDataPointOptimized'
    PaginatedResponse:
      title: PaginatedResponse
      required:
        - data
        - items_per_page
        - page
        - total_items
        - total_pages
      type: object
      properties:
        page:
          title: Page
          type: integer
        total_pages:
          title: Total Pages
          type: integer
        total_items:
          title: Total Items
          type: integer
        items_per_page:
          title: Items Per Page
          type: integer
        data:
          title: Data
          type: array
          items:
            $ref: '#/components/schemas/RecommendationData'
    PlotData:
      title: PlotData
      required:
        - date
        - is_projected
      type: object
      properties:
        date:
          title: Date
          type: string
          format: date-time
        allocation:
          title: Allocation
          type: number
        utilization:
          title: Utilization
          type: number
        cpu_allocation_cost:
          title: Cpu Allocation Cost
          type: number
        cpu_utilization_cost:
          title: Cpu Utilization Cost
          type: number
        memory_allocation_cost:
          title: Memory Allocation Cost
          type: number
        memory_utilization_cost:
          title: Memory Utilization Cost
          type: number
        is_projected:
          title: Is Projected
          type: string
    PlotDataOptimized:
      title: PlotDataOptimized
      required:
        - date
        - is_projected
      type: object
      properties:
        date:
          title: Date
          type: string
          format: date-time
        optimized_max_pod:
          title: Optimized Max Pod
          type: number
        optimized_all_pods:
          title: Optimized All Pods
          type: number
        optimized:
          title: Optimized
          type: number
        cpu_optimized_cost:
          title: Cpu Optimized Cost
          type: number
        memory_optimized_cost:
          title: Memory Optimized Cost
          type: number
        is_projected:
          title: Is Projected
          type: string
    PlotDataPoint:
      title: PlotDataPoint
      required:
        - cpu_allocation_cost
        - cpu_utilization_cost
        - date
        - is_projected
        - memory_allocation_cost
        - memory_utilization_cost
      type: object
      properties:
        date:
          title: Date
          type: string
          format: date-time
        cpu_allocation_cost:
          title: Cpu Allocation Cost
          minimum: 0
          type: number
        cpu_utilization_cost:
          title: Cpu Utilization Cost
          minimum: 0
          type: number
        memory_allocation_cost:
          title: Memory Allocation Cost
          minimum: 0
          type: number
        memory_utilization_cost:
          title: Memory Utilization Cost
          minimum: 0
          type: number
        is_projected:
          title: Is Projected
          type: string
    PlotDataPointOptimized:
      title: PlotDataPointOptimized
      required:
        - cpu_optimized_cost
        - date
        - is_projected
        - memory_optimized_cost
      type: object
      properties:
        date:
          title: Date
          type: string
          format: date-time
        cpu_optimized_cost:
          title: Cpu Optimized Cost
          minimum: 0
          type: number
        memory_optimized_cost:
          title: Memory Optimized Cost
          minimum: 0
          type: number
        is_projected:
          title: Is Projected
          type: string
    ProjectMetricsResponse:
      title: ProjectMetricsResponse
      required:
        - cpu_efficiency_last_month
        - cpu_efficiency_this_month
        - estimated_costs_by_env
        - memory_efficiency_last_month
        - memory_efficiency_this_month
        - metrics_by_env_comp
        - missed_savings_last_month
        - organization_id
        - plot
        - project_id
        - total_last_month_cost
        - total_monthly_cost
        - total_monthly_cpu_cost
        - total_monthly_memory_cost
      type: object
      properties:
        organization_id:
          title: Organization Id
          type: string
        project_id:
          title: Project Id
          type: string
        total_monthly_cpu_cost:
          title: Total Monthly Cpu Cost
          minimum: 0
          type: number
        total_monthly_memory_cost:
          title: Total Monthly Memory Cost
          minimum: 0
          type: number
        total_monthly_cost:
          title: Total Monthly Cost
          minimum: 0
          type: number
        total_last_month_cost:
          title: Total Last Month Cost
          minimum: 0
          type: number
        cpu_efficiency_this_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        memory_efficiency_this_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        cpu_efficiency_last_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        memory_efficiency_last_month:
          $ref: '#/components/schemas/EfficiencyMetrics'
        missed_savings_last_month:
          title: Missed Savings Last Month
          type: number
        estimated_costs_by_env:
          title: Estimated Costs By Env
          type: object
          additionalProperties:
            type: number
        metrics_by_env_comp:
          title: Metrics By Env Comp
          type: array
          items:
            $ref: '#/components/schemas/EnvironmentMetricsProjLevel'
        plot:
          title: Plot
          type: array
          items:
            $ref: '#/components/schemas/PlotDataPoint'
    ProjectMetricsTableEntry:
      title: ProjectMetricsTableEntry
      required:
        - cpu
        - memory
        - project_id
        - saved_cost
        - total_cost
      type: object
      properties:
        cpu:
          $ref: '#/components/schemas/ResourceMetrics'
        memory:
          $ref: '#/components/schemas/ResourceMetrics'
        total_cost:
          title: Total Cost
          minimum: 0
          type: number
        saved_cost:
          title: Saved Cost
          type: number
        project_id:
          title: Project Id
          type: string
    ProjectOptimizedPlot:
      title: ProjectOptimizedPlot
      required:
        - organization_id
        - plot
        - project_id
      type: object
      properties:
        organization_id:
          title: Organization Id
          type: string
        project_id:
          title: Project Id
          type: string
        plot:
          title: Plot
          type: array
          items:
            $ref: '#/components/schemas/PlotDataPointOptimized'
    RecAlgoConfigs:
      title: RecAlgoConfigs
      type: object
      properties:
        window:
          title: Window
          maximum: 7
          minimum: 1
          type: integer
          description: Number of days of data to consider (1–7).
        cpu:
          $ref: '#/components/schemas/MetricConfig'
        memory:
          $ref: '#/components/schemas/MetricConfig'
      description: |-
        Top-level configuration object produced / accepted by the recommendation algorithm.

        Example JSON produced by `.model_dump()` (Pydantic v2) or `.dict()` (v1):

        {
            "window": 3,
            "cpu": {
                "percentile": 0.95,
                "buffer": 0.10,
                "limit_scale": 2.0
            },
            "memory": {
                "percentile": 0.97,
                "buffer": 0.20,
                "limit_scale": 2.0
            }
        }
    Recommendation:
      title: Recommendation
      required:
        - apply
        - cpu
        - memory
        - save_upto
      type: object
      properties:
        apply:
          title: Apply
          type: boolean
        save_upto:
          title: Save Upto
          type: number
        cpu:
          $ref: '#/components/schemas/ResourceRecommendations'
        memory:
          $ref: '#/components/schemas/ResourceRecommendations'
    RecommendationData:
      title: RecommendationData
      required:
        - date
        - enable_s2z
        - environment_id
        - org_id
        - project_id
        - release_id
        - resource_right_size_savings
        - stop_deployment
      type: object
      properties:
        date:
          title: Date
          type: string
          format: date-time
        org_id:
          title: Org Id
          type: string
        project_id:
          title: Project Id
          type: string
        component_id:
          title: Component Id
          type: string
          default: ""
        release_id:
          title: Release Id
          type: string
        environment_id:
          title: Environment Id
          type: string
        resource_right_size_savings:
          $ref: '#/components/schemas/Recommendation'
        enable_s2z:
          $ref: '#/components/schemas/DeployRecommendation'
        stop_deployment:
          $ref: '#/components/schemas/DeployRecommendation'
    ResourceMetrics:
      title: ResourceMetrics
      required:
        - allocation
        - cost
        - utilization
      type: object
      properties:
        allocation:
          title: Allocation
          minimum: 0
          type: number
        utilization:
          title: Utilization
          minimum: 0
          type: number
        cost:
          title: Cost
          minimum: 0
          type: number
    ResourceRecommendation:
      title: ResourceRecommendation
      required:
        - recommended_limit
        - recommended_request
        - savings
      type: object
      properties:
        recommended_limit:
          title: Recommended Limit
          minimum: 0
          type: integer
        recommended_request:
          title: Recommended Request
          minimum: 0
          type: integer
        savings:
          title: Savings
          type: number
    ResourceRecommendations:
      title: ResourceRecommendations
      required:
        - recommended_limit
        - recommended_request
        - savings
      type: object
      properties:
        recommended_limit:
          title: Recommended Limit
          type: number
        recommended_request:
          title: Recommended Request
          type: number
        savings:
          title: Savings
          type: number
    ResourceRightSizeSavings:
      title: ResourceRightSizeSavings
      required:
        - apply
        - cpu
        - memory
        - save_upto
      type: object
      properties:
        apply:
          title: Apply
          type: boolean
        save_upto:
          title: Save Upto
          minimum: 0
          type: number
        cpu:
          $ref: '#/components/schemas/ResourceRecommendation'
        memory:
          $ref: '#/components/schemas/ResourceRecommendation'
    ValidationError:
      title: ValidationError
      required:
        - loc
        - msg
        - type
      type: object
      properties:
        loc:
          title: Location
          type: array
          items:
            anyOf:
              - type: string
              - type: integer
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - x-request-id
    - correlation-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://cost-optimizer.choreo-ai:8000/
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://cost-optimizer.choreo-ai:8000/
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/cost-optimizer/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
