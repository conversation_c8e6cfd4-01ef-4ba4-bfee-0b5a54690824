openapi: 3.1.0
info:
  title: Choreo Analytics Query API
  description: GraphQL API for querying analytics data from Choreo applications
  contact: {}
  version: 0.1.0
servers:
  - url: http://insights-query-api.choreo-observability.svc.cluster.local:8080/systemapis/analyticsqueryapi/0.1.0
    description: Default Server URL
security:
  - default: []
paths:
  /query:
    post:
      summary: GraphQL Query Endpoint
      description: Standard GraphQL endpoint that accepts queries and mutations
      operationId: graphqlQuery
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GraphQLRequest'
        required: true
      responses:
        "200":
          description: Successful GraphQL response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GraphQLResponse'
        "400":
          description: Bad request
        "401":
          description: Unauthorized
        "500":
          description: Internal server error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /status:
    post:
      summary: Transform Job Status Endpoint
      description: Endpoint to retrieve the status of a transform job
      operationId: transformJobStatus
      requestBody:
        content:
          application/json:
            schema: {}
        required: true
      responses:
        "200":
          description: Successful response with transform job details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatusResponse'
        "400":
          description: Bad request
        "401":
          description: Unauthorized
        "500":
          description: Internal server error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    StatusResponse:
      properties:
        metadata_id:
          description: Metadata ID of the transform job
        transform_metadata:
          properties:
            transform_id:
              description: ID of the transform job
            last_updated_at:
              description: Last updated timestamp
              format: int64
            status:
              description: Status of the transform job
            failure_reason:
              description: Reason for failure, if any
              nullable: true
            stats:
              properties:
                pages_processed: {}
                documents_processed: {}
                documents_indexed: {}
                index_time_in_millis: {}
                search_time_in_millis: {}
            continuous_stats:
              properties:
                last_timestamp:
                  format: int64
                documents_behind:
                  additionalProperties: {}
      example:
        metadata_id: "12345"
        transform_metadata:
          transform_id: "67890"
          last_updated_at: 1683072000
          status: COMPLETED
          failure_reason: null
          stats:
            pages_processed: 100
            documents_processed: 500
            documents_indexed: 500
            index_time_in_millis: 1200
            search_time_in_millis: 300
          continuous_stats:
            last_timestamp: 1683072100
            documents_behind: 0
    GraphQLRequest:
      required:
        - query
      properties:
        query:
          description: GraphQL query string
        variables:
          additionalProperties: {}
          description: Query variables
          nullable: true
        operationName:
          description: Name of the operation
          nullable: true
    GraphQLResponse:
      properties:
        data:
          additionalProperties: {}
          description: Response data
          nullable: true
        errors:
          items:
            properties:
              message: {}
              locations:
                items:
                  properties:
                    line: {}
                    column: {}
              path:
                items: {}
            additionalProperties: {}
          nullable: true
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - Authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - POST
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://insights-query-api.choreo-observability.svc.cluster.local:8080
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://insights-query-api.choreo-observability.svc.cluster.local:8080
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/analyticsqueryapi/0.1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
