{"id": "64bfa4eb012b9122ece541b1", "name": "Choreo Logging API", "displayName": "Choreo Logging API", "description": "API for retrieving logs of choreo user applications.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreologgingapi", "version": "0.2.0", "provider": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": [], "maxTps": null, "visibility": "PUBLIC", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1690281195822", "lastUpdatedTime": "2025-06-20 07:16:06.15", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}, "production_endpoints": {"config": {"actionDuration": "300000"}, "url": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": "urn:choreosystem:choreologgingapi:", "operations": [{"id": "", "target": "/logs/component/build", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/component/build", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/component/buildV2", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/component/buildV2", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/automation-pipelines/{pipelineId}/runs/{runId}", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/automation-pipelines/{pipelineId}/runs/{runId}", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/component/application", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/component/application", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/component/application/live", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/component/application/live", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/component/application/grouped", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/component/application/grouped", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/component/zipped", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/component/zipped", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/component/gateway", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/component/gateway", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/component/gateway/live", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/component/gateway/live", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/project/application", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/project/application", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/project/application/live", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/project/application/live", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/logs/org/application", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/logs/org/application", "verb": "POST", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/executions", "verb": "GET", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/executions", "verb": "GET", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/executions/{executionId}/logs", "verb": "GET", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/executions/{executionId}/logs", "verb": "GET", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}, {"id": "", "target": "/healthz", "verb": "GET", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "9e4ef366-9aaf-4d16-9ff0-6f9f2970f5b7", "backendOperation": {"target": "/healthz", "verb": "GET", "endpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": "http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi", "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": null, "componentId": null, "versionId": null}}