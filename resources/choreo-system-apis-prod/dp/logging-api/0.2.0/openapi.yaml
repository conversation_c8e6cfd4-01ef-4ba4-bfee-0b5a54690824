openapi: 3.0.3
info:
  title: Choreo Logging API
  description: API for retrieving logs of choreo user applications.
  contact: {}
  version: 0.2.0
servers:
  - url: https://{hostname}/systemapis/choreologgingapi/0.2.0
    variables:
      hostname:
        default: app.choreo.dev
security:
  - default: []
paths:
  /logs/component/build:
    post:
      summary: Retrieve build logs for the workflows
      description: |
        Retrieve build logs for the workflows stored in Choreo Cloud.
      operationId: getBuildLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BuildLogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulBuildLogs'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /logs/component/buildV2:
    post:
      summary: Retrieve build logs for the workflows
      description: |
        Retrieve build logs for the workflows stored in Choreo Cloud.
      operationId: getBuildLogsV2
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BuildLogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulBuildLogsV2'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /logs/automation-pipelines/{pipelineId}/runs/{runId}:
    post:
      summary: Retrieve logs for an automation pipeline run
      description: |
        Retrieve logs for an automation pipeline run executed in Choreo Cloud.
      operationId: getPipelineRunLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
        - $ref: '#/components/parameters/pipelineId'
        - $ref: '#/components/parameters/runId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutomationPipelineLogsRequest'
            examples:
              ExampleRequest:
                summary: Example pipeline run logs request
                value:
                  sort: asc
                  limit: 100
                  startTime: "2024-05-01T00:00:00Z"
                  endTime: "2024-05-01T01:30:00Z"
                  searchPhrase: error
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulPipelineLogs'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /logs/component/application:
    post:
      summary: Retrieve historical application logs for the component
      description: |
        Retrieve historical application logs for the component stored in Choreo Cloud.
      operationId: getApplicationLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulLogsTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /logs/component/application/live:
    post:
      summary: Retrieve live application logs for the component
      description: |
        Retrieve live application logs for the component. This can only retrieve data for the last
        1 to 2 hours
      operationId: getApplicationLiveLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulLogsTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
        "501":
          description: Response indicating that live logs feature has not been implemented (enabled)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /logs/component/application/grouped:
    post:
      summary: Retrieve historical application logs for the component grouped into time bins
      description: "Retrieve historical application logs for the component stored in Choreo Cloud grouped \ninto a set of time bins\n"
      operationId: getApplicationGroupedLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupedLogsRequest'
        required: true
      responses:
        "200":
          description: |
            Successful response containing the grouped logs. The columns of the table are:
            * TimeGenerated - The timestamp of the log line
            * Logs - The group of logs for the time bin as a JSON array of objects.
              The properties of the object are:
              * LogLevel - The level of the log (one of ["ERROR","INFO","WARN","DEBUG"])
              * LogEntry - The log message
              * Count - The number of times the log entry was found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LogsTable'
              examples:
                LogsTable:
                  value:
                    columns:
                      - name: TimeGenerated
                        type: datetime
                      - name: Logs
                        type: dynamic
                    rows:
                      - - "2021-09-01T20:30:00Z"
                        - '[{"LogLevel":"ERROR","LogEntry":"error while connecting to the hr-service","Count":2},{"LogLevel":"INFO","LogEntry":"ballerina: initializing connection with observability backend periscope.choreo.dev:443","Count":1},{"LogLevel":"INFO","LogEntry":"ballerina: visit http://console.choreo.dev/observe/app/117ed545-9158-11eb-8af4-bb5c98e5b4d6/e4057bdc-0b64-11ec-bd7d-ee59efa67041 to access observability data","Count":1},{"LogLevel":"INFO","LogEntry":"ballerina: started publishing metrics to Choreo","Count":1},{"LogLevel":"INFO","LogEntry":"[ballerina/http] started HTTP/WS listener 0.0.0.0:8090","Count":1}]'
                      - - "2021-09-01T20:48:00Z"
                        - '[{"LogLevel":"ERROR","LogEntry":"error while connecting to the hr-service","Count":2646}]'
                      - - "2021-09-01T21:06:00Z"
                        - '[{"LogLevel":"ERROR","LogEntry":"error while connecting to the hr-service","Count":2943}]'
                      - - "2021-09-01T21:24:00Z"
                        - '[{"LogLevel":"ERROR","LogEntry":"error while connecting to the hr-service","Count":1},{"LogLevel":"WARN","LogEntry":"employee information not found in the hr-service","Count":2940}]'
                      - - "2021-09-01T21:42:00Z"
                        - null
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
                InvalidBin:
                  description: |
                    The provided bin query parameter is invalid. Only valid
                    integer values are supported. If you are trying to send
                    a decimal value, consider using 'binUnit' to specify a
                    smaller unit.
                  value:
                    code: OBS-L-12
                    message: '''foo'' is not supported as a bin'
                InvalidBinUnit:
                  description: |
                    The provided bin unit query parameter is invalid.
                  value:
                    code: OBS-L-12
                    message: '''foo'' is not supported as a bin unit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /logs/component/zipped:
    post:
      summary: Retrieve historical logs for the component as a zip file
      description: |
        Retrieve historical stored logs as a zip file. This endpoint supports
        higher log limits (600000 log lines) and the limit will be automatically applied.
        Therefore this should be used for exporting large amounts of logs.
        Logs are by default sorted in ascending and changing this is not supported.
      operationId: getZippedLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ZippedLogsRequest'
        required: true
      responses:
        "200":
          description: Successful response containing a binary payload of a zip file.
          headers:
            X-Limit-Reached:
              description: |
                Header indicating whether a limit was applied to the logs contained in the zip file.
                If the header is not provided, the consumer should assume that the value is '0'.
                * 0 - Limit not applied
                * 1 - Limit applied
              style: simple
              explode: false
              schema:
                type: integer
                enum:
                  - 0
                  - 1
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidTimeZoneOffset:
                  $ref: '#/components/examples/InvalidTimeZoneOffset'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          description: Response indicating an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                FailedToRetrieveMetrics:
                  $ref: '#/components/examples/FailedToRetrieveMetrics'
                FailedToCreateZip:
                  description: |
                    Failed to create a zip file from the logs. Please contact the
                    developers if you require more information about the error.
                  value:
                    code: OBS-L-22
                    message: Failed to create zip entry
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /logs/component/gateway:
    post:
      summary: Retrieve historical gateway access logs for the component
      description: |
        Retrieve historical gateway access logs for the component stored in Choreo Cloud.
      operationId: getGatewayLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GatewayLogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulLogsTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /logs/component/gateway/live:
    post:
      summary: Retrieve live gateway logs for the component
      description: |
        Retrieve live gateway logs for the component from OpenSearch.
      operationId: getLiveGatewayLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GatewayLogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulLogsTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /logs/project/application:
    post:
      summary: Retrieve historical application logs for all the components in the project
      description: |
        Retrieve historical application logs stored in Choreo Cloud for all the components in the project
      operationId: getProjectApplicationLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectLogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulProjectLogsTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /logs/project/application/live:
    post:
      summary: Retrieve live application logs for all components in the project
      description: |
        Retrieve live application logs for all components in the project. This can only retrieve data
        for the last 1 to 2 hours
      operationId: getProjectApplicationLiveLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectLogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulProjectLogsTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
        "501":
          description: Response indicating that live logs feature has not been implemented (enabled)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /logs/org/application:
    post:
      summary: Retrieve historical application logs for all the components in the organization
      description: |
        Retrieve historical application logs stored in Choreo Cloud for all the components in the organization
      operationId: getOrgApplicationLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrgLogsRequest'
        required: true
      responses:
        "200":
          $ref: '#/components/responses/SuccessfulLogsTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  $ref: '#/components/examples/TimeRangeNotProvided'
                InvalidTimeRange:
                  $ref: '#/components/examples/InvalidTimeRange'
                InvalidSearchPhrase:
                  $ref: '#/components/examples/InvalidSearchPhrase'
                InvalidLogLevels:
                  $ref: '#/components/examples/InvalidLogLevels'
                InvalidLimit:
                  $ref: '#/components/examples/InvalidLimit'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/executions:
    get:
      summary: Retrieve execution list of a Task component
      description: |
        Retrieve list of executions of a task component
      operationId: getTaskExecutions
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/environmentId'
        - $ref: '#/components/parameters/deploymentTrackId'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/limit'
      responses:
        "200":
          $ref: '#/components/responses/SuccessExecutionsTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/executions/{executionId}/logs:
    get:
      summary: Retrieve execution list of a Task component
      description: |
        Retrieve list of executions of a task component
      operationId: getExecutionLogs
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/authHeader'
        - $ref: '#/components/parameters/environmentId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/deploymentTrackId'
        - $ref: '#/components/parameters/executionId'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/limit'
      responses:
        "200":
          $ref: '#/components/responses/SuccessExecutionLogTable'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalStoreError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /healthz:
    get:
      summary: Retrieve health of the loggingapi
      description: |
        Retrieve the status of the API. This may not reflect exactly the current state of the API.
      operationId: checkHealth
      responses:
        "200":
          description: Response indicating an ok status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthOkResponse'
        "503":
          description: Response indicating a service unavailable status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
components:
  schemas:
    AutomationPipelineLogsRequest:
      type: object
      properties:
        sort:
          type: string
          description: |
            Indicate whether the logs should be sorted by timestamp in ascending (asc)
            or descending (desc) order. If none or invalid value is provided,
            the logs will be sorted by ascending order.
          enum:
            - asc
            - desc
          default: asc
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched. If the provided limit exceeds the
            supported maximum logs limit, the maximum logs limit will be used.
          format: int64
          default: 1000
        startTime:
          type: string
          description: Start time for the logs as a ISO 8601 string.
          format: date-time
        endTime:
          type: string
          description: End time for the logs as a ISO 8601 string.
          format: date-time
        searchPhrase:
          type: string
          description: |
            The log phrase for filter logs by. If an empty string is provided,
            all the logs will be returned without filtering.
          default: ""
    BuildLogsRequest:
      required:
        - componentId
        - deploymentTrackId
        - workflowName
      type: object
      properties:
        startTime:
          type: string
          description: Start time for the logs as a ISO 8601 string.
          format: date-time
        endTime:
          type: string
          description: End time for the logs as a ISO 8601 string.
          format: date-time
        componentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Component id of the user application (specified in pod labels).
          format: uuid
        deploymentTrackId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Deployment track id of the user application.
          format: uuid
        workflowName:
          type: string
          description: Name of the workflow.
        namespace:
          type: string
          description: Namespace of the user application.
        searchPhrase:
          type: string
          description: |
            The log phrase for filter logs by. If an empty string is provided,
            all the logs will be returned without filtering.
          default: ""
        sort:
          type: string
          description: |
            Indicate whether the logs should be sorted by timestamp in ascending (asc)
            or descending (desc) order. If none or invalid value is provided,
            the logs will be sorted by ascending order.
          enum:
            - asc
            - desc
          default: asc
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched. If the provided limit exceeds the
            supported maximum logs limit, the maximum logs limit will be used.
          format: int64
          default: 100
        traceId:
          type: string
          description: Trace id of the request if the logs should be filtered for a specific request.
    LogsRequest:
      required:
        - componentId
        - endTime
        - environmentId
        - startTime
      type: object
      properties:
        startTime:
          type: string
          description: Start time for the logs as a ISO 8601 string.
          format: date-time
        endTime:
          type: string
          description: End time for the logs as a ISO 8601 string.
          format: date-time
        componentId:
          pattern: ^(([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})|(sample_app_component_id))$
          type: string
          description: Component id of the user application (specified in pod labels).
          format: uuid
        environmentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Environment id of the user application (specified in pod labels).
          format: uuid
        versionList:
          type: array
          description: |
            List of component versions to filter by. All the versions are returned if not provided.
          items:
            type: string
        logLevels:
          type: array
          description: |
            list of log levels to filter by. All the log levels are returned if not provided.
          items:
            type: string
            enum:
              - ERROR
              - WARN
              - INFO
              - DEBUG
        searchPhrase:
          type: string
          description: |
            The log phrase for filter logs by. If an empty string is provided,
            all the logs will be returned without filtering.
          default: ""
        sort:
          type: string
          description: |
            Indicate whether the logs should be sorted by timestamp in ascending (asc)
            or descending (desc) order. If none or invalid value is provided,
            the logs will be sorted by ascending order.
          enum:
            - asc
            - desc
          default: asc
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched. If the provided limit exceeds the
            supported maximum logs limit, the maximum logs limit will be used.
          format: int64
          default: 100
        traceId:
          type: string
          description: Trace id of the request if the logs should be filtered for a specific request.
    GroupedLogsRequest:
      required:
        - componentId
        - endTime
        - environmentId
        - startTime
      type: object
      properties:
        startTime:
          type: string
          description: Start time for the logs as a ISO 8601 string.
          format: date-time
        endTime:
          type: string
          description: End time for the logs as a ISO 8601 string.
          format: date-time
        componentId:
          pattern: ^(([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})|(sample_app_component_id))$
          type: string
          description: Component id of the user application (specified in pod labels).
          format: uuid
        environmentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Environment id of the user application (specified in pod labels).
          format: uuid
        logLevels:
          type: array
          description: |
            list of log levels to filter by. All the log levels are returned if not provided.
          items:
            type: string
            enum:
              - ERROR
              - WARN
              - INFO
              - DEBUG
        sort:
          type: string
          description: |
            Indicate whether the logs should be sorted by timestamp in ascending (asc)
            or descending (desc) order. If none or invalid value is provided,
            the logs will be sorted by ascending order.
          enum:
            - asc
            - desc
          default: asc
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched. If the provided limit exceeds the
            supported maximum logs limit, the maximum logs limit will be used.
          format: int64
          default: 100
        bin:
          type: integer
          description: partition interval
          format: int64
        binUnit:
          type: string
          description: partition interval unit
          enum:
            - m
            - s
            - h
          default: m
    ZippedLogsRequest:
      required:
        - componentId
        - endTime
        - environmentId
        - startTime
      type: object
      properties:
        startTime:
          type: string
          description: Start time for the logs as a ISO 8601 string.
          format: date-time
        endTime:
          type: string
          description: End time for the logs as a ISO 8601 string.
          format: date-time
        componentId:
          pattern: ^(([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})|(sample_app_component_id))$
          type: string
          description: Component id of the user application (specified in pod labels).
          format: uuid
        environmentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Environment id of the user application (specified in pod labels).
          format: uuid
        logLevels:
          type: array
          description: |
            list of log levels to filter by. All the log levels are returned if not provided.
          items:
            type: string
            enum:
              - ERROR
              - WARN
              - INFO
              - DEBUG
        searchPhrase:
          type: string
          description: |
            The log phrase for filter logs by. If an empty string is provided,
            all the logs will be returned without filtering.
          default: ""
        sort:
          type: string
          description: |
            Indicate whether the logs should be sorted by timestamp in ascending (asc)
            or descending (desc) order. If none or invalid value is provided,
            the logs will be sorted by ascending order.
          enum:
            - asc
            - desc
          default: asc
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched. If the provided limit exceeds the
            supported maximum logs limit, the maximum logs limit will be used.
          format: int64
          default: 100
        traceId:
          type: string
          description: Trace id of the request if the logs should be filtered for a specific request.
        timeZoneOffset:
          pattern: ^[\+\-]\d{2}:\d{2}$
          type: string
          description: Observability view time zone offset.
    GatewayLogsRequest:
      required:
        - componentId
        - endTime
        - environmentId
        - startTime
      type: object
      properties:
        startTime:
          type: string
          description: Start time for the logs as a ISO 8601 string.
          format: date-time
        endTime:
          type: string
          description: End time for the logs as a ISO 8601 string.
          format: date-time
        componentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Component id of the user application (specified in pod labels).
          format: uuid
        environmentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Environment id of the user application (specified in pod labels).
          format: uuid
        versionList:
          type: array
          description: |
            List of component versions to filter by. All the versions are returned if not provided.
          items:
            type: string
        searchPhrase:
          type: string
          description: |
            The log phrase for filter logs by. If an empty string is provided,
            all the logs will be returned without filtering.
          default: ""
        sort:
          type: string
          description: |
            Indicate whether the logs should be sorted by timestamp in ascending (asc)
            or descending (desc) order. If none or invalid value is provided,
            the logs will be sorted by ascending order.
          enum:
            - asc
            - desc
          default: asc
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched. If the provided limit exceeds the
            supported maximum logs limit, the maximum logs limit will be used.
          format: int64
          default: 100
    ProjectLogsRequest:
      required:
        - endTime
        - environmentId
        - projectId
        - startTime
      type: object
      properties:
        startTime:
          type: string
          description: Start time for the logs as a ISO 8601 string.
          format: date-time
        endTime:
          type: string
          description: End time for the logs as a ISO 8601 string.
          format: date-time
        projectId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Project id of the user application (specified in pod labels).
          format: uuid
        environmentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Environment id of the user application (specified in pod labels).
          format: uuid
        logLevels:
          type: array
          description: |
            list of log levels to filter by. All the log levels are returned if not provided.
          items:
            type: string
            enum:
              - ERROR
              - WARN
              - INFO
              - DEBUG
        searchPhrase:
          type: string
          description: |
            The log phrase for filter logs by. If an empty string is provided,
            all the logs will be returned without filtering.
          default: ""
        sort:
          type: string
          description: |
            Indicate whether the logs should be sorted by timestamp in ascending (asc)
            or descending (desc) order. If none or invalid value is provided,
            the logs will be sorted by ascending order.
          enum:
            - asc
            - desc
          default: asc
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched. If the provided limit exceeds the
            supported maximum logs limit, the maximum logs limit will be used.
          format: int64
          default: 100
        componentIdList:
          type: array
          description: |
            List of component ids to filter by. All the component ids are returned if not provided.
          items:
            type: string
    OrgLogsRequest:
      required:
        - endTime
        - environmentId
        - startTime
      type: object
      properties:
        startTime:
          type: string
          description: Start time for the logs as a ISO 8601 string.
          format: date-time
        endTime:
          type: string
          description: End time for the logs as a ISO 8601 string.
          format: date-time
        environmentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Environment id of the user application (specified in pod labels).
          format: uuid
        logLevels:
          type: array
          description: |
            list of log levels to filter by. All the log levels are returned if not provided.
          items:
            type: string
            enum:
              - ERROR
              - WARN
              - INFO
              - DEBUG
        searchPhrase:
          type: string
          description: |
            The log phrase for filter logs by. If an empty string is provided,
            all the logs will be returned without filtering.
          default: ""
        sort:
          type: string
          description: |
            Indicate whether the logs should be sorted by timestamp in ascending (asc)
            or descending (desc) order. If none or invalid value is provided,
            the logs will be sorted by ascending order.
          enum:
            - asc
            - desc
          default: asc
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched. If the provided limit exceeds the
            supported maximum logs limit, the maximum logs limit will be used.
          format: int64
          default: 100
        projectIdList:
          type: array
          description: |
            The list of project ids to filter logs by. If not provided, logs from all projects
            will be returned.
          items:
            type: string
    BuildLogCategory:
      type: object
      properties:
        log:
          type: string
          description: Main log which is base64 encoded
        status:
          type: string
          description: The status of the build
          enum:
            - completed
            - in_progress
        steps:
          type: array
          description: The list of steps in the category
          items:
            $ref: '#/components/schemas/BuildLogStep'
    PipelineLogStep:
      type: object
      properties:
        completed_at:
          type: string
          description: The time the step was completed
          format: date-time
        conclusion:
          type: string
          description: The conclusion of the step
          enum:
            - success
            - failure
        name:
          type: string
          description: The name of the step
        started_at:
          type: string
          description: The time the step was started
          format: date-time
        status:
          type: string
          description: The status of the step
          enum:
            - completed
            - in_progress
            - queued
        logs:
          type: array
          description: The logs of the step
          items:
            type: string
            description: The log of the step
        encodedLogs:
          type: string
          description: The logs of the step encoded in base64
    BuildLogStepV2:
      type: object
      properties:
        completed_at:
          type: string
          description: The time the step was completed
          format: date-time
        conclusion:
          type: string
          description: The conclusion of the step
          enum:
            - success
            - failure
        conclusionV2:
          type: string
          description: The conclusion of the step which contains warning status
          enum:
            - success
            - failure
            - warning
        name:
          type: string
          description: The name of the step
        started_at:
          type: string
          description: The time the step was started
          format: date-time
        status:
          type: string
          description: The status of the step
          enum:
            - completed
            - in_progress
            - queued
        isCustomizableStep:
          type: boolean
          description: Indicates if the step is user added step
    BuildLogStep:
      type: object
      properties:
        completed_at:
          type: string
          description: The time the step was completed
          format: date-time
        conclusion:
          type: string
          description: The conclusion of the step
          enum:
            - success
            - failure
        name:
          type: string
          description: The name of the step
        number:
          type: integer
          description: The number of the step
        started_at:
          type: string
          description: The time the step was started
          format: date-time
        status:
          type: string
          description: The status of the step
          enum:
            - completed
            - in_progress
            - queued
    LogsTable:
      type: object
      properties:
        columns:
          type: array
          description: |
            The list of columns returned in the response. The TimeGenerated column is a special column
            which would always be included in this array.
          items:
            $ref: '#/components/schemas/Column'
        rows:
          type: array
          description: |
            The rows mapping to the columns in the response. Each item in this array maps to a row in
            the returned table.
          items:
            $ref: '#/components/schemas/Row'
      description: |
        Response object received from the get logs endpoints in the form of a table.
        The table contains a TimeGenerated column which maps to the timestamp of a log.
    Column:
      type: object
      properties:
        name:
          type: string
          description: The name of the column which can be used to identify the rows.
        type:
          type: string
          description: This is a type hint which can be used for identifying the rows.
          enum:
            - datetime
            - string
            - dynamic
      description: Represents a column in the returned table
    Row:
      type: array
      description: |
        Represents a row in the returned table. Position of a datum in the row maps
        to a column in the columns array in the parent response object. The size of
        this array would be equal to the size of the array of columns.
      items:
        type: string
        description: |
          This is a datum in the row accroding to the column that maps to the datum.
    ErrorResponse:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/BackendErrorCodes'
        message:
          type: string
          description: The summarized error message
    HealthOkResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - OK
    HealthErrorResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - ERROR
    BackendErrorCodes:
      type: integer
      description: |
        Error code indicating the specific error that occurred.
        * OBS-L-10 - Parameters not received
        * OBS-L-11 - Malicious input
        * OBS-L-12 - Invalid input
        * OBS-L-13 - Authentication failure
        * OBS-L-22 - Internal store error
        * OBS-L-23 - Zip creation error
      enum:
        - null
        - null
        - null
        - null
        - null
        - null
  responses:
    SuccessfulBuildLogsV2:
      description: "Successful response containing the build logs. \n"
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                description: The list of steps of the pipeline
                items:
                  $ref: '#/components/schemas/BuildLogStepV2'
          examples:
            BuildLogs:
              value:
                data:
                  - completed_at: "2020-09-21T17:30:20.230200Z"
                    conclusion: success
                    conclusionV2: success
                    name: Environment Setup
                    started_at: "2020-09-21T17:30:20.230200Z"
                    status: completed
                    isCustomizableStep: false
                    encodedLogs: dGhpcyBpcyBhIHNhbXBsZSBsb2dz
                  - completed_at: "2020-09-21T17:30:20.230200Z"
                    conclusion: success
                    conclusionV2: success
                    name: Source File Validation
                    started_at: "2020-09-21T17:30:20.230200Z"
                    status: completed
                    isCustomizableStep: false
                    encodedLogs: dGhpcyBpcyBhIHNhbXBsZSBsb2dz
                  - completed_at: "2020-09-21T17:30:20.230200Z"
                    conclusion: success
                    conclusionV2: success
                    name: Build
                    started_at: "2020-09-21T17:30:20.230200Z"
                    status: completed
                    isCustomizableStep: false
                    encodedLogs: dGhpcyBpcyBhIHNhbXBsZSBsb2dz
                  - completed_at: "2020-09-21T17:30:20.230200Z"
                    conclusion: success
                    conclusionV2: success
                    name: Trivy Scan
                    started_at: "2020-09-21T17:30:20.230200Z"
                    status: completed
                    isCustomizableStep: false
                    encodedLogs: dGhpcyBpcyBhIHNhbXBsZSBsb2dz
                  - completed_at: "2020-09-21T17:30:20.230200Z"
                    conclusion: success
                    conclusionV2: success
                    name: Environment Cleanup
                    started_at: "2020-09-21T17:30:20.230200Z"
                    status: completed
                    isCustomizableStep: false
                    encodedLogs: dGhpcyBpcyBhIHNhbXBsZSBsb2dz
    SuccessfulBuildLogs:
      description: "Successful response containing the build logs. \n"
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  init:
                    $ref: '#/components/schemas/BuildLogCategory'
                  build:
                    $ref: '#/components/schemas/BuildLogCategory'
                  deploy:
                    $ref: '#/components/schemas/BuildLogCategory'
              success:
                type: boolean
                description: Indicates whether the request was successful or not.
          examples:
            BuildLogs:
              value:
                data:
                  init:
                    log: eyJ0ZXN0IjogInRlc3QgY29udGVudCIsICJzdGF0dXMiOiAiY29tcGxldGVkIiwgInN0ZXBzIjogW3sibmFtZSI6ICJ0ZXN0IiwgInN1c3RvbmVkX2F0IjogIjIwMjAtMDktMjFUMTc6MzA6MjAuMjMwMjAwMCIsICJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25
                    status: completed
                    steps:
                      - completed_at: "2020-09-21T17:30:20.230200Z"
                        conclusion: success
                        name: test
                        number: 1
                        started_at: "2020-09-21T17:30:20.230200Z"
                        status: completed
                      - completed_at: "2020-09-21T17:30:20.230200Z"
                        conclusion: success
                        name: test
                        number: 2
                        started_at: "2020-09-21T17:30:20.230200Z"
                        status: completed
                  build:
                    log: eyJ0ZXN0IjogInRlc3QgY29udGVudCIsICJzdGF0dXMiOiAiY29tcGxldGVkIiwgInN0ZXBzIjogW3sibmFtZSI6ICJ0ZXN0IiwgInN1c3RvbmVkX2F0I6IjIwMjAtMDktMjFUMTc6MzA6MjAuMjMwMjAwMCIsICJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25
                    status: completed
                    steps:
                      - completed_at: "2020-09-21T17:30:20.230200Z"
                        conclusion: success
                        name: test
                        number: 4
                        started_at: "2020-09-21T17:30:20.230200Z"
                        status: completed
                      - completed_at: "2020-09-21T17:30:20.230200Z"
                        conclusion: success
                        name: test
                        number: 5
                        started_at: "2020-09-21T17:30:20.230200Z"
                        status: completed
                  deploy:
                    log: eyJ0ZXN0IjogInRlc3QgY29udGVudCIsICJzdGF0dXMiOiAiY29tcGxldGVkIiwgInN0ZXBzIjogW3sibmFtZSI6ICJ0ZXN0IiwgInN1c3RvbmVkX2F0I6IjIwMjAtMDktMjFUMTc6MzA6MjAuMjMwMjAwMCIsICJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25jbHVzaW9uIjogInN1Y2Nlc3MiLCAic3RlcHMiOiBbeyJjb21wbGV0ZWQiOiAyMDIwLTA5LTIxVDE3OjMwOjIwLjIzMDIwMDAwLCJjb25
                    status: completed
                    steps:
                      - completed_at: "2020-09-21T17:30:20.230200Z"
                        conclusion: success
                        name: test
                        number: 7
                        started_at: "2020-09-21T17:30:20.230200Z"
                        status: completed
    SuccessfulPipelineLogs:
      description: "Successful response containing the pipeline logs. \n"
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                description: The list of steps of the pipeline
                items:
                  $ref: '#/components/schemas/PipelineLogStep'
              success:
                type: boolean
                description: Indicates whether the request was successful or not.
              message:
                type: string
                description: response status message
          examples:
            BuildLogs:
              value:
                data:
                  - completed_at: "2025-03-12T16:10:29.682Z"
                    conclusion: success
                    name: Step 1
                    started_at: "2025-03-12T16:10:27.701Z"
                    status: completed
                    logs:
                      - Hello, Argo Workflows! Executing step 1
                      - time="2025-03-12T16:10:28.701Z" level=info msg="sub-process exited" argo=true error="<nil>"
                    encodedLogs: CkhlbGxvLCBBcmdvIFdvcmtmbG93cyEgRXhlY3V0aW5nIHN0ZXAgMQp0aW1lPSIyMDI1LTAzLTEyVDE2OjEwOjI4LjcwMVoiIGxldmVsPWluZm8gbXNnPSJzdWItcHJvY2VzcyBleGl0ZWQiIGFyZ289dHJ1ZSBlcnJvcj0iPG5pbD4i
                  - completed_at: "2025-03-12T16:10:45.772Z"
                    conclusion: success
                    name: Step 2
                    started_at: "2025-03-12T16:10:43.788Z"
                    status: completed
                    logs:
                      - Hello, Argo Workflows! Executing step 2
                      - time="2025-03-12T16:10:44.787Z" level=info msg="sub-process exited" argo=true error="<nil>"
                    encodedLogs: CkhlbGxvLCBBcmdvIFdvcmtmbG93cyEgRXhlY3V0aW5nIHN0ZXAgMgp0aW1lPSIyMDI1LTAzLTEyVDE2OjEwOjQ0Ljc4N1oiIGxldmVsPWluZm8gbXNnPSJzdWItcHJvY2VzcyBleGl0ZWQiIGFyZ289dHJ1ZSBlcnJvcj0iPG5pbD4i
                  - completed_at: "2025-03-12T16:11:18.943Z"
                    conclusion: success
                    name: Step 3a
                    started_at: "2025-03-12T16:11:16.951Z"
                    status: completed
                    logs:
                      - Hello, Argo Workflows! Executing step 3a
                      - time="2025-03-12T16:11:17.951Z" level=info msg="sub-process exited" argo=true error="<nil>"
                    encodedLogs: CkhlbGxvLCBBcmdvIFdvcmtmbG93cyEgRXhlY3V0aW5nIHN0ZXAgM2EKdGltZT0iMjAyNS0wMy0xMlQxNjoxMToxNy45NTFaIiBsZXZlbD1pbmZvIG1zZz0ic3ViLXByb2Nlc3MgZXhpdGVkIiBhcmdvPXRydWUgZXJyb3I9IjxuaWw+Ig==
                success: true
                message: Successfully retrieved live logs for automation pipeline
    SuccessfulLogsTable:
      description: |
        Successful response containing the logs. The columns of the table are:
        * TimeGenerated - The timestamp of the log line
        * LogLevel - The level of the log (one of ["ERROR","INFO","WARN","DEBUG"])
        * LogEntry - The log message
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/LogsTable'
          examples:
            LogsTable:
              value:
                columns:
                  - name: TimeGenerated
                    type: datetime
                  - name: LogLevel
                    type: string
                  - name: LogEntry
                    type: string
                rows:
                  - - "2020-11-15T08:11:06.659Z"
                    - WARN
                    - '[choreo-samples/main-module] - Sample warn log'
                  - - "2020-11-15T08:11:06.659Z"
                    - WARN
                    - '[choreo-samples/main-module] - Sample warn log'
                  - - "2020-11-15T08:11:06.66Z"
                    - INFO
                    - '[choreo-samples/main-module] - Sample info log'
                  - - "2020-11-15T08:11:06.66Z"
                    - ERROR
                    - '[choreo-samples/main-module] - Sample error log'
                  - - "2020-11-15T08:11:06.66Z"
                    - ERROR
                    - '[choreo-samples/main-module] - new error log'
    SuccessfulProjectLogsTable:
      description: |
        Successful response containing the logs of all the components. The columns of the table are:
        * TimeGenerated - The timestamp of the log line
        * LogLevel - The level of the log (one of ["ERROR","INFO","WARN","DEBUG"])
        * LogEntry - The log message
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/LogsTable'
          examples:
            LogsTable:
              value:
                columns:
                  - name: TimeGenerated
                    type: datetime
                  - name: LogLevel
                    type: string
                  - name: LogEntry
                    type: string
                  - name: ComponentId
                    type: string
                  - name: ComponentName
                    type: string
                  - name: ComponentVersion
                    type: string
                rows:
                  - - "2020-11-15T08:11:06.659Z"
                    - WARN
                    - '[choreo-samples/main-module] - Sample warn log'
                    - f8517ee2-f5b1-4d7c-819d-6a9eb9d79436
                    - Sample app 1
                    - 1.0.0
                  - - "2020-11-15T08:11:06.659Z"
                    - WARN
                    - '[choreo-samples/main-module] - Sample warn log'
                    - a5517ee2-f5b1-4d7c-819d-6a9eb9d79537
                    - Sample app 2
                    - 1.0.0
                  - - "2020-11-15T08:11:06.66Z"
                    - INFO
                    - '[choreo-samples/main-module] - Sample info log'
                    - f8517ee2-f5b1-4d7c-819d-6a9eb9d79436
                    - Sample app 1
                    - 1.1.0
                  - - "2020-11-15T08:11:06.66Z"
                    - ERROR
                    - '[choreo-samples/main-module] - Sample error log'
                    - f8517ee2-f5b1-4d7c-819d-6a9eb9d79436
                    - Sample app 1
                    - 1.0.0
                  - - "2020-11-15T08:11:06.66Z"
                    - ERROR
                    - '[choreo-samples/main-module] - new error log'
                    - f8517ee2-f5b1-4d7c-819d-6a9eb9d79436
                    - Sample app 1
                    - 1.0.0
    SuccessExecutionsTable:
      description: |
        Successful response containing the executions.
      content:
        application/json:
          schema:
            type: object
            properties:
              list:
                type: array
                items:
                  $ref: '#/components/schemas/LogsTable'
              count:
                type: integer
          example:
            count: 100
            list:
              value:
                columns:
                  - name: ID
                    type: string
                  - name: Name
                    type: string
                  - name: GitHash
                    type: string
                  - name: StartTime
                    type: string
                rows:
                  - - 66cdd637-da9c-46ed-9897-60b7107e9c4b
                    - simpletask-653613759-28283386-x58ts
                    - a5cd28ef8d99b99adc0c3b5b5f169eb8a0f4c19f
                    - "2023-10-11T05:46:00Z"
                  - - 10cf568a-9972-4126-8a18-d16740992972
                    - simpletask-653613759-28283385-bcbvb
                    - a5cd28ef8d99b99adc0c3b5b5f169eb8a0f4c19f
                    - "2023-10-11T05:45:01Z"
                  - - b81674e2-da75-4bae-9596-69f5a1ac5beb
                    - simpletask-653613759-28283384-q9f6b
                    - a5cd28ef8d99b99adc0c3b5b5f169eb8a0f4c19f
                    - "2023-10-11T05:44:00Z"
                  - - 2630a7a9-1f29-4f5b-bb8a-9d97202154cc
                    - simpletask-653613759-28283383-9jxwb
                    - a5cd28ef8d99b99adc0c3b5b5f169eb8a0f4c19f
                    - "2023-10-11T05:43:00Z"
                  - - 47eeb53c-c8c1-436c-a911-61b868cbd8f6
                    - simpletask-653613759-28283382-k8fps
                    - a5cd28ef8d99b99adc0c3b5b5f169eb8a0f4c19f
                    - "2023-10-11T05:42:00Z"
    SuccessExecutionLogTable:
      description: |
        Successful response containing the execution logs.
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/LogsTable'
          examples:
            LogsTable:
              value:
                columns:
                  - name: ContainerID
                    type: string
                  - name: LogEntry
                    type: string
                  - name: TimeGenerated
                    type: datetime
                rows:
                  - - f6e6111d78e5966054286dbbe732d88be0ddfba1a9373bc91f83054038db71fb
                    - 'ballerina: initializing connection with observability backend periscope.preview-dv.choreo.dev:443'
                    - "2023-10-05T17:22:20.0918492Z"
                  - - f6e6111d78e5966054286dbbe732d88be0ddfba1a9373bc91f83054038db71fb
                    - 'error: Failed to create /home/<USER>/?/.config/choreo directory'
                    - "2023-10-05T17:22:21.9902304Z"
    Unauthorized:
      description: Response indicating an unauthorized request.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            InvalidCredentials:
              description: |
                The provided credentials does not have permission to view logs.
              value:
                code: OBS-L-13
                message: The provided credentials are not valid
    InternalStoreError:
      description: Response indicating an internal server error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            FailedToRetrieveMetrics:
              $ref: '#/components/examples/FailedToRetrieveMetrics'
  parameters:
    requestIdHeader:
      name: X-Request-Id
      in: header
      description: |
        Request ID for the request in order to track it across systems.
        A new id will be generated if not provided.
      required: false
      style: simple
      explode: false
      schema:
        pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
        type: string
        format: uuid
    authHeader:
      name: Authorization
      in: header
      description: |
        Authorization header containing the JWT token with the Bearer prefix.
      required: true
      style: simple
      explode: false
      schema:
        type: string
        format: Bearer <JWT>
    environmentId:
      name: environmentId
      in: query
      description: |
        Environment ID.
      required: true
      style: form
      explode: true
      schema:
        type: string
    componentId:
      name: componentId
      in: path
      description: |
        Component UUID.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    deploymentTrackId:
      name: deploymentTrackId
      in: path
      description: |
        UUID of the deployment track.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    executionId:
      name: executionId
      in: path
      description: |
        Task execution id
      required: true
      style: simple
      explode: false
      schema:
        type: string
    pipelineId:
      name: pipelineId
      in: path
      description: |
        Automation pipeline id.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    runId:
      name: runId
      in: path
      description: |
        Automation pipeline run id.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    offset:
      name: offset
      in: query
      description: |
        Offset value
      required: false
      style: form
      explode: true
      schema:
        type: string
    limit:
      name: limit
      in: query
      description: |
        Limit value
      required: false
      style: form
      explode: true
      schema:
        type: string
  examples:
    InvalidLimit:
      description: |
        Provided limit is invalid. A valid interger should be provided.
      value:
        code: OBS-L-12
        message: '''foo'' is not supported as the limit'
    TimeRangeNotProvided:
      description: Start time and/or end time query parameters not provided.
      value:
        code: OBS-L-10
        message: Required parameters not found
    InvalidTimeRange:
      description: |
        Provided start time and/or end time query parameters are invalid.
        Only valid ISO 8601 time strings are supported.
      value:
        code: OBS-L-12
        message: The request contains invalid values for startTime and/or endTime
    InvalidSearchPhrase:
      description: Provided log search phrase query parameter is invalid.
      value:
        code: OBS-L-12
        message: Character ';' is not allowed for filtering log entries
    InvalidLogLevels:
      description: Provided log level query parameter is invalid.
      value:
        code: OBS-L-12
        message: '''TRACE'' is not a supported log level'
    InvalidTimeZoneOffset:
      description: Provided timeZoneOffset is not in expected format.
      value:
        code: OBS-L-12
        message: Provided timeZoneOffset is not in expected format
    FailedToRetrieveMetrics:
      description: |
        Failed to retrive logs for the application due to an internal error. Please
        contact the developers if you require more information about the error.
      value:
        code: OBS-L-22
        message: Failed to retrieve logs
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreologgingapi/0.2.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
