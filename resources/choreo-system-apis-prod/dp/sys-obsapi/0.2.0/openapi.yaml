openapi: 3.0.1
info:
  title: Choreo System Observability API
  description: API for retrieving system metrics of choreo user applications.
  contact:
    email: <EMAIL>
  version: 0.2.0
servers:
  - url: https://{hostname}/sysobsapi
    variables:
      hostname:
        default: app.choreo.dev
paths:
  /system-metrics/component/application:
    get:
      summary: Retrieve metrics
      description: |
        Retrieve Memory and CPU usage metrics for the provided observability ID.
      operationId: getMetrics
      parameters:
        - $ref: '#/components/parameters/startTimeQueryParam'
        - $ref: '#/components/parameters/endTimeQueryParam'
        - $ref: '#/components/parameters/intervalQueryParam'
        - $ref: '#/components/parameters/releaseIdQueryParam'
        - $ref: '#/components/parameters/namespaceQueryParam'
        - $ref: '#/components/parameters/authHeader'
      responses:
        "200":
          description: |
            Successful response containing the metrics timeseries. The columns
            of the timeseries table are:
            * TimeGenerated - The series of start timestamps of the current bin of the timeseries
            * cpu - The series of absolute CPU used by the application
            * memory - The series of absolute memory used by the application
            * cpuPercentage - The series of CPU usage percentage of the application
            * memoryPercentage - The series of memory usage percentage of the application
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TimeseriesTable'
              examples:
                MetricsTimeSeries:
                  description: A table of metrics timeseries.
                  value:
                    columns:
                      - name: cpu
                        type: dynamic
                      - name: memory
                        type: dynamic
                      - name: cpuPercentage
                        type: dynamic
                      - name: memoryPercentage
                        type: dynamic
                      - name: TimeGenerated
                        type: dynamic
                    rows:
                      - - '[2.6019475,3.5465355999999995,4.078511666666667,2.2744435666666676,2.1614184499999998]'
                        - '[99.41028645833333,190.8876953125,199.72259114583332,203.94381510416667,204.40559895833334]'
                        - '[0.21397745182926814,0.21885869236111113,0.20725850462962955,0.3170908820754717,0.20080433749999999]'
                        - '[30.908771368684736,31.026507988177844,31.043993107225486,29.54618798433289,28.056405526620377]'
                        - '["2020-11-04T13:00:00.0000000Z","2020-11-04T14:00:00.0000000Z","2020-11-04T15:00:00.0000000Z","2020-11-04T16:00:00.0000000Z","2020-11-04T17:00:00.0000000Z"]'
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                TimeRangeNotProvided:
                  description: Start time query parameters not provided.
                  value:
                    message: no query param value found for 'startTime'
                    description: ""
                InvalidTimeRange:
                  description: |
                    Provided start time and/or end time query parameters are invalid.
                    Only valid ISO 8601 time strings are supported.
                  value:
                    code: OBS-S-12
                    message: The request contains invalid properties for startTime and/or endTime
                    description: ""
                InvalidInterval:
                  description: |
                    The provided interval is invalid. Only an integer indicating the number of
                    minutes is supported.
                  value:
                    code: OBS-S-12
                    message: 'error in casting query param : ''interval'''
                    description: ""
        "401":
          description: Response indicating an unauthorized request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                InvalidCredentials:
                  description: |
                    The provided credentials does not have permission to view the metrics timeseries.
                  value:
                    code: OBS-S-13
                    message: Authorization failed
                    description: ""
        "500":
          description: Response indicating an internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                FailedToRetrieveMetrics:
                  description: |
                    Failed to retrive metrics for the application due to an internal error. Please
                    contact the developers if you require more information about the error.
                  value:
                    code: OBS-S-22
                    message: Failed to retrieve metrics
                    description: ""
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /healthz:
    get:
      summary: Retrieve health of the sysobsapi
      description: |
        Retrieve the status of the API. This may not reflect exactly the current state of the API.
      operationId: checkHealth
      parameters:
        - $ref: '#/components/parameters/authHeader'
      responses:
        "200":
          description: Response indicating an ok status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthOkResponse'
        "503":
          description: Response indicating a service unavailable status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    TimeseriesTable:
      type: object
      properties:
        columns:
          type: array
          description: |
            The list of columns returned in the response. The TimeGenerated column is a special column
            which would always be included in this array.
          items:
            $ref: '#/components/schemas/Column'
        rows:
          type: array
          description: |
            The rows mapping to the columns in the response. Each item in this array maps to a row in
            the returned table.
          items:
            $ref: '#/components/schemas/Row'
      description: |
        Response object received from the get metrics endpoints in the form of a table.
        The table contains a TimeGenerated column which maps to the starting timestamp of a bin
        used in the generated time series. Other columns represents the different metrics timeseries
        available.
    Column:
      type: object
      properties:
        name:
          type: string
          description: The name of the column which can be used to identify the rows.
        type:
          type: string
          description: This is a type hint which can be used for identifying the rows.
          enum:
            - datetime
            - dynamic
      description: Represents a column in the returned table
    Row:
      type: array
      description: |
        Represents a row in the returned table. Position of a datum in the row maps
        to a column in the columns array in the parent response object. The size of
        this array would be equal to the size of the array of columns.
      items:
        type: string
        description: |
          This is a datum in the row and represents a series of data. This series
          is represented as a JSON string array in which each item corresponds to
          a bin starting at a timestamp in the timeseries. To find the starting
          timestamp refer the TimeGenerated column.
        format: json
    ErrorResponse:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/BackendErrorCodes'
        message:
          type: string
          description: The summarized error message
        description:
          type: string
          description: Description about the error occurred
    HealthOkResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - OK
    HealthErrorResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - ERROR
    BackendErrorCodes:
      type: integer
      description: |
        Error code indicating the specific error that occurred.
        * OBS-S-12 - Invalid input
        * OBS-S-13 - Authentication failure
        * OBS-S-22 - Internal store error
      enum:
        - null
        - null
        - null
  parameters:
    startTimeQueryParam:
      name: startTime
      in: query
      description: Start time for the timeseries metrics as a ISO 8601 string.
      required: true
      style: form
      explode: true
      schema:
        type: string
        format: date
    endTimeQueryParam:
      name: endTime
      in: query
      description: End time for the timeseries metrics as a ISO 8601 string.
      required: true
      style: form
      explode: true
      schema:
        type: string
        format: date
    intervalQueryParam:
      name: interval
      in: query
      description: Granularity for the timeseries of metrics in minutes.
      required: false
      style: form
      explode: true
      schema:
        type: integer
        default: 1
    releaseIdQueryParam:
      name: releaseId
      in: query
      description: Releas Id of the user application.
      required: true
      style: form
      explode: true
      schema:
        pattern: ^.{8}-.{4}-.{4}-.{4}-.{12}$|(sample_app_release_id)
        type: string
        format: uuid
    namespaceQueryParam:
      name: namespace
      in: query
      description: Observability namespace of the user application.
      required: true
      style: form
      explode: true
      schema:
        type: string
    authHeader:
      name: Authorization
      in: header
      description: |
        Authorization header containing the JWT token with the Bearer prefix.
      required: true
      style: simple
      explode: false
      schema:
        type: string
        format: Bearer <JWT>
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://choreo-sys-obsapi.choreo-observability.svc.cluster.local:9098/sysobsapi
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-sys-obsapi.choreo-observability.svc.cluster.local:9098/sysobsapi
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreosysobsapi/0.2.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
