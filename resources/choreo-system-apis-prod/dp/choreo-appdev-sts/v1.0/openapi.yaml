openapi: 3.0.1
info:
  title: Choreo Appdev STS
  description: Choreo Security Token Service API
  contact: {}
  version: v1.0
servers:
  - url: https://choreosts.dev/systemapis/choreo-appdev-sts/v1.0
security:
  - default: []
paths:
  /oauth2/*:
    get:
      summary: Get endpoints related to OAuth2
      responses:
        "200":
          description: Successful response
        "302":
          description: Redirect response
        "401":
          description: Unauthorized response
        "403":
          description: Forbidden response
        "404":
          description: Not Found response
        "500":
          description: Internal Server Error response
        "504":
          description: Gateway Timeout response
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      summary: Post endpoints related to OAuth2
      responses:
        "200":
          description: Successful response
        "302":
          description: Redirect response
        "401":
          description: Unauthorized response
        "403":
          description: Forbidden response
        "404":
          description: Not Found response
        "500":
          description: Internal Server Error response
        "504":
          description: Gateway Timeout response
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /oidc/*:
    get:
      summary: Get endpoints related to OIDC
      responses:
        "200":
          description: Successful response
        "401":
          description: Unauthorized response
        "403":
          description: Forbidden response
        "404":
          description: Not Found response
        "500":
          description: Internal Server Error response
        "504":
          description: Gateway Timeout response
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      summary: Post endpoints related to OIDC
      responses:
        "200":
          description: Successful response
        "401":
          description: Unauthorized response
        "403":
          description: Forbidden response
        "404":
          description: Not Found response
        "500":
          description: Internal Server Error response
        "504":
          description: Gateway Timeout response
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /login:
    get:
      summary: Get the Login page
      responses:
        "200":
          description: Successful response
        "302":
          description: Redirect response
        "401":
          description: Unauthorized response
        "403":
          description: Forbidden response
        "404":
          description: Not Found response
        "500":
          description: Internal Server Error response
        "504":
          description: Gateway Timeout response
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      summary: Submit login credentials
      responses:
        "200":
          description: Successful response
        "302":
          description: Redirect response
        "401":
          description: Unauthorized response
        "403":
          description: Forbidden response
        "404":
          description: Not Found response
        "500":
          description: Internal Server Error response
        "504":
          description: Gateway Timeout response
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /.well-known/openid-configuration:
    get:
      summary: Get OpenID configuration
      responses:
        "200":
          description: Successful response
        "401":
          description: Unauthorized response
        "403":
          description: Forbidden response
        "404":
          description: Not Found response
        "500":
          description: Internal Server Error response
        "504":
          description: Gateway Timeout response
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /connect/logout:
    get:
      summary: Logout endpoint
      responses:
        "200":
          description: Successful response
        "302":
          description: Redirect response
        "401":
          description: Unauthorized response
        "403":
          description: Forbidden response
        "404":
          description: Not Found response
        "500":
          description: Internal Server Error response
        "504":
          description: Gateway Timeout response
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-api-key-header: api-key
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins: []
  accessControlAllowCredentials: false
  accessControlAllowHeaders: []
  accessControlAllowMethods: []
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://choreo-appdev-sts.choreodp-system.svc.cluster.local:9000
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-appdev-sts.choreodp-system.svc.cluster.local:9000
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreo-appdev-sts/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
