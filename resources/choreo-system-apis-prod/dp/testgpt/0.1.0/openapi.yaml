openapi: 3.0.1
info:
  title: Choreo TestGPT API
  description: API for using Choreo TestGPT for API testing
  contact:
    email: <EMAIL>
  version: 0.1.0
servers:
  - url: https://{hostname}/testgpt
    variables:
      hostname:
        default: app.choreo.dev
security:
  - default: []
paths:
  /prepare:
    post:
      summary: Processing the OpenAPI specification to extract the API endpoint definitions and generate sample queries
      operationId: postPrepare
      parameters:
        - name: x-request-id
          in: header
          description: Request ID
          required: false
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Test preparation request payload
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestPreparationRequest'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestPreparationResponse'
        "500":
          description: InternalServerError
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /execute:
    post:
      summary: Execute a single API test case while caching the progress
      operationId: postExecute
      parameters:
        - name: token
          in: header
          description: Authorization token
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-request-id
          in: header
          description: Request ID
          required: false
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiTestResult'
        "500":
          description: InternalServerError
        "404":
          description: NotFound
        "400":
          description: BadRequest
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /health:
    get:
      summary: Heath check endpoint
      operationId: getHealth
      responses:
        "200":
          description: Ok
        "500":
          description: InternalServerError
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    TestPreparationRequest:
      required:
        - openapi
      type: object
      properties:
        openapi:
          type: object
          additionalProperties: true
          description: openapi specification
      description: request for api enrichment
    ConstantValueSchema:
      required:
        - const
      type: object
      properties:
        const:
          description: The constant value.
      description: Defines a constant value field in the schema.
    BaseInputTypeSchema:
      required:
        - type
      type: object
      properties:
        type:
          type: string
          description: Input data type. Should be one of `STRING`, `INTEGER`, `NUMBER`, `FLOAT`, or `BOOLEAN`.
          enum:
            - array
            - object
            - number
            - boolean
            - float
            - integer
            - string
        description:
          type: string
        default:
          description: Default value of the input
    PrimitiveInputSchema:
      allOf:
        - $ref: '#/components/schemas/BaseInputTypeSchema'
        - type: object
          properties:
            format:
              type: string
              description: Format of the input. This is not applicable for `BOOLEAN` type.
            pattern:
              type: string
              description: Pattern of the input. This is only applicable for `STRING` type.
            enum:
              type: array
              description: Enum values of the input. This is only applicable for `STRING` type.
              items:
                type: string
    ObjectInputSchema:
      allOf:
        - $ref: '#/components/schemas/BaseInputTypeSchema'
        - required:
            - properties
            - type
          type: object
          properties:
            type:
              description: Input data type. Should be `OBJECT`.
            required:
              type: array
              description: List of required properties
              items:
                type: string
            properties:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/JsonSubSchema'
              description: Schema of the object properties
            description:
              type: string
            default: {}
    ArrayInputSchema:
      allOf:
        - $ref: '#/components/schemas/BaseInputTypeSchema'
        - required:
            - items
            - type
          type: object
          properties:
            type:
              description: Input data type. Should be `ARRAY`.
            items:
              $ref: '#/components/schemas/JsonSubSchema'
            default:
              type: array
              description: Default value for the array
              items: {}
            description:
              type: string
    AnyOfInputSchema:
      required:
        - anyOf
      type: object
      properties:
        anyOf:
          type: array
          description: List of possible input types
          items:
            $ref: '#/components/schemas/ObjectInputSchema'
      description: Defines an `anyOf` input field in the schema. Follows OpenAPI 3.x specification.
    OneOfInputSchema:
      required:
        - oneOf
      type: object
      properties:
        oneOf:
          type: array
          description: List of possible input types
          items:
            $ref: '#/components/schemas/JsonSubSchema'
      description: Defines an `oneOf` input field in the schema. Follows OpenAPI 3.x specification.
    AllOfInputSchema:
      required:
        - allOf
      type: object
      properties:
        allOf:
          type: array
          description: List of possible input types
          items:
            $ref: '#/components/schemas/ObjectInputSchema'
      description: Defines an `allOf` input field in the schema. Follows OpenAPI 3.x specification.
    NotInputSchema:
      required:
        - not
      type: object
      properties:
        not:
          $ref: '#/components/schemas/JsonSubSchema'
      description: Defines a `not` input field in the schema. Follows OpenAPI 3.x specification.
    JsonSubSchema:
      description: Defines a json sub schema
      oneOf:
        - $ref: '#/components/schemas/ObjectInputSchema'
        - $ref: '#/components/schemas/ArrayInputSchema'
        - $ref: '#/components/schemas/AnyOfInputSchema'
        - $ref: '#/components/schemas/OneOfInputSchema'
        - $ref: '#/components/schemas/AllOfInputSchema'
        - $ref: '#/components/schemas/NotInputSchema'
        - $ref: '#/components/schemas/PrimitiveInputSchema'
        - $ref: '#/components/schemas/ConstantValueSchema'
    ArrayTypeParameterSchema:
      allOf:
        - $ref: '#/components/schemas/ArrayInputSchema'
        - type: object
          properties: {}
    ParameterType:
      description: Define parameter types for HTTP parameters.
      oneOf:
        - $ref: '#/components/schemas/ConstantValueSchema'
        - $ref: '#/components/schemas/PrimitiveInputSchema'
        - $ref: '#/components/schemas/ArrayTypeParameterSchema'
    ParameterSchema:
      required:
        - properties
      type: object
      properties:
        required:
          type: array
          description: A list of mandatory parameters
          items:
            type: string
        properties:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ParameterType'
          description: A map of parameter names and their types
      description: Defines a HTTP parameter schema (can be query parameter or path parameters).
    JsonInputSchema:
      description: Defines a json input schema
      oneOf:
        - $ref: '#/components/schemas/ObjectInputSchema'
        - $ref: '#/components/schemas/ArrayInputSchema'
        - $ref: '#/components/schemas/AnyOfInputSchema'
        - $ref: '#/components/schemas/OneOfInputSchema'
        - $ref: '#/components/schemas/AllOfInputSchema'
        - $ref: '#/components/schemas/NotInputSchema'
    HttpTool:
      required:
        - description
        - method
        - name
        - path
      type: object
      properties:
        name:
          type: string
          description: Name of the Http resource tool
        description:
          type: string
          description: Description of the Http resource tool used by the LLM
        method:
          type: string
          description: Http method type (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)
          enum:
            - OPTIONS
            - HEAD
            - PATCH
            - PUT
            - DELETE
            - POST
            - GET
        path:
          type: string
          description: Path of the Http resource
        queryParameters:
          $ref: '#/components/schemas/ParameterSchema'
        pathParameters:
          $ref: '#/components/schemas/ParameterSchema'
        requestBody:
          $ref: '#/components/schemas/JsonInputSchema'
      description: Defines an HTTP tool. This is a special type of tool that can be used to invoke HTTP resources.
    HttpApiSpecification:
      required:
        - tools
      type: object
      properties:
        serviceUrl:
          type: string
          description: Extracted service URL from the OpenAPI specification if there is any
        tools:
          type: array
          description: Extracted Http tools from the OpenAPI specification
          items:
            $ref: '#/components/schemas/HttpTool'
      description: Provides extracted tools and service URL from the OpenAPI specification.
    SampleQuery:
      required:
        - query
        - scenario
      type: object
      properties:
        scenario:
          type: string
          description: scenario
        query:
          type: string
          description: query generated
      description: sample query record
    TestPreparationResponse:
      required:
        - apiSpec
        - queries
      type: object
      properties:
        apiSpec:
          $ref: '#/components/schemas/HttpApiSpecification'
        queries:
          type: array
          description: list of sample queries
          items:
            $ref: '#/components/schemas/SampleQuery'
      description: response for api enrichment
    ApiTestResult:
      required:
        - taskStatus
        - testCaseId
      type: object
      properties:
        testCaseId:
          type: string
          description: test case id
          nullable: true
        taskStatus:
          type: string
          description: task status
          enum:
            - EXPIRED_TOKEN
            - TERMINATED
            - COMPLETED
            - IN_PROGRESS
        result:
          description: result of the test case
          oneOf:
            - {}
            - type: string
      description: response for api testing
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://testgpt.choreo-ai.svc.cluster.local:9090
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://testgpt.choreo-ai.svc.cluster.local:9090
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreotestgpt/0.1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
