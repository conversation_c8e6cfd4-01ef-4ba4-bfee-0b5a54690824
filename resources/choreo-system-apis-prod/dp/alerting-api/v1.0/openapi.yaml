openapi: 3.0.0
info:
  title: Choreo Alerting API
  description: API for configuring OpenSearch based alerts in Choreo DPs
  contact: {}
  version: v1.0
servers:
  - url: http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080/systemapis/choreo-alerting-api/v1.0
    description: Choreo Alerting Service
security:
  - default: []
paths:
  /readiness:
    get:
      summary: Readiness check
      operationId: readiness
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /liveness:
    get:
      summary: Liveness check
      operationId: liveness
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /healthz:
    get:
      summary: Health check for Choreo Alerting api
      operationId: healthz
      responses:
        "200":
          description: All dependencies are healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    description: Overall health status
                    enum:
                      - healthy
                  timestamp:
                    type: string
                    description: Timestamp of the health check
                    format: date-time
                  dependencies:
                    type: object
                    properties:
                      obsManager:
                        type: string
                        description: OBS Manager health status
                        enum:
                          - healthy
                      redis:
                        type: string
                        description: Redis health status
                        enum:
                          - healthy
                      openSearch:
                        type: string
                        description: OpenSearch health status
                        enum:
                          - healthy
                      alert-manager:
                        type: string
                        description: alert-manager health status
                        enum:
                          - healthy
        "503":
          description: One or more dependencies are unhealthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    description: Overall health status
                    enum:
                      - unhealthy
                  timestamp:
                    type: string
                    description: Timestamp of the health check
                    format: date-time
                  dependencies:
                    type: object
                    properties:
                      obsManager:
                        type: string
                        description: OBS Manager health status
                        enum:
                          - unhealthy
                      redis:
                        type: string
                        description: Redis health status
                        enum:
                          - healthy
                      openSearch:
                        type: string
                        description: OpenSearch health status
                        enum:
                          - unhealthy
                      alert-manager:
                        type: string
                        description: alert-manager health status
                        enum:
                          - healthy
                  error:
                    type: string
                    description: Error message if health check fails
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /component/alerts/rules:
    get:
      summary: List component alert rules
      parameters:
        - name: componentId
          in: query
          description: ID of the component to filter rules
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: environmentId
          in: query
          description: ID of the environment to filter rules
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: type
          in: query
          description: alert rule type
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: versionId
          in: query
          description: ID of the version to filter rules
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Alert rule search results
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ComponentAlertRuleConfiguration'
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Create component alert rule
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComponentAlertRuleConfiguration'
        required: true
      responses:
        "201":
          description: Rule added successfully
          content:
            application/json:
              schema:
                type: object
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Delete all component alert rules by component ID
      responses:
        "200":
          description: Rules deleted successfully
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /component/alerts/rules/{ruleId}:
    get:
      summary: Get component alert rule by ID
      parameters:
        - name: componentId
          in: query
          description: ID of the component to filter rules
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: ruleId
          in: path
          description: Alert rule ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Alert rule retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentAlertRuleConfiguration'
        "404":
          description: No rule found in DB
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      summary: Update component alert rule by rule ID
      parameters:
        - name: ruleId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComponentAlertRuleConfiguration'
        required: true
      responses:
        "200":
          description: Rule updated successfully
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Delete component alert rule by rule ID
      parameters:
        - name: ruleId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Rule deleted successfully
        "404":
          description: No rule found in DB
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /component/alerts/history/search:
    post:
      summary: Get Triggered Alerts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HistoryComponentsRequestBody'
        required: true
      responses:
        "200":
          description: List of triggered alerts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HistoryComponentResponseBody'
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /component/alerts/rules/count:
    get:
      summary: Get component alert rules count and capacity information
      parameters:
        - name: componentId
          in: query
          description: ID of the component to filter rules
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: environmentId
          in: query
          description: ID of the environment to filter rules
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Alert rule count and capacity information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertRuleCount'
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    Health:
      properties:
        status: {}
    ComponentAlertRuleConfiguration:
      required:
        - componentId
        - emails
        - environmentId
        - type
      type: object
      properties:
        id:
          type: string
        type:
          type: string
        metric:
          type: number
          format: float
        threshold:
          type: string
        period:
          type: string
        interval:
          type: integer
        count:
          type: integer
        searchPhrase:
          type: string
        logType:
          type: string
        statusCode:
          type: string
        emails:
          type: array
          items:
            type: string
        enabled:
          type: boolean
        componentId:
          type: string
        environmentId:
          type: string
        projectId:
          type: string
        versionId:
          type: string
        apimEnvIdList:
          type: array
          items:
            type: string
    HistoryComponentsRequestBody:
      type: object
      properties:
        alertTypes:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
              metrics:
                type: array
                items:
                  type: string
        componentId:
          type: string
        endTime:
          type: string
        environmentId:
          type: string
        limit:
          type: integer
          format: int64
        releaseIdList:
          type: array
          items:
            type: string
        searchPhrase:
          type: string
        sort:
          type: string
        startTime:
          type: string
        versionIdList:
          type: array
          items:
            type: string
    Error:
      title: Error object returned with 4XX HTTP status
      required:
        - code
        - message
      properties:
        code:
          description: Error code
        message:
          description: Error message.
        description:
          description: |
            A detail description about the error message.
    HistoryComponentResponseBody:
      type: object
      properties:
        columns:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              type:
                type: string
        rows:
          type: array
          items:
            type: object
            properties:
              ComponentVersionId:
                type: string
              ComponentReleaseId:
                type: string
              TimeGenerated:
                type: string
              AlertType:
                type: string
              AlertMessage:
                type: string
              AlertMetric:
                type: string
              ComponentVersion:
                type: string
    AlertRuleCount:
      required:
        - count
        - max
        - remaining
      type: object
      properties:
        count:
          type: integer
          description: Current number of alert rules
        remaining:
          type: integer
          description: Remaining capacity for alert rules
        max:
          type: integer
          description: Maximum allowed alert rules
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreo-alerting-api/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
