{"id": "67d1701741e1bc3cd16d28ac", "name": "Choreo Alerting API", "displayName": "Choreo Alerting API", "description": "API for configuring OpenSearch based alerts in Choreo DPs", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreo-alerting-api", "version": "v1.0", "provider": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": [], "maxTps": null, "visibility": "PUBLIC", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1741778967933", "lastUpdatedTime": "2025-06-26 09:19:59.668", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}, "production_endpoints": {"url": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/readiness", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/readiness", "verb": "GET", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/liveness", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/liveness", "verb": "GET", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/healthz", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/healthz", "verb": "GET", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/component/alerts/rules", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/component/alerts/rules", "verb": "GET", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/component/alerts/rules", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/component/alerts/rules", "verb": "POST", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/component/alerts/rules", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/component/alerts/rules", "verb": "DELETE", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/component/alerts/rules/{ruleId}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/component/alerts/rules/{ruleId}", "verb": "GET", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/component/alerts/rules/{ruleId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/component/alerts/rules/{ruleId}", "verb": "PUT", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/component/alerts/rules/{ruleId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/component/alerts/rules/{ruleId}", "verb": "DELETE", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/component/alerts/history/search", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/component/alerts/history/search", "verb": "POST", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/component/alerts/rules/count", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "97da9dc7-f34d-4ccf-94b6-13cbbfe9c224", "backendOperation": {"target": "/component/alerts/rules/count", "verb": "GET", "endpoint": "http://choreo-alerting-service.choreo-observability.svc.cluster.local:8080"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": null, "componentId": null, "versionId": "67d1701741e1bc3cd16d28ac"}}