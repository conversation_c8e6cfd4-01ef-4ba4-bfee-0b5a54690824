openapi: 3.0.0
info:
  title: AI Copilot
  contact: {}
  version: v1.0
servers:
  - url: https://choreoapis.dev/systemapis/aicopilot/v1.0
security:
  - default: []
paths:
  /copilot:
    get:
      summary: Health check
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Submit a question
      parameters:
        - name: x-request-id
          in: header
          description: Request ID
          required: false
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuestionInput'
        required: true
      responses:
        "200":
          description: Success
          content:
            text/event-stream:
              schema:
                oneOf:
                  - type: string
                  - type: object
      security:
        - BearerAuth: []
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    QuestionInput:
      type: object
      properties:
        question:
          minLength: 1
          type: string
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - x-request-id
    - correlation-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://ai-copilot.choreo-ai:9090/
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://ai-copilot.choreo-ai:9090/
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/aicopilot/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
