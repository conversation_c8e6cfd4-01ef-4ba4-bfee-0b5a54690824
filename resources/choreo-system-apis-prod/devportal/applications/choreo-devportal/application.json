{"applicationId": "f5d4a453-6186-4fea-b262-7a95bb194acb", "name": "Choreo Devportal", "throttlingPolicy": "Unlimited", "description": "Application for Choreo Developer Portal in the Production environment", "tokenType": "JWT", "status": "APPROVED", "groups": [], "subscriptionCount": 3, "keys": [], "attributes": {"isDpSystemApp": "", "scopes": ""}, "subscriptionScopes": [{"key": "urn:choreosystem:usermanagement:role_mapping_update", "name": "role_mapping_update", "roles": [], "description": "Update group role mapping"}, {"key": "urn:choreosystem:usermanagement:user_update", "name": "user_update", "roles": [], "description": "Update users"}, {"key": "urn:choreosystem:usermanagement:role_mapping_delete", "name": "role_mapping_delete", "roles": [], "description": "Delete group role mapping"}, {"key": "urn:choreosystem:usermanagement:invitation_view", "name": "invitation_view", "roles": [], "description": "View invitation"}, {"key": "urn:choreosystem:usermanagement:role_mapping_view", "name": "role_mapping_view", "roles": [], "description": "View group role mapping"}, {"key": "urn:choreosystem:usermanagement:role_update", "name": "role_update", "roles": [], "description": "Update roles"}, {"key": "urn:choreosystem:organizationapi:org_manage", "name": "org_manage", "roles": [], "description": "Manage organizations"}, {"key": "urn:choreosystem:usermanagement:user_delete", "name": "user_delete", "roles": [], "description": "Create user"}, {"key": "urn:choreosystem:usermanagement:role_create", "name": "role_create", "roles": [], "description": "Create roles"}, {"key": "urn:choreosystem:usermanagement:invitation_delete", "name": "invitation_delete", "roles": [], "description": "Delete invitation"}, {"key": "urn:choreosystem:usermanagement:invitation_manage", "name": "invitation_manage", "roles": [], "description": "Manage invitation"}, {"key": "urn:choreosystem:usermanagement:user_manage", "name": "user_manage", "roles": [], "description": "Manage users"}, {"key": "urn:choreosystem:usermanagement:role_mapping_manage", "name": "role_mapping_manage", "roles": [], "description": "Manage group role mapping"}, {"key": "urn:choreosystem:usermanagement:role_mapping_create", "name": "role_mapping_create", "roles": [], "description": "Create group role mapping"}, {"key": "urn:choreosystem:usermanagement:invitation_send", "name": "invitation_send", "roles": [], "description": "Send invitation"}, {"key": "urn:choreosystem:usermanagement:role_manage", "name": "role_manage", "roles": [], "description": "Manage roles"}, {"key": "urn:choreosystem:usermanagement:role_view", "name": "role_view", "roles": [], "description": "View roles"}, {"key": "urn:choreosystem:usermanagement:role_delete", "name": "role_delete", "roles": [], "description": "Delete roles"}, {"key": "urn:choreosystem:usermanagement:user_view", "name": "user_view", "roles": [], "description": "View user"}], "owner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "ownerInfo": {"id": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "displayName": "Choreo System", "email": "<EMAIL>"}, "sharedPermissions": ["SHARE", "WRITE", "SUBSCRIBE", "READ"], "sharedWith": [], "hashEnabled": false, "createdTime": "1642658435647", "updatedTime": "1642658435647"}