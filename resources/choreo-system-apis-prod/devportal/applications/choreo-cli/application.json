{"applicationId": "2f3e40e1-2653-4b6a-95dd-2b312ad56c2d", "name": "Choreo CLI", "throttlingPolicy": "Unlimited", "description": "", "tokenType": "JWT", "status": "APPROVED", "groups": [], "subscriptionCount": 16, "keys": [], "attributes": {"isDpSystemApp": "", "scopes": ""}, "subscriptionScopes": [{"key": "urn:choreosystem:componentsmanagement:component_init_view", "name": "component_init_view", "roles": [], "description": "View component init status"}, {"key": "urn:choreosystem:componentutils:component_trigger", "name": "component_trigger", "roles": [], "description": "Trigger component"}, {"key": "urn:choreosystem:choreodevopsportalapi:component_manage", "name": "component_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:choreodevopsportalapi:deployment_view", "name": "deployment_view", "roles": [], "description": ""}, {"key": "urn:choreosystem:organizationapi:org_manage", "name": "org_manage", "roles": [], "description": "Manage organizations"}, {"key": "urn:choreosystem:configmanagement:global_config_delete", "name": "global_config_delete", "roles": [], "description": "Delete global configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_config_view", "name": "component_config_view", "roles": [], "description": "View component configs"}, {"key": "urn:choreosystem:configmanagement:config_delete", "name": "config_delete", "roles": [], "description": "Delete configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_logs_view", "name": "component_logs_view", "roles": [], "description": "View component logs"}, {"key": "urn:choreosystem:configmanagement:config_manage", "name": "config_manage", "roles": [], "description": "Manage configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_create", "name": "component_create", "roles": [], "description": "Create component"}, {"key": "urn:choreosystem:configmanagement:global_config_manage", "name": "global_config_manage", "roles": [], "description": "Manage global configuration"}, {"key": "urn:choreosystem:choreodevopsportalapi:deployment_manage", "name": "deployment_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:componentsmanagement:component_trigger", "name": "component_trigger", "roles": [], "description": "Trigger component"}, {"key": "urn:choreosystem:componentutils:component_manage", "name": "component_manage", "roles": [], "description": "Manage component"}, {"key": "urn:choreosystem:configmanagement:global_config_create", "name": "global_config_create", "roles": [], "description": "Create global configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_manage", "name": "component_manage", "roles": [], "description": "Manage component"}, {"key": "urn:choreosystem:configmanagement:config_create", "name": "config_create", "roles": [], "description": "Create configuration"}, {"key": "urn:choreosystem:configmanagement:config_view", "name": "config_view", "roles": [], "description": "View configuration"}, {"key": "urn:choreosystem:configmanagement:global_config_update", "name": "global_config_update", "roles": [], "description": "Update global configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_file_view", "name": "component_file_view", "roles": [], "description": "View component file"}, {"key": "urn:choreosystem:configmanagement:global_config_view", "name": "global_config_view", "roles": [], "description": "View global configuration"}], "owner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "ownerInfo": {"id": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "displayName": "Choreo System", "email": "<EMAIL>"}, "sharedPermissions": ["SHARE", "WRITE", "SUBSCRIBE", "READ"], "sharedWith": [], "hashEnabled": false, "createdTime": "1695645965903", "updatedTime": "1695645965903"}