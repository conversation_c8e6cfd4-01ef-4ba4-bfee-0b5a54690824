subscriptions:
  - subscriptionId: 5a018883-e011-498c-8b5a-cf66c4b75507
    applicationId: 2adae431-e640-4d1d-ba44-3f9d38811561
    apiId: 633be0a544f40d1b459e5a9f
    apiInfo:
      id: 633be0a544f40d1b459e5a9f
      name: Choreo Admin API
      displayName: Choreo Admin API
      description: |
        This is a RESTFul API to perform Administrative operations in Choreo
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api
      version: 1.0.0
      type: HTTP
      createdTime: "1664868517194"
      provider: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 2adae431-e640-4d1d-ba44-3f9d38811561
      name: Billing Admin Operations
      throttlingPolicy: Unlimited
      description: This application is used to generate OAuth tokens to access Choreo Admin API
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729245743980"
      updatedTime: "1729245743980"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
