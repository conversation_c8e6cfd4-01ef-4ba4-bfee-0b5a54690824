subscriptions:
  - subscriptionId: b3ea39bd-8341-4b01-ba25-f8491088dedd
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 662f36a6e23797639415e2ba
    apiInfo:
      id: 662f36a6e23797639415e2ba
      name: AI Copilot
      displayName: AI Copilot
      description: AI Copilot service that provide chat experience in Chore Console.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/aicopilot
      version: v1.0
      type: HTTP
      createdTime: "1714370214962"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://ai-copilot.choreo-ai:9090/
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 6ace374c-882c-4b15-b19e-6f6c0623db95
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 62cd4f3fafa3f22aadee5e8f
    apiInfo:
      id: 62cd4f3fafa3f22aadee5e8f
      name: Alert Configuration Service
      displayName: Alert Configuration Service
      description: This api is used to connect to the Alert Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/alert-configuration-service
      version: 1.0.0
      type: HTTP
      createdTime: "1657622335295"
      provider: 531b4953-bc28-40fb-8794-41834bd55438
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 531b4953-bc28-40fb-8794-41834bd55438
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 2
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 50c7ccd2-2bd4-4fd1-8d3c-a52a8ea3b562
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 670768cb2584260c4314ce42
    apiInfo:
      id: 670768cb2584260c4314ce42
      name: API Key Service
      displayName: API Key Service
      description: This api is used to API Key management operations
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/api-key-service
      version: v1.0
      type: HTTP
      createdTime: "1728538827190"
      provider: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 3
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 84af1c22-02fa-4276-bf04-142780d78a27
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 65448ed2be658723011579a7
    apiInfo:
      id: 65448ed2be658723011579a7
      name: APIM AppDev APIs
      displayName: APIM AppDev APIs
      description: This api is used to connect to the APIM AppDev APIs service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/apim-appdev
      version: v1.0
      type: HTTP
      createdTime: "1698991826606"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 4
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: a24be429-c46f-4cb8-936b-72227a6b7aea
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 654489dabe6587230115797a
    apiInfo:
      id: 654489dabe6587230115797a
      name: Built-in IdP User Management
      displayName: Built-in IdP User Management
      description: This is the RESTful API for Choreo Resident IdP User Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-store-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698990554639"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 5
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: cc911f60-b0c7-4c87-94e0-13abbc56bf2c
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 65448bbbbe6587230115797d
    apiInfo:
      id: 65448bbbbe6587230115797d
      name: Built-in STS Authorization Service
      displayName: Built-in STS Authorization Service
      description: |
        This API specification outlines the endpoints and operations for the Authorization Service.  It enables users to handle roles, role-permission mappings, role-group mappings, and process authorization requests.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/authz-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698991035696"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 6
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 27527ec0-2ba1-4f32-8986-6c7a274dcc95
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 633be0a544f40d1b459e5a9f
    apiInfo:
      id: 633be0a544f40d1b459e5a9f
      name: Choreo Admin API
      displayName: Choreo Admin API
      description: |
        This is a RESTFul API to perform Administrative operations in Choreo
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api
      version: 1.0.0
      type: HTTP
      createdTime: "1664868517194"
      provider: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 7
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 5c121374-3408-4570-aa19-2706c4953bd4
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 63a447d4afc39e72d98ea2be
    apiInfo:
      id: 63a447d4afc39e72d98ea2be
      name: Choreo AI Test Assistant
      displayName: Choreo AI Test Assistant
      description: |
        The Test Assistant API provides example payload data for testing Choreo components.
        It considers the payload schemas of the Open API specification and generates sample data that conforms to the
        schema and enriches the specification with the generated examples.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/ai-test-assistant
      version: 1.0.0
      type: HTTP
      createdTime: "1671710676949"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 8
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 9aa20033-9d07-4fe6-8854-290981190cc6
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 67289e13d90467127752125e
    apiInfo:
      id: 67289e13d90467127752125e
      name: Choreo Appdev STS Management Service
      displayName: Choreo Appdev STS Management Service
      description: API for the Choreo Appdev STS Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-appdev-sts-management-service
      version: v1.0
      type: HTTP
      createdTime: "1730715155674"
      provider: a579828c-19e4-4406-92f4-15df9033c2fc
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: a579828c-19e4-4406-92f4-15df9033c2fc
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 9
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 97706b75-56d7-4d2c-b556-bd309bad025c
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6757f66a542ddb34b244bb53
    apiInfo:
      id: 6757f66a542ddb34b244bb53
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: v1.0
      type: HTTP
      createdTime: "1733817962976"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 10
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: b9079e73-f03e-4455-8544-8c8a590c9ecf
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6482e60403e62408c6c8be6e
    apiInfo:
      id: 6482e60403e62408c6c8be6e
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: 1.0.0
      type: HTTP
      createdTime: "1686300164732"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 11
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 6f233420-4b41-4957-8c20-5aa35b6222c0
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 631aa4195fb46c53f7971f88
    apiInfo:
      id: 631aa4195fb46c53f7971f88
      name: Choreo DevOps Portal API
      displayName: Choreo DevOps Portal API
      description: This api is used to connect to the Choreo DevOps Portal API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/devops
      version: 1.0.0
      type: HTTP
      createdTime: "1662690329318"
      provider: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 12
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: cf0c7e5e-a826-402c-ac08-e26ba44e5a14
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 66f15212fe856218b0c32c84
    apiInfo:
      id: 66f15212fe856218b0c32c84
      name: Choreo Governance Service API
      displayName: Choreo Governance Service API
      description: API for governing Choreo APIs/Components and other resources.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/governance
      version: v1.0
      type: HTTP
      createdTime: "1727091218612"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 13
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 15abd978-62fb-4afa-b83c-54bd8f2ab11f
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 650142320a6377400ddc4a99
    apiInfo:
      id: 650142320a6377400ddc4a99
      name: Choreo TestGPT API
      displayName: Choreo TestGPT API
      description: API for using Choreo TestGPT for API testing.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreotestgpt
      version: 0.1.0
      type: HTTP
      createdTime: "1694581298180"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://testgpt.choreo-ai.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 14
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 25ec19d7-cc3b-44ae-af8f-38abc5bc38df
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 63f89b690ba4773450505c81
    apiInfo:
      id: 63f89b690ba4773450505c81
      name: CIO Incident Configurator
      displayName: CIO Incident Configurator
      description: This API will act as the configurator for CIO dashboard. API will store configuration  details required for incident scraper and serve them as a REST API on demand
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-incident-configurator
      version: 1.0.0
      type: HTTP
      createdTime: "1677237097935"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 15
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: bedf93d1-b428-4c6f-8afd-16caabe22093
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 63a2d42a17a2541a07342aeb
    apiInfo:
      id: 63a2d42a17a2541a07342aeb
      name: CIO Query API
      displayName: CIO Query API
      description: This api is used to connect to the CIO Query API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-query-api
      version: 1.0.0
      type: HTTP
      createdTime: "1671615530332"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 16
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 122abb61-11f8-40f9-9adb-0c9d9572267d
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 65a108071b4376654a0141ef
    apiInfo:
      id: 65a108071b4376654a0141ef
      name: Component Creation Status
      displayName: Component Creation Status
      description: This api is used to connect to the Component Creation Status service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-creation
      version: v1.0
      type: HTTP
      createdTime: "1705052167906"
      provider: 51c0ae66-b0cd-42b2-90b5-2605f856bf24
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 51c0ae66-b0cd-42b2-90b5-2605f856bf24
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 17
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 4fd2d9e6-ba11-4d4c-bfe5-f16ce7d7e69c
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 670e3f6055f33d048d031340
    apiInfo:
      id: 670e3f6055f33d048d031340
      name: Component Utils
      displayName: Component Utils
      description: This is Component Management Utils API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils
      version: 1.0.0
      type: HTTP
      createdTime: "1728986976365"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 18
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: d9e3362f-c52e-4846-8b91-c9adb29bbd26
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 645c87bf29258d5e5cd9125d
    apiInfo:
      id: 645c87bf29258d5e5cd9125d
      name: Components Management
      displayName: Components Management
      description: This is the Choreo Components Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1683785663821"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 19
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: d51ce5c9-b25e-4ee8-bb42-8322d15e905c
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 64140b1f0b438a1df721516c
    apiInfo:
      id: 64140b1f0b438a1df721516c
      name: Config Management
      displayName: Config Management
      description: This is the Choreo Global Configuration Service API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1679035167851"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 20
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 132e34e1-6d19-459c-b480-2375a2bd812f
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 654350269e53c83b329ab097
    apiInfo:
      id: 654350269e53c83b329ab097
      name: Configuration Mapping Service
      displayName: Configuration Mapping Service
      description: This api is used to connect to the Configuration Mapping Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910246275"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 21
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 01818a79-2bc0-4d3e-b684-8eebc78b8adc
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6543529f9e53c83b329ab0a5
    apiInfo:
      id: 6543529f9e53c83b329ab0a5
      name: Configuration Service
      displayName: Configuration Service
      description: This api is used to connect to the Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910879023"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 22
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 3e7586b5-d5d6-4fc8-abe1-0cfcd682d9b4
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6544d9e29a886f6d1afddd22
    apiInfo:
      id: 6544d9e29a886f6d1afddd22
      name: Connection Management
      displayName: Connection Management
      description: This api is used to connect to the Connection Management service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections
      version: v1.0
      type: HTTP
      createdTime: "1699011042225"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://connection-service.prod-choreo-system:9000/connections/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 23
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 291b2324-4f35-43c7-b44e-f21ee821eddd
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 61bdd47abd84cd17397efee0
    apiInfo:
      id: 61bdd47abd84cd17397efee0
      name: ConnectorBuilder
      displayName: ConnectorBuilder
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connectorbuilder
      version: 0.1.0
      type: HTTP
      createdTime: "1639830650431"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: e859aabe-1642-48ff-845e-a631303fd9e7
        vendor: WSO2
      businessInformation:
        businessOwner: Isuru Boyagane
        businessOwnerEmail: <EMAIL>
        technicalOwner: Isuru Boyagane
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 24
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 7f974b0b-c0b1-417f-a3e1-76d75bf6f910
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6423c6c318381a2b93e26270
    apiInfo:
      id: 6423c6c318381a2b93e26270
      name: Crypto Key Service
      displayName: Crypto Key Service
      description: Api to expose crypto key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/crypto-key-service
      version: 0.1.0
      type: HTTP
      createdTime: "1680066243461"
      provider: 392560f1-c26f-45f3-89d6-ad0a5cec79af
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 392560f1-c26f-45f3-89d6-ad0a5cec79af
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 25
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 67384c3e-2f3c-49c2-ac7f-c9d5c8fb08e9
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6541ed3532503454e1085aa3
    apiInfo:
      id: 6541ed3532503454e1085aa3
      name: Declarative API
      displayName: Declarative API
      description: This api is used to connect to the Declarative API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/declarative-api
      version: v1.0
      type: HTTP
      createdTime: "1698819381865"
      provider: 5072771b-6e31-41d3-8964-2f4fb991d6e3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://declarative-api.prod-choreo-system:8080/api
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5072771b-6e31-41d3-8964-2f4fb991d6e3
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 26
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: f23e80f0-9ed3-4091-9db6-50bd00269d36
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 61bdd480bd84cd17397efee1
    apiInfo:
      id: 61bdd480bd84cd17397efee1
      name: Insights
      displayName: Insights
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insights
      version: 1.0.0
      type: HTTP
      createdTime: "1639830656122"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "5.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: fcbbb075-1891-4756-ae44-1a9432e13d0a
        vendor: WSO2
      businessInformation:
        businessOwner: Fazlan Nazeem
        businessOwnerEmail: <EMAIL>
        technicalOwner: Fazlan Nazeem
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 27
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: fb6e43a7-30c0-4f86-8bd5-02db62a540e3
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 61bdd3ccbd84cd17397efedf
    apiInfo:
      id: 61bdd3ccbd84cd17397efedf
      name: InsightsAlert
      displayName: InsightsAlert
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insightsalert
      version: 1.0.0
      type: HTTP
      createdTime: "1639830476199"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 7a90890c-9c7d-4f13-86ea-93c74a75f801
        vendor: WSO2
      businessInformation:
        businessOwner: Raveen Rathnayake
        businessOwnerEmail: <EMAIL>
        technicalOwner: Raveen Rathnayake
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 28
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 28a0fdda-95f1-4c61-9f87-479a8360a030
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6486b08be88a407b4f8f5ff1
    apiInfo:
      id: 6486b08be88a407b4f8f5ff1
      name: Marketplace
      displayName: Marketplace
      description: This api is used to connect to the Marketplace service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/marketplace
      version: 0.1.0
      type: HTTP
      createdTime: "1686548619356"
      provider: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 29
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 91e7eddc-a671-4e2d-9df2-4433a3986a2b
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 667e7240a3a9912fa3455193
    apiInfo:
      id: 667e7240a3a9912fa3455193
      name: Milvus Proxy
      displayName: Milvus Proxy
      description: API to interact with the milvus vector DB
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy
      version: v1.0
      type: HTTP
      createdTime: "1719562816821"
      provider: 278ca2f9-1c2e-44f8-8966-eacb35954a99
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 278ca2f9-1c2e-44f8-8966-eacb35954a99
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 30
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: ec7e7498-41bb-4e81-a676-189117ad1e30
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 648031628219b95767c68caa
    apiInfo:
      id: 648031628219b95767c68caa
      name: Moesif Key
      displayName: Moesif Key
      description: This api is used to connect to the Moesif Key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/moesif-key
      version: 0.1.0
      type: HTTP
      createdTime: "1686122850577"
      provider: 2f146911-4a19-4906-9109-bcd6e60feba1
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2f146911-4a19-4906-9109-bcd6e60feba1
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 31
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 01ecc7cf-f12d-41fa-a3e5-e58ae486ae0f
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 64b4e04e6f73c71fa9969691
    apiInfo:
      id: 64b4e04e6f73c71fa9969691
      name: Observability Manager
      displayName: Observability Manager
      description: This api is used to connect to the Observability Manager service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager
      version: 0.1.0
      type: HTTP
      createdTime: "1689575502411"
      provider: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Silver
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 32
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 473ce793-ee9c-4512-943e-509cd5eaa029
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 61bd9a5d6c70d4270abf41b3
    apiInfo:
      id: 61bd9a5d6c70d4270abf41b3
      name: ObservabilityApplication
      displayName: ObservabilityApplication
      description: Choreo control plane Observability API for fetching application level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/application
      version: 0.1.0
      type: HTTP
      createdTime: "1639815773440"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 33
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: fcea5a9a-db23-4f4a-8d4f-b99f188dbdd3
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 61bd9d156c70d4270abf41b5
    apiInfo:
      id: 61bd9d156c70d4270abf41b5
      name: ObservabilityLogging
      displayName: ObservabilityLogging
      description: Choreo control plane Observability API for fetching logs of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/logging
      version: 0.1.0
      type: HTTP
      createdTime: "1639816469410"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 34
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 24094370-e9e8-4bc2-93ff-73d77738c0da
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 61bd9d9482ae4a5211ea7924
    apiInfo:
      id: 61bd9d9482ae4a5211ea7924
      name: ObservabilitySystem
      displayName: ObservabilitySystem
      description: Choreo control plane Observability API for fetching system level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/system
      version: 0.1.0
      type: HTTP
      createdTime: "1639816596688"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 35
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 9702de31-0fa1-4bda-a589-d8701f610f2b
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6423d5729382a77e3d50c19e
    apiInfo:
      id: 6423d5729382a77e3d50c19e
      name: OnPremKey Management
      displayName: OnPremKey Management
      description: This is the Choreo APIInsights AccessKeys API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/onprem-key-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1680070002236"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 36
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: d21aa626-ec89-4598-8d03-99468c8def8a
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 64742bf8240d4717b1131bcc
    apiInfo:
      id: 64742bf8240d4717b1131bcc
      name: Organization API
      displayName: Organization API
      description: This is the Choreo Runtime Organization API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/orgs
      version: 1.0.0
      type: HTTP
      createdTime: "1685335032726"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 37
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 7409f2a3-ec67-4d02-9929-c4526883a6de
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 62c3ce68f4ea5a773534c0b8
    apiInfo:
      id: 62c3ce68f4ea5a773534c0b8
      name: Organization Management
      displayName: Organization Management
      description: |-
        Owned by Org-Mgt team

        Repo: https://github.com/wso2-enterprise/choreo-organization-management
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/org-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1656999528217"
      provider: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 38
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: a68a01a0-7487-4d0a-8bb3-c0893b9ec0ad
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 6541172854bcdb21d2a334e4
    apiInfo:
      id: 6541172854bcdb21d2a334e4
      name: Platform Services Manager API
      displayName: Platform Services Manager API
      description: This api is used to connect to the Platform Services Manager API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/platform-services
      version: v1.0
      type: HTTP
      createdTime: "1698764584558"
      provider: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://platform-services-manager.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 39
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: d8f8f33c-304d-43af-bcc3-653fee1bdcdd
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 62419cc7690bbb0c8c544443
    apiInfo:
      id: 62419cc7690bbb0c8c544443
      name: Project API
      displayName: Project API
      description: This api is used to connect to the Project API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/projects
      version: 1.0.0
      type: HTTP
      createdTime: "1648467143810"
      provider: 2d92c502-cb6c-411e-865d-e25dace20279
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2d92c502-cb6c-411e-865d-e25dace20279
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: null
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 40
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: f6ceb893-b87e-4cb8-b021-451b83a5122b
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 65e82d435e87d93a849b8943
    apiInfo:
      id: 65e82d435e87d93a849b8943
      name: URL Management API
      displayName: URL Management API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/url-mgt
      version: v1.0
      type: HTTP
      createdTime: "1709714755963"
      provider: d962d47d-f855-4202-be7b-3b31e391d1f4
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d962d47d-f855-4202-be7b-3b31e391d1f4
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 41
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: d2836f56-eeaa-423f-a423-5ff64b1c2ee6
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 64daf146d5aa8b4bada40cde
    apiInfo:
      id: 64daf146d5aa8b4bada40cde
      name: User Management
      displayName: User Management
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1692070214459"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://api-server.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 42
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 31e59291-8e8b-4891-9439-18fde90c9b7c
    applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
    apiId: 66ea50d5ebf87a2d63485eb1
    apiInfo:
      id: 66ea50d5ebf87a2d63485eb1
      name: Workflow Management API
      displayName: Workflow Management API
      description: Workflow Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/workflow-mgt
      version: v1.0
      type: HTTP
      createdTime: "1726632149432"
      provider: 678ead13-dc75-4004-9fc0-35cd45eddf8f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 678ead13-dc75-4004-9fc0-35cd45eddf8f
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 99ab9f47-3c46-4a81-820d-b49c89429c18
      name: Devant
      throttlingPolicy: Unlimited
      description: Devant UI application
      status: APPROVED
      groups: []
      subscriptionCount: 43
      attributes: {}
      owner: 02518c5a-596d-4501-bdd7-227785f1e754
      createdTime: "1740378916277"
      updatedTime: "1740378916277"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
