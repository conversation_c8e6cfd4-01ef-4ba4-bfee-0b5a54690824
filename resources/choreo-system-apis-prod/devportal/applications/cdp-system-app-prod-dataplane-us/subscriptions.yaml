subscriptions:
  - subscriptionId: b127e5ad-07c6-464a-a692-3b94fd23220c
    applicationId: 5d8ed25f-1420-417d-b534-ecbf1ce0eef0
    apiId: 61d293d43234be4e91a47af3
    apiInfo:
      id: 61d293d43234be4e91a47af3
      name: NotificationService
      displayName: NotificationService
      description: This API is used to connect to the Service Openapi Yaml service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/notification-service
      version: 1.0.0
      type: HTTP
      createdTime: "1641190356623"
      provider: 4c1b2770-c9ac-49aa-a5ec-08d90014e95b
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4c1b2770-c9ac-49aa-a5ec-08d90014e95b
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 5d8ed25f-1420-417d-b534-ecbf1ce0eef0
      name: cdp-system-app-prod-dataplane-us
      throttlingPolicy: Unlimited
      description: This application is used to subscribe to internal CP System APIs
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 0228631a-5468-445b-af43-ba78b5468457
      createdTime: "1741234675270"
      updatedTime: "1741234675270"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - apiId: 67ca8c1946d9173bc9cc0a24
    throttlingPolicy: Unlimited
