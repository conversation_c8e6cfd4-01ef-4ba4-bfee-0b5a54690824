subscriptions:
  - subscriptionId: aaf7e634-6fb5-4643-b9fb-961218a28715
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 670768cb2584260c4314ce42
    apiInfo:
      id: 670768cb2584260c4314ce42
      name: API Key Service
      displayName: API Key Service
      description: This api is used to API Key management operations
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/api-key-service
      version: v1.0
      type: HTTP
      createdTime: "1728538827190"
      provider: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 670768cb2584260c4314ce42
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:44.517"
  - subscriptionId: 4f74895f-2ded-4144-a3b9-fc511759fb59
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6482e60403e62408c6c8be6e
    apiInfo:
      id: 6482e60403e62408c6c8be6e
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: 1.0.0
      type: HTTP
      createdTime: "1686300164732"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-12-11 09:39:00.593"
  - subscriptionId: a0744afa-a331-4b3c-a663-a6575a7f7e92
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6757f66a542ddb34b244bb53
    apiInfo:
      id: 6757f66a542ddb34b244bb53
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: v1.0
      type: HTTP
      createdTime: "1733817962976"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6757f66a542ddb34b244bb53
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-12-11 09:39:15.963"
  - subscriptionId: f852a823-00bb-4333-b163-352139e2a179
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 631aa4195fb46c53f7971f88
    apiInfo:
      id: 631aa4195fb46c53f7971f88
      name: Choreo DevOps Portal API
      displayName: Choreo DevOps Portal API
      description: This api is used to connect to the Choreo DevOps Portal API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/devops
      version: 1.0.0
      type: HTTP
      createdTime: "1662690329318"
      provider: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:36.377"
  - subscriptionId: 18970ed7-1403-4884-8df5-370dc1661c38
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 66f15212fe856218b0c32c84
    apiInfo:
      id: 66f15212fe856218b0c32c84
      name: Choreo Governance Service API
      displayName: Choreo Governance Service API
      description: API for governing Choreo APIs/Components and other resources.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/governance
      version: v1.0
      type: HTTP
      createdTime: "1727091218612"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 66f15212fe856218b0c32c84
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:43.863"
  - subscriptionId: 6efc44ff-5f44-4fa6-be70-fd65052a7b2e
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 64bfa4eb012b9122ece541b1
    apiInfo:
      id: 64bfa4eb012b9122ece541b1
      name: Choreo Logging API
      displayName: Choreo Logging API
      description: API for retrieving logs of choreo user applications.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreologgingapi
      version: 0.2.0
      type: HTTP
      createdTime: "1690281195822"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:37.16"
  - subscriptionId: 3512fdd2-dd29-4749-a26c-eb1e80581ad9
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 670e3f6055f33d048d031340
    apiInfo:
      id: 670e3f6055f33d048d031340
      name: Component Utils
      displayName: Component Utils
      description: This is Component Management Utils API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils
      version: 1.0.0
      type: HTTP
      createdTime: "1728986976365"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-11-21 12:57:24.873"
  - subscriptionId: edf391a8-686a-4218-af27-84e426326294
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 645c87bf29258d5e5cd9125d
    apiInfo:
      id: 645c87bf29258d5e5cd9125d
      name: Components Management
      displayName: Components Management
      description: This is the Choreo Components Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1683785663821"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:37.813"
  - subscriptionId: 08a5cf94-c4b4-4a3d-ade6-dac546498a3d
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 64140b1f0b438a1df721516c
    apiInfo:
      id: 64140b1f0b438a1df721516c
      name: Config Management
      displayName: Config Management
      description: This is the Choreo Global Configuration Service API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1679035167851"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:38.5"
  - subscriptionId: 60bfd59e-ce84-44e7-8452-80ba73a67c58
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 654350269e53c83b329ab097
    apiInfo:
      id: 654350269e53c83b329ab097
      name: Configuration Mapping Service
      displayName: Configuration Mapping Service
      description: This api is used to connect to the Configuration Mapping Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910246275"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 654350269e53c83b329ab097
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:39.263"
  - subscriptionId: cf6ae88a-f6c9-48cc-bd25-d6d84ae61995
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6544d9e29a886f6d1afddd22
    apiInfo:
      id: 6544d9e29a886f6d1afddd22
      name: Connection Management
      displayName: Connection Management
      description: This api is used to connect to the Connection Management service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections
      version: v1.0
      type: HTTP
      createdTime: "1699011042225"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://connection-service.prod-choreo-system:9000/connections/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6544d9e29a886f6d1afddd22
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:39.903"
  - subscriptionId: e39510a4-bd4b-47da-a3b7-44e690913584
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6541ed3532503454e1085aa3
    apiInfo:
      id: 6541ed3532503454e1085aa3
      name: Declarative API
      displayName: Declarative API
      description: This api is used to connect to the Declarative API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/declarative-api
      version: v1.0
      type: HTTP
      createdTime: "1698819381865"
      provider: 5072771b-6e31-41d3-8964-2f4fb991d6e3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://declarative-api.prod-choreo-system:8080/api
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5072771b-6e31-41d3-8964-2f4fb991d6e3
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6541ed3532503454e1085aa3
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:40.62"
  - subscriptionId: 72f602f0-a551-4c6e-88fd-bb9ad590cdd1
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6486b08be88a407b4f8f5ff1
    apiInfo:
      id: 6486b08be88a407b4f8f5ff1
      name: Marketplace
      displayName: Marketplace
      description: This api is used to connect to the Marketplace service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/marketplace
      version: 0.1.0
      type: HTTP
      createdTime: "1686548619356"
      provider: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:41.213"
  - subscriptionId: 7fd6e25b-c539-4c3b-85da-13074eeaaf52
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 64b4e04e6f73c71fa9969691
    apiInfo:
      id: 64b4e04e6f73c71fa9969691
      name: Observability Manager
      displayName: Observability Manager
      description: This api is used to connect to the Observability Manager service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager
      version: 0.1.0
      type: HTTP
      createdTime: "1689575502411"
      provider: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Silver
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:41.82"
  - subscriptionId: 513cad71-c3e0-400f-b64a-996fdc3d63d3
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 64742bf8240d4717b1131bcc
    apiInfo:
      id: 64742bf8240d4717b1131bcc
      name: Organization API
      displayName: Organization API
      description: This is the Choreo Runtime Organization API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/orgs
      version: 1.0.0
      type: HTTP
      createdTime: "1685335032726"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:42.483"
  - subscriptionId: 8a87dae1-146a-445a-9fe7-6a1536420fad
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6541172854bcdb21d2a334e4
    apiInfo:
      id: 6541172854bcdb21d2a334e4
      name: Platform Services Manager API
      displayName: Platform Services Manager API
      description: This api is used to connect to the Platform Services Manager API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/platform-services
      version: v1.0
      type: HTTP
      createdTime: "1698764584558"
      provider: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://platform-services-manager.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6541172854bcdb21d2a334e4
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-07-09 06:06:27.923"
  - subscriptionId: 2862a375-6d01-4c1d-92a4-7a0d8b531de1
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 62419cc7690bbb0c8c544443
    apiInfo:
      id: 62419cc7690bbb0c8c544443
      name: Project API
      displayName: Project API
      description: This api is used to connect to the Project API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/projects
      version: 1.0.0
      type: HTTP
      createdTime: "1648467143810"
      provider: 2d92c502-cb6c-411e-865d-e25dace20279
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2d92c502-cb6c-411e-865d-e25dace20279
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: null
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-16 07:57:43.233"
  - subscriptionId: de86f970-b51e-421e-aaa4-262709cc66a5
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 65e82d435e87d93a849b8943
    apiInfo:
      id: 65e82d435e87d93a849b8943
      name: URL Management API
      displayName: URL Management API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/url-mgt
      version: v1.0
      type: HTTP
      createdTime: "1709714755963"
      provider: d962d47d-f855-4202-be7b-3b31e391d1f4
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d962d47d-f855-4202-be7b-3b31e391d1f4
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 65e82d435e87d93a849b8943
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-02-17 03:39:35.917"
  - subscriptionId: 8758b364-9114-4fa3-96c6-a4387733e39f
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 64daf146d5aa8b4bada40cde
    apiInfo:
      id: 64daf146d5aa8b4bada40cde
      name: User Management
      displayName: User Management
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1692070214459"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://api-server.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-05-23 05:58:00.72"
  - subscriptionId: 7cebb6f7-1fd6-4015-a5af-1158b841a468
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 66ea50d5ebf87a2d63485eb1
    apiInfo:
      id: 66ea50d5ebf87a2d63485eb1
      name: Workflow Management API
      displayName: Workflow Management API
      description: Workflow Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/workflow-mgt
      version: v1.0
      type: HTTP
      createdTime: "1726632149432"
      provider: 678ead13-dc75-4004-9fc0-35cd45eddf8f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 678ead13-dc75-4004-9fc0-35cd45eddf8f
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 66ea50d5ebf87a2d63485eb1
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-11-21 12:57:25.377"
  - subscriptionId: 6e6878b3-8250-4e87-b978-df7f55153bbe
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 662f36a6e23797639415e2ba
    apiInfo:
      id: 662f36a6e23797639415e2ba
      name: AI Copilot
      displayName: AI Copilot
      description: AI Copilot service that provide chat experience in Chore Console.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/aicopilot
      version: v1.0
      type: HTTP
      createdTime: "1714370214962"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://ai-copilot.choreo-ai:9090/
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 21
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: de1bfb50-5c10-4782-8644-487d8732cc4a
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 62cd4f3fafa3f22aadee5e8f
    apiInfo:
      id: 62cd4f3fafa3f22aadee5e8f
      name: Alert Configuration Service
      displayName: Alert Configuration Service
      description: This api is used to connect to the Alert Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/alert-configuration-service
      version: 1.0.0
      type: HTTP
      createdTime: "1657622335295"
      provider: 531b4953-bc28-40fb-8794-41834bd55438
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 531b4953-bc28-40fb-8794-41834bd55438
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 22
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: b71c27b3-7033-45f5-82f0-1be5dde9ce76
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 65448ed2be658723011579a7
    apiInfo:
      id: 65448ed2be658723011579a7
      name: APIM AppDev APIs
      displayName: APIM AppDev APIs
      description: This api is used to connect to the APIM AppDev APIs service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/apim-appdev
      version: v1.0
      type: HTTP
      createdTime: "1698991826606"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 23
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 53f5b076-7c10-4aa5-9815-bdbba7349e8c
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 686df722d34caf156680dfe7
    apiInfo:
      id: 686df722d34caf156680dfe7
      name: Automation Pipelines API
      displayName: Automation Pipelines API
      description: API for managing automation pipelines.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/automation-pipelines
      version: v1.0
      type: HTTP
      createdTime: "1752037154351"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 24
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 3c9c0543-88e7-4803-82e0-43d040c10e18
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 67c5307b75ae805502c72267
    apiInfo:
      id: 67c5307b75ae805502c72267
      name: Build Pipeline Service
      displayName: Build Pipeline Service
      description: API for the Build Pipeline Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipeline-service
      version: 1.0.0
      type: HTTP
      createdTime: "1740976251631"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 25
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: bb8935f8-5e95-4e9d-a398-c79788deb78d
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 67c9287304e65a6a11828e40
    apiInfo:
      id: 67c9287304e65a6a11828e40
      name: Build Pipelines
      displayName: Build Pipelines
      description: API for the Build Pipelines.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipelines
      version: v1.0
      type: HTTP
      createdTime: "1741236339290"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 26
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 890453a5-c1fa-4dd7-8d23-218b70b47ff2
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 654489dabe6587230115797a
    apiInfo:
      id: 654489dabe6587230115797a
      name: Built-in IdP User Management
      displayName: Built-in IdP User Management
      description: This is the RESTful API for Choreo Resident IdP User Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-store-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698990554639"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 27
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: ec9148c5-2bcb-4d0c-a409-73657af3dc1f
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 65448bbbbe6587230115797d
    apiInfo:
      id: 65448bbbbe6587230115797d
      name: Built-in STS Authorization Service
      displayName: Built-in STS Authorization Service
      description: |
        This API specification outlines the endpoints and operations for the Authorization Service.  It enables users to handle roles, role-permission mappings, role-group mappings, and process authorization requests.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/authz-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698991035696"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 28
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: ef65a9ad-6e44-44ac-97ed-0fc1e39d2281
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 633be0a544f40d1b459e5a9f
    apiInfo:
      id: 633be0a544f40d1b459e5a9f
      name: Choreo Admin API
      displayName: Choreo Admin API
      description: |
        This is a RESTFul API to perform Administrative operations in Choreo
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api
      version: 1.0.0
      type: HTTP
      createdTime: "1664868517194"
      provider: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 29
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: dea56538-6db9-42df-9041-0ada827f03cd
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 63a447d4afc39e72d98ea2be
    apiInfo:
      id: 63a447d4afc39e72d98ea2be
      name: Choreo AI Test Assistant
      displayName: Choreo AI Test Assistant
      description: |
        The Test Assistant API provides example payload data for testing Choreo components.
        It considers the payload schemas of the Open API specification and generates sample data that conforms to the
        schema and enriches the specification with the generated examples.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/ai-test-assistant
      version: 1.0.0
      type: HTTP
      createdTime: "1671710676949"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 30
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 505dca01-0c6f-4b08-9a98-534fba8f50f5
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 67289e13d90467127752125e
    apiInfo:
      id: 67289e13d90467127752125e
      name: Choreo Appdev STS Management Service
      displayName: Choreo Appdev STS Management Service
      description: API for the Choreo Appdev STS Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-appdev-sts-management-service
      version: v1.0
      type: HTTP
      createdTime: "1730715155674"
      provider: a579828c-19e4-4406-92f4-15df9033c2fc
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: a579828c-19e4-4406-92f4-15df9033c2fc
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 31
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 5e05741d-aaaa-4cab-a374-0cf1129899c5
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 650142320a6377400ddc4a99
    apiInfo:
      id: 650142320a6377400ddc4a99
      name: Choreo TestGPT API
      displayName: Choreo TestGPT API
      description: API for using Choreo TestGPT for API testing.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreotestgpt
      version: 0.1.0
      type: HTTP
      createdTime: "1694581298180"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://testgpt.choreo-ai.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 32
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: d5154c3d-085f-41bf-ad6a-39dc09ac8949
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 63f89b690ba4773450505c81
    apiInfo:
      id: 63f89b690ba4773450505c81
      name: CIO Incident Configurator
      displayName: CIO Incident Configurator
      description: This API will act as the configurator for CIO dashboard. API will store configuration  details required for incident scraper and serve them as a REST API on demand
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-incident-configurator
      version: 1.0.0
      type: HTTP
      createdTime: "1677237097935"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 33
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 7f577f76-ba91-4588-a43b-583d015e6255
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 63a2d42a17a2541a07342aeb
    apiInfo:
      id: 63a2d42a17a2541a07342aeb
      name: CIO Query API
      displayName: CIO Query API
      description: This api is used to connect to the CIO Query API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-query-api
      version: 1.0.0
      type: HTTP
      createdTime: "1671615530332"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 34
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 981f001d-f8eb-46b3-83c6-618b101be668
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 65a108071b4376654a0141ef
    apiInfo:
      id: 65a108071b4376654a0141ef
      name: Component Creation Status
      displayName: Component Creation Status
      description: This api is used to connect to the Component Creation Status service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-creation
      version: v1.0
      type: HTTP
      createdTime: "1705052167906"
      provider: 51c0ae66-b0cd-42b2-90b5-2605f856bf24
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 51c0ae66-b0cd-42b2-90b5-2605f856bf24
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 35
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 92e17ed2-a19f-4822-9b2e-cd360e38ab04
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 67038cf4f86e5c6a5a320084
    apiInfo:
      id: 67038cf4f86e5c6a5a320084
      name: Configuration Schema Service
      displayName: Configuration Schema Service
      description: This api is used to connect to the Configuration Schema Service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/configuration-schema
      version: v1.0
      type: HTTP
      createdTime: "1728285940200"
      provider: 8e923942-ae07-4a50-a426-3c6467624ee4
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 8e923942-ae07-4a50-a426-3c6467624ee4
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 36
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: a7eb0cd5-51f9-47cb-8207-91f87822eb95
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6543529f9e53c83b329ab0a5
    apiInfo:
      id: 6543529f9e53c83b329ab0a5
      name: Configuration Service
      displayName: Configuration Service
      description: This api is used to connect to the Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910879023"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 37
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 50e345eb-5544-46cb-903d-a9330bcacb1f
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 61bdd47abd84cd17397efee0
    apiInfo:
      id: 61bdd47abd84cd17397efee0
      name: ConnectorBuilder
      displayName: ConnectorBuilder
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connectorbuilder
      version: 0.1.0
      type: HTTP
      createdTime: "1639830650431"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: e859aabe-1642-48ff-845e-a631303fd9e7
        vendor: WSO2
      businessInformation:
        businessOwner: Isuru Boyagane
        businessOwnerEmail: <EMAIL>
        technicalOwner: Isuru Boyagane
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 38
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 0887bb4e-f6ef-4414-b24f-6b47a9a1cf54
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 680a3cb909019922a10c2c81
    apiInfo:
      id: 680a3cb909019922a10c2c81
      name: copilot-feedback-collector
      displayName: Copilot Feedback Collector
      description: This api is used to connect to the Copilot Feedback Collector service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/copilotfeedbackcollector
      version: v1.0
      type: HTTP
      createdTime: "1745501369090"
      provider: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 39
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: e1d274fa-1b75-47ae-8cee-925a0443b43f
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 67c8046dfc952316d8178f8a
    apiInfo:
      id: 67c8046dfc952316d8178f8a
      name: Cost Optimizer
      displayName: Cost Optimizer
      description: Cost optimizer provide the cost insights and recommendations.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/cost-optimizer
      version: v1.0
      type: HTTP
      createdTime: "1741161581228"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://cost-optimizer.choreo-ai:8000/
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 40
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: ff061b31-f9f3-417d-9f2f-d6a545a9ac14
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 67ca8c1946d9173bc9cc0a24
    apiInfo:
      id: 67ca8c1946d9173bc9cc0a24
      name: cost optimizer cp service
      displayName: cost optimizer cp service
      description: This api is used to connect to the cost optimizer cp service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cost-optimizer-cp
      version: v1.0
      type: HTTP
      createdTime: "1741327385172"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 41
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: d07b6b99-ce04-4dce-a5a2-c9daa2e21c7e
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6423c6c318381a2b93e26270
    apiInfo:
      id: 6423c6c318381a2b93e26270
      name: Crypto Key Service
      displayName: Crypto Key Service
      description: Api to expose crypto key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/crypto-key-service
      version: 0.1.0
      type: HTTP
      createdTime: "1680066243461"
      provider: 392560f1-c26f-45f3-89d6-ad0a5cec79af
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 392560f1-c26f-45f3-89d6-ad0a5cec79af
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 42
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 8f7f59b3-aaff-4c01-b232-d92f1925e021
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 61bdd480bd84cd17397efee1
    apiInfo:
      id: 61bdd480bd84cd17397efee1
      name: Insights
      displayName: Insights
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insights
      version: 1.0.0
      type: HTTP
      createdTime: "1639830656122"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "5.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: fcbbb075-1891-4756-ae44-1a9432e13d0a
        vendor: WSO2
      businessInformation:
        businessOwner: Fazlan Nazeem
        businessOwnerEmail: <EMAIL>
        technicalOwner: Fazlan Nazeem
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 43
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: a9330f8d-e55e-4d73-adb2-028ac16b9eb0
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 61bdd3ccbd84cd17397efedf
    apiInfo:
      id: 61bdd3ccbd84cd17397efedf
      name: InsightsAlert
      displayName: InsightsAlert
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insightsalert
      version: 1.0.0
      type: HTTP
      createdTime: "1639830476199"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 7a90890c-9c7d-4f13-86ea-93c74a75f801
        vendor: WSO2
      businessInformation:
        businessOwner: Raveen Rathnayake
        businessOwnerEmail: <EMAIL>
        technicalOwner: Raveen Rathnayake
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 44
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 0a614c9f-423b-4fb6-9ad4-43ffd5ab5d48
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 667e7240a3a9912fa3455193
    apiInfo:
      id: 667e7240a3a9912fa3455193
      name: Milvus Proxy
      displayName: Milvus Proxy
      description: API to interact with the milvus vector DB
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy
      version: v1.0
      type: HTTP
      createdTime: "1719562816821"
      provider: 278ca2f9-1c2e-44f8-8966-eacb35954a99
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 278ca2f9-1c2e-44f8-8966-eacb35954a99
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 45
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: b59f5e5a-5369-43fc-8844-114fb52cd4ba
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 648031628219b95767c68caa
    apiInfo:
      id: 648031628219b95767c68caa
      name: Moesif Key
      displayName: Moesif Key
      description: This api is used to connect to the Moesif Key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/moesif-key
      version: 0.1.0
      type: HTTP
      createdTime: "1686122850577"
      provider: 2f146911-4a19-4906-9109-bcd6e60feba1
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2f146911-4a19-4906-9109-bcd6e60feba1
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 46
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 25953a46-a6ec-40de-8648-69eaf7249e7d
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 61bd9a5d6c70d4270abf41b3
    apiInfo:
      id: 61bd9a5d6c70d4270abf41b3
      name: ObservabilityApplication
      displayName: ObservabilityApplication
      description: Choreo control plane Observability API for fetching application level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/application
      version: 0.1.0
      type: HTTP
      createdTime: "1639815773440"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 47
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 0cf673a9-0da3-44ba-858a-af9d96bb4667
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 61bd9d156c70d4270abf41b5
    apiInfo:
      id: 61bd9d156c70d4270abf41b5
      name: ObservabilityLogging
      displayName: ObservabilityLogging
      description: Choreo control plane Observability API for fetching logs of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/logging
      version: 0.1.0
      type: HTTP
      createdTime: "1639816469410"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 48
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 8e2bc04b-5386-4ea6-8121-3aef32ce7a05
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 61bd9d9482ae4a5211ea7924
    apiInfo:
      id: 61bd9d9482ae4a5211ea7924
      name: ObservabilitySystem
      displayName: ObservabilitySystem
      description: Choreo control plane Observability API for fetching system level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/system
      version: 0.1.0
      type: HTTP
      createdTime: "1639816596688"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 49
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 95d3e00e-6f7d-4674-a997-cb20b1d1eb57
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 6423d5729382a77e3d50c19e
    apiInfo:
      id: 6423d5729382a77e3d50c19e
      name: OnPremKey Management
      displayName: OnPremKey Management
      description: This is the Choreo APIInsights AccessKeys API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/onprem-key-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1680070002236"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 50
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 79141070-9508-4c09-b686-d723540b385e
    applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
    apiId: 62c3ce68f4ea5a773534c0b8
    apiInfo:
      id: 62c3ce68f4ea5a773534c0b8
      name: Organization Management
      displayName: Organization Management
      description: |-
        Owned by Org-Mgt team

        Repo: https://github.com/wso2-enterprise/choreo-organization-management
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/org-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1656999528217"
      provider: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 8bbe075d-2a26-44ba-bfdd-549da39c35d0
      name: PAT App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for personal access tokens(PATs)
      status: APPROVED
      groups: []
      subscriptionCount: 51
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729065454523"
      updatedTime: "1729065454523"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
