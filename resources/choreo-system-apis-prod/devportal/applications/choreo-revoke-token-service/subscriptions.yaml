subscriptions:
  - subscriptionId: 3e8396f4-419b-4f31-a07d-dbf8b248db33
    applicationId: cf0578ed-6e6c-41c7-9442-a585e1a27391
    apiId: 67adf68adff4a5334bdd64ff
    apiInfo:
      id: 67adf68adff4a5334bdd64ff
      name: Choreo Token Revocation Service
      displayName: Choreo Token Revocation Service
      description: API for the Choreo Token Revocation Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-token-revocation-service
      version: v1.0
      type: HTTP
      createdTime: "1739454090685"
      provider: a579828c-19e4-4406-92f4-15df9033c2fc
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: a579828c-19e4-4406-92f4-15df9033c2fc
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: cf0578ed-6e6c-41c7-9442-a585e1a27391
      name: Choreo Revoke Token Service
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for the Choreo Revoke Token Service
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: a579828c-19e4-4406-92f4-15df9033c2fc
      createdTime: "1739456255200"
      updatedTime: "1739456255200"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
