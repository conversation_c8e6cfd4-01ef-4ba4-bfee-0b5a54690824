{"applicationId": "6ee0f767-50a3-404c-a243-25608b015a70", "name": "<PERSON><PERSON><PERSON> Console", "throttlingPolicy": "Unlimited", "description": "Choreo Console Application", "tokenType": "JWT", "status": "APPROVED", "groups": [], "subscriptionCount": 50, "keys": [], "attributes": {"isDpSystemApp": "true", "scopes": ""}, "subscriptionScopes": [{"key": "urn:choreosystem:usermanagement:invitation_view", "name": "invitation_view", "roles": [], "description": "View invitation"}, {"key": "urn:choreosystem:organizationmanagement:self_signup_config_update", "name": "urn:choreosystem:organizationmanagement:self_signup_config_update", "roles": [], "description": ""}, {"key": "urn:choreosystem:configmanagement:global_config_delete", "name": "global_config_delete", "roles": [], "description": "Delete global configuration"}, {"key": "urn:choreosystem:organizationmanagement:self_signup_approval_view", "name": "urn:choreosystem:organizationmanagement:self_signup_approval_view", "roles": [], "description": ""}, {"key": "urn:choreosystem:usermanagement:role_create", "name": "role_create", "roles": [], "description": "Create roles"}, {"key": "urn:choreosystem:organizationmanagement:theme_create", "name": "urn:choreosystem:organizationmanagement:theme_create", "roles": [], "description": ""}, {"key": "urn:choreosystem:usermanagement:invitation_delete", "name": "invitation_delete", "roles": [], "description": "Delete invitation"}, {"key": "urn:choreosystem:componentsmanagement:component_manage", "name": "component_manage", "roles": [], "description": "Manage component"}, {"key": "urn:choreosystem:configmanagement:config_create", "name": "config_create", "roles": [], "description": "Create configuration"}, {"key": "urn:choreosystem:configmanagement:global_config_update", "name": "global_config_update", "roles": [], "description": "Update global configuration"}, {"key": "urn:choreosystem:configmanagement:global_config_view", "name": "global_config_view", "roles": [], "description": "View global configuration"}, {"key": "choreo:domain_manage", "name": "choreo:domain_manage", "roles": [], "description": "Manage Custom Domains"}, {"key": "urn:choreosystem:organizationmanagement:self_signup_config_view", "name": "urn:choreosystem:organizationmanagement:self_signup_config_view", "roles": [], "description": ""}, {"key": "urn:choreosystem:usermanagement:user_update", "name": "user_update", "roles": [], "description": "Update users"}, {"key": "urn:choreosystem:usermanagement:role_mapping_delete", "name": "role_mapping_delete", "roles": [], "description": "Delete group role mapping"}, {"key": "urn:choreosystem:organizationmanagement:self_signup_manage", "name": "urn:choreosystem:organizationmanagement:self_signup_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:organizationmanagement:enterprise_login_config_manage", "name": "urn:choreosystem:organizationmanagement:enterprise_login_config_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:componentsmanagement:component_logs_view", "name": "component_logs_view", "roles": [], "description": "View component logs"}, {"key": "urn:choreosystem:organizationmanagement:enterprise_login_config_view", "name": "urn:choreosystem:organizationmanagement:enterprise_login_config_view", "roles": [], "description": ""}, {"key": "urn:choreosystem:usermanagement:user_manage", "name": "user_manage", "roles": [], "description": "Manage users"}, {"key": "urn:choreosystem:componentsmanagement:component_create", "name": "component_create", "roles": [], "description": "Create component"}, {"key": "urn:choreosystem:organizationmanagement:theme_view", "name": "urn:choreosystem:organizationmanagement:theme_view", "roles": [], "description": ""}, {"key": "choreo:url_mapping_manage", "name": "choreo:url_mapping_manage", "roles": [], "description": "Manage URL Mappings"}, {"key": "choreo:url_mapping_approve", "name": "choreo:url_mapping_approve", "roles": [], "description": "Approve URL Mappings"}, {"key": "urn:choreosystem:componentsmanagement:component_file_view", "name": "component_file_view", "roles": [], "description": "View component file"}, {"key": "urn:choreosystem:organizationmanagement:theme_delete", "name": "urn:choreosystem:organizationmanagement:theme_delete", "roles": [], "description": ""}, {"key": "urn:choreosystem:organizationmanagement:theme_manage", "name": "urn:choreosystem:organizationmanagement:theme_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:componentsmanagement:component_init_view", "name": "component_init_view", "roles": [], "description": "View component init status"}, {"key": "urn:choreosystem:componentutils:component_trigger", "name": "component_trigger", "roles": [], "description": "Trigger component"}, {"key": "urn:choreosystem:organizationmanagement:self_signup_approval_update", "name": "urn:choreosystem:organizationmanagement:self_signup_approval_update", "roles": [], "description": ""}, {"key": "urn:choreosystem:choreodevopsportalapi:component_manage", "name": "component_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:usermanagement:role_mapping_view", "name": "role_mapping_view", "roles": [], "description": "View group role mapping"}, {"key": "urn:choreosystem:choreodevopsportalapi:deployment_view", "name": "deployment_view", "roles": [], "description": ""}, {"key": "urn:choreosystem:onpremkeymanagement:on_prem_key_manage", "name": "on_prem_key_manage", "roles": [], "description": "Manage on prem key"}, {"key": "urn:choreosystem:usermanagement:role_update", "name": "role_update", "roles": [], "description": "Update roles"}, {"key": "choreo:url_mapping_view", "name": "choreo:url_mapping_view", "roles": [], "description": "View URL Mappings"}, {"key": "urn:choreosystem:organizationapi:org_manage", "name": "org_manage", "roles": [], "description": "Manage organizations"}, {"key": "urn:choreosystem:configmanagement:config_delete", "name": "config_delete", "roles": [], "description": "Delete configuration"}, {"key": "urn:choreosystem:configmanagement:config_manage", "name": "config_manage", "roles": [], "description": "Manage configuration"}, {"key": "urn:choreosystem:onpremkeymanagement:on_prem_key_delete", "name": "on_prem_key_delete", "roles": [], "description": "Delete on prem key"}, {"key": "choreo:domain_view", "name": "choreo:domain_view", "roles": [], "description": "View Custom Domains"}, {"key": "urn:choreosystem:usermanagement:role_mapping_manage", "name": "role_mapping_manage", "roles": [], "description": "Manage group role mapping"}, {"key": "urn:choreosystem:onpremkeymanagement:on_prem_key_update", "name": "on_prem_key_update", "roles": [], "description": "Update on prem key"}, {"key": "urn:choreosystem:choreodevopsportalapi:deployment_manage", "name": "deployment_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:componentsmanagement:component_trigger", "name": "component_trigger", "roles": [], "description": "Trigger component"}, {"key": "urn:choreosystem:configmanagement:config_view", "name": "config_view", "roles": [], "description": "View configuration"}, {"key": "urn:choreosystem:usermanagement:role_manage", "name": "role_manage", "roles": [], "description": "Manage roles"}, {"key": "urn:choreosystem:usermanagement:role_view", "name": "role_view", "roles": [], "description": "View roles"}, {"key": "urn:choreosystem:usermanagement:user_view", "name": "user_view", "roles": [], "description": "View user"}, {"key": "urn:choreosystem:usermanagement:role_mapping_update", "name": "role_mapping_update", "roles": [], "description": "Update group role mapping"}, {"key": "urn:choreosystem:choreoauditloggingapi:audit_logs_manage", "name": "audit_logs_manage", "roles": [], "description": "Manage audit logs"}, {"key": "urn:choreosystem:usermanagement:user_delete", "name": "user_delete", "roles": [], "description": "Create user"}, {"key": "urn:choreosystem:componentsmanagement:component_config_view", "name": "component_config_view", "roles": [], "description": "View component configs"}, {"key": "urn:choreosystem:usermanagement:invitation_manage", "name": "invitation_manage", "roles": [], "description": "Manage invitation"}, {"key": "urn:choreosystem:configmanagement:global_config_manage", "name": "global_config_manage", "roles": [], "description": "Manage global configuration"}, {"key": "choreo:component_manage", "name": "choreo:component_manage", "roles": [], "description": "Manage component"}, {"key": "urn:choreosystem:onpremkeymanagement:on_prem_key_view", "name": "on_prem_key_view", "roles": [], "description": "View on prem key"}, {"key": "urn:choreosystem:usermanagement:role_mapping_create", "name": "role_mapping_create", "roles": [], "description": "Create group role mapping"}, {"key": "urn:choreosystem:choreoauditloggingapi:audit_logs_view", "name": "audit_logs_view", "roles": [], "description": "View audit logs"}, {"key": "urn:choreosystem:componentutils:component_manage", "name": "component_manage", "roles": [], "description": "Manage component"}, {"key": "urn:choreosystem:configmanagement:global_config_create", "name": "global_config_create", "roles": [], "description": "Create global configuration"}, {"key": "urn:choreosystem:organizationmanagement:theme_deploy", "name": "urn:choreosystem:organizationmanagement:theme_deploy", "roles": [], "description": ""}, {"key": "urn:choreosystem:usermanagement:invitation_send", "name": "invitation_send", "roles": [], "description": "Send invitation"}, {"key": "urn:choreosystem:onpremkeymanagement:on_prem_key_create", "name": "on_prem_key_create", "roles": [], "description": "Create on prem key"}, {"key": "urn:choreosystem:usermanagement:role_delete", "name": "role_delete", "roles": [], "description": "Delete roles"}], "owner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "ownerInfo": {"id": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "displayName": "Choreo System", "email": "<EMAIL>"}, "sharedPermissions": ["SHARE", "WRITE", "SUBSCRIBE", "READ"], "sharedWith": [], "hashEnabled": false, "createdTime": "1639814619107", "updatedTime": "1695025140597"}