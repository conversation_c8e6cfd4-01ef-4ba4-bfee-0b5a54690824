subscriptions:
  - subscriptionId: fa8ffe8c-054a-4223-9774-e444f2d5ad11
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 662f36a6e23797639415e2ba
    apiInfo:
      id: 662f36a6e23797639415e2ba
      name: AI Copilot
      displayName: AI Copilot
      description: AI Copilot service that provide chat experience in Chore Console.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/aicopilot
      version: v1.0
      type: HTTP
      createdTime: "1714370214962"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://ai-copilot.choreo-ai:9090/
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 662f36a6e23797639415e2ba
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-04-29 06:05:19.957"
  - subscriptionId: b67cd54d-8d77-4b1e-8075-dc080032bfb0
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 62cd4f3fafa3f22aadee5e8f
    apiInfo:
      id: 62cd4f3fafa3f22aadee5e8f
      name: Alert Configuration Service
      displayName: Alert Configuration Service
      description: This api is used to connect to the Alert Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/alert-configuration-service
      version: 1.0.0
      type: HTTP
      createdTime: "1657622335295"
      provider: 531b4953-bc28-40fb-8794-41834bd55438
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 531b4953-bc28-40fb-8794-41834bd55438
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-07-15 05:22:32.397"
  - subscriptionId: 90532be4-3f88-491f-8131-77cceb479ebf
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 670768cb2584260c4314ce42
    apiInfo:
      id: 670768cb2584260c4314ce42
      name: API Key Service
      displayName: API Key Service
      description: This api is used to API Key management operations
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/api-key-service
      version: v1.0
      type: HTTP
      createdTime: "1728538827190"
      provider: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 670768cb2584260c4314ce42
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-10 08:40:00.587"
  - subscriptionId: e242726c-c1c4-4e3f-bc96-0fdb26f53333
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 65448ed2be658723011579a7
    apiInfo:
      id: 65448ed2be658723011579a7
      name: APIM AppDev APIs
      displayName: APIM AppDev APIs
      description: This api is used to connect to the APIM AppDev APIs service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/apim-appdev
      version: v1.0
      type: HTTP
      createdTime: "1698991826606"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 65448ed2be658723011579a7
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-11-03 06:36:53.717"
  - subscriptionId: 385f7118-d308-409f-b08f-a66a5d3d1aae
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 686df722d34caf156680dfe7
    apiInfo:
      id: 686df722d34caf156680dfe7
      name: Automation Pipelines API
      displayName: Automation Pipelines API
      description: API for managing automation pipelines.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/automation-pipelines
      version: v1.0
      type: HTTP
      createdTime: "1752037154351"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 686df722d34caf156680dfe7
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-07-09 06:00:46.42"
  - subscriptionId: 0e6a1394-73e7-478d-8720-fc0dcc6a305d
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 67c5307b75ae805502c72267
    apiInfo:
      id: 67c5307b75ae805502c72267
      name: Build Pipeline Service
      displayName: Build Pipeline Service
      description: API for the Build Pipeline Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipeline-service
      version: 1.0.0
      type: HTTP
      createdTime: "1740976251631"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-03-03 05:19:02.777"
  - subscriptionId: 5e70d6e8-551a-4a87-a2e5-1bf6d97954a7
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 67c9287304e65a6a11828e40
    apiInfo:
      id: 67c9287304e65a6a11828e40
      name: Build Pipelines
      displayName: Build Pipelines
      description: API for the Build Pipelines.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipelines
      version: v1.0
      type: HTTP
      createdTime: "1741236339290"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 67c9287304e65a6a11828e40
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-03-06 05:00:58.397"
  - subscriptionId: fb340025-184f-4cb7-a170-8c1add4854b0
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 654489dabe6587230115797a
    apiInfo:
      id: 654489dabe6587230115797a
      name: Built-in IdP User Management
      displayName: Built-in IdP User Management
      description: This is the RESTful API for Choreo Resident IdP User Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-store-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698990554639"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 654489dabe6587230115797a
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-11-03 06:36:56.28"
  - subscriptionId: cc7806f0-d5a7-4a57-8918-254c67fe0e5e
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 65448bbbbe6587230115797d
    apiInfo:
      id: 65448bbbbe6587230115797d
      name: Built-in STS Authorization Service
      displayName: Built-in STS Authorization Service
      description: |
        This API specification outlines the endpoints and operations for the Authorization Service.  It enables users to handle roles, role-permission mappings, role-group mappings, and process authorization requests.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/authz-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698991035696"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 65448bbbbe6587230115797d
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-11-03 06:37:11.69"
  - subscriptionId: 4d56dd22-a1ab-4d7e-aad0-3948a9913988
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 633be0a544f40d1b459e5a9f
    apiInfo:
      id: 633be0a544f40d1b459e5a9f
      name: Choreo Admin API
      displayName: Choreo Admin API
      description: |
        This is a RESTFul API to perform Administrative operations in Choreo
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api
      version: 1.0.0
      type: HTTP
      createdTime: "1664868517194"
      provider: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-10-04 17:26:01.697"
  - subscriptionId: 3c6c1f8f-e192-42ef-8cc3-d758021b3330
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 63a447d4afc39e72d98ea2be
    apiInfo:
      id: 63a447d4afc39e72d98ea2be
      name: Choreo AI Test Assistant
      displayName: Choreo AI Test Assistant
      description: |
        The Test Assistant API provides example payload data for testing Choreo components.
        It considers the payload schemas of the Open API specification and generates sample data that conforms to the
        schema and enriches the specification with the generated examples.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/ai-test-assistant
      version: 1.0.0
      type: HTTP
      createdTime: "1671710676949"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-12-22 12:10:04.15"
  - subscriptionId: 6f7e95ab-e3b7-48b4-8b85-3a819f259651
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 67289e13d90467127752125e
    apiInfo:
      id: 67289e13d90467127752125e
      name: Choreo Appdev STS Management Service
      displayName: Choreo Appdev STS Management Service
      description: API for the Choreo Appdev STS Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-appdev-sts-management-service
      version: v1.0
      type: HTTP
      createdTime: "1730715155674"
      provider: a579828c-19e4-4406-92f4-15df9033c2fc
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: a579828c-19e4-4406-92f4-15df9033c2fc
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 67289e13d90467127752125e
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-11-05 07:55:50.597"
  - subscriptionId: a583bd75-fce3-4b2c-ab04-06f9bb958503
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6757f66a542ddb34b244bb53
    apiInfo:
      id: 6757f66a542ddb34b244bb53
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: v1.0
      type: HTTP
      createdTime: "1733817962976"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6757f66a542ddb34b244bb53
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-12-11 09:37:53.42"
  - subscriptionId: 32e02393-7f91-4652-b20c-d495d0f4d889
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6482e60403e62408c6c8be6e
    apiInfo:
      id: 6482e60403e62408c6c8be6e
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: 1.0.0
      type: HTTP
      createdTime: "1686300164732"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-06-16 11:25:47.347"
  - subscriptionId: dd60949d-c67e-4a91-8c84-b1ddb834e68a
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 631aa4195fb46c53f7971f88
    apiInfo:
      id: 631aa4195fb46c53f7971f88
      name: Choreo DevOps Portal API
      displayName: Choreo DevOps Portal API
      description: This api is used to connect to the Choreo DevOps Portal API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/devops
      version: 1.0.0
      type: HTTP
      createdTime: "1662690329318"
      provider: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-12-02 05:27:29.257"
  - subscriptionId: 16d81c96-ea61-4e10-a919-add771c169de
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 66f15212fe856218b0c32c84
    apiInfo:
      id: 66f15212fe856218b0c32c84
      name: Choreo Governance Service API
      displayName: Choreo Governance Service API
      description: API for governing Choreo APIs/Components and other resources.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/governance
      version: v1.0
      type: HTTP
      createdTime: "1727091218612"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 66f15212fe856218b0c32c84
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-09-23 12:27:26.983"
  - subscriptionId: 0722e0e3-08fe-4ba3-a30d-859bfee15298
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 650142320a6377400ddc4a99
    apiInfo:
      id: 650142320a6377400ddc4a99
      name: Choreo TestGPT API
      displayName: Choreo TestGPT API
      description: API for using Choreo TestGPT for API testing.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreotestgpt
      version: 0.1.0
      type: HTTP
      createdTime: "1694581298180"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://testgpt.choreo-ai.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-09-13 06:34:28.943"
  - subscriptionId: 2cf16809-0f34-4ac0-b996-b38008333f43
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 63f89b690ba4773450505c81
    apiInfo:
      id: 63f89b690ba4773450505c81
      name: CIO Incident Configurator
      displayName: CIO Incident Configurator
      description: This API will act as the configurator for CIO dashboard. API will store configuration  details required for incident scraper and serve them as a REST API on demand
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-incident-configurator
      version: 1.0.0
      type: HTTP
      createdTime: "1677237097935"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-02-24 11:14:43.56"
  - subscriptionId: 4ab5fd80-58ee-4831-92c2-bae7b161dc9d
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 63a2d42a17a2541a07342aeb
    apiInfo:
      id: 63a2d42a17a2541a07342aeb
      name: CIO Query API
      displayName: CIO Query API
      description: This api is used to connect to the CIO Query API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-query-api
      version: 1.0.0
      type: HTTP
      createdTime: "1671615530332"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-12-21 09:41:00.403"
  - subscriptionId: 105fc8cd-5d89-48f8-9766-c120071e1276
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 65a108071b4376654a0141ef
    apiInfo:
      id: 65a108071b4376654a0141ef
      name: Component Creation Status
      displayName: Component Creation Status
      description: This api is used to connect to the Component Creation Status service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-creation
      version: v1.0
      type: HTTP
      createdTime: "1705052167906"
      provider: 51c0ae66-b0cd-42b2-90b5-2605f856bf24
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 51c0ae66-b0cd-42b2-90b5-2605f856bf24
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 65a108071b4376654a0141ef
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-01-12 10:53:58.823"
  - subscriptionId: 0616a4c8-ba93-4e25-8289-9d382f4a1930
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 670e3f6055f33d048d031340
    apiInfo:
      id: 670e3f6055f33d048d031340
      name: Component Utils
      displayName: Component Utils
      description: This is Component Management Utils API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils
      version: 1.0.0
      type: HTTP
      createdTime: "1728986976365"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-15 10:35:00.873"
  - subscriptionId: b0079f8b-a1b3-4469-ad5c-1096f0faa99a
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 645c87bf29258d5e5cd9125d
    apiInfo:
      id: 645c87bf29258d5e5cd9125d
      name: Components Management
      displayName: Components Management
      description: This is the Choreo Components Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1683785663821"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-05-11 06:37:34.783"
  - subscriptionId: 1c5c20b5-7223-4ff4-900c-79fad8162e24
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 64140b1f0b438a1df721516c
    apiInfo:
      id: 64140b1f0b438a1df721516c
      name: Config Management
      displayName: Config Management
      description: This is the Choreo Global Configuration Service API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1679035167851"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-03-22 09:41:06.633"
  - subscriptionId: 4f57183a-2de8-40a1-8db9-ba0d3f12f2d5
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 654350269e53c83b329ab097
    apiInfo:
      id: 654350269e53c83b329ab097
      name: Configuration Mapping Service
      displayName: Configuration Mapping Service
      description: This api is used to connect to the Configuration Mapping Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910246275"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 654350269e53c83b329ab097
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-11-02 08:45:00.373"
  - subscriptionId: e7fcfcd5-740b-42b3-aac2-851fcb0f1bce
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 67038cf4f86e5c6a5a320084
    apiInfo:
      id: 67038cf4f86e5c6a5a320084
      name: Configuration Schema Service
      displayName: Configuration Schema Service
      description: This api is used to connect to the Configuration Schema Service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/configuration-schema
      version: v1.0
      type: HTTP
      createdTime: "1728285940200"
      provider: 8e923942-ae07-4a50-a426-3c6467624ee4
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 8e923942-ae07-4a50-a426-3c6467624ee4
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 67038cf4f86e5c6a5a320084
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-03-10 07:31:25.833"
  - subscriptionId: e64afbee-3a96-4bfe-b1f6-1e291282951c
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6543529f9e53c83b329ab0a5
    apiInfo:
      id: 6543529f9e53c83b329ab0a5
      name: Configuration Service
      displayName: Configuration Service
      description: This api is used to connect to the Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910879023"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6543529f9e53c83b329ab0a5
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-11-02 08:43:51.083"
  - subscriptionId: 97687405-31f0-4dcc-ad70-0e6573d5a046
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6544d9e29a886f6d1afddd22
    apiInfo:
      id: 6544d9e29a886f6d1afddd22
      name: Connection Management
      displayName: Connection Management
      description: This api is used to connect to the Connection Management service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections
      version: v1.0
      type: HTTP
      createdTime: "1699011042225"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://connection-service.prod-choreo-system:9000/connections/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6544d9e29a886f6d1afddd22
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-11-03 11:33:52.503"
  - subscriptionId: 97d8ef4b-b017-413a-b864-7abb4d39341a
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 61bdd47abd84cd17397efee0
    apiInfo:
      id: 61bdd47abd84cd17397efee0
      name: ConnectorBuilder
      displayName: ConnectorBuilder
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connectorbuilder
      version: 0.1.0
      type: HTTP
      createdTime: "1639830650431"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: e859aabe-1642-48ff-845e-a631303fd9e7
        vendor: WSO2
      businessInformation:
        businessOwner: Isuru Boyagane
        businessOwnerEmail: <EMAIL>
        technicalOwner: Isuru Boyagane
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2021-12-18 12:35:51.927"
  - subscriptionId: b435641f-a1d8-42c2-b537-4d2f6953b4bd
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 680a3cb909019922a10c2c81
    apiInfo:
      id: 680a3cb909019922a10c2c81
      name: copilot-feedback-collector
      displayName: Copilot Feedback Collector
      description: This api is used to connect to the Copilot Feedback Collector service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/copilotfeedbackcollector
      version: v1.0
      type: HTTP
      createdTime: "1745501369090"
      provider: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 680a3cb909019922a10c2c81
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-04-25 05:32:06.36"
  - subscriptionId: 50e5d8ea-f31c-4f1a-aec7-edfce856bb0f
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 67c8046dfc952316d8178f8a
    apiInfo:
      id: 67c8046dfc952316d8178f8a
      name: Cost Optimizer
      displayName: Cost Optimizer
      description: Cost optimizer provide the cost insights and recommendations.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/cost-optimizer
      version: v1.0
      type: HTTP
      createdTime: "1741161581228"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://cost-optimizer.choreo-ai:8000/
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 67c8046dfc952316d8178f8a
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-03-05 08:10:44.947"
  - subscriptionId: 87670b07-e213-4b91-a0eb-1fc18736110e
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 67ca8c1946d9173bc9cc0a24
    apiInfo:
      id: 67ca8c1946d9173bc9cc0a24
      name: cost optimizer cp service
      displayName: cost optimizer cp service
      description: This api is used to connect to the cost optimizer cp service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cost-optimizer-cp
      version: v1.0
      type: HTTP
      createdTime: "1741327385172"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 67ca8c1946d9173bc9cc0a24
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-03-07 09:09:13.363"
  - subscriptionId: b010e861-3e50-493d-b1f9-e79abfc1aae4
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6423c6c318381a2b93e26270
    apiInfo:
      id: 6423c6c318381a2b93e26270
      name: Crypto Key Service
      displayName: Crypto Key Service
      description: Api to expose crypto key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/crypto-key-service
      version: 0.1.0
      type: HTTP
      createdTime: "1680066243461"
      provider: 392560f1-c26f-45f3-89d6-ad0a5cec79af
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 392560f1-c26f-45f3-89d6-ad0a5cec79af
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-03-29 06:50:55.723"
  - subscriptionId: c67fbd10-1838-4853-9755-5aa26dd67235
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6541ed3532503454e1085aa3
    apiInfo:
      id: 6541ed3532503454e1085aa3
      name: Declarative API
      displayName: Declarative API
      description: This api is used to connect to the Declarative API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/declarative-api
      version: v1.0
      type: HTTP
      createdTime: "1698819381865"
      provider: 5072771b-6e31-41d3-8964-2f4fb991d6e3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://declarative-api.prod-choreo-system:8080/api
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5072771b-6e31-41d3-8964-2f4fb991d6e3
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6541ed3532503454e1085aa3
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-11-01 08:49:47.707"
  - subscriptionId: 3601577e-2116-4699-a7e7-f723a5b46807
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 61bdd480bd84cd17397efee1
    apiInfo:
      id: 61bdd480bd84cd17397efee1
      name: Insights
      displayName: Insights
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insights
      version: 1.0.0
      type: HTTP
      createdTime: "1639830656122"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "5.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: fcbbb075-1891-4756-ae44-1a9432e13d0a
        vendor: WSO2
      businessInformation:
        businessOwner: Fazlan Nazeem
        businessOwnerEmail: <EMAIL>
        technicalOwner: Fazlan Nazeem
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2021-12-18 12:35:58.997"
  - subscriptionId: eebec7f2-c853-43b2-9a9b-986e275b8e61
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 61bdd3ccbd84cd17397efedf
    apiInfo:
      id: 61bdd3ccbd84cd17397efedf
      name: InsightsAlert
      displayName: InsightsAlert
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insightsalert
      version: 1.0.0
      type: HTTP
      createdTime: "1639830476199"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 7a90890c-9c7d-4f13-86ea-93c74a75f801
        vendor: WSO2
      businessInformation:
        businessOwner: Raveen Rathnayake
        businessOwnerEmail: <EMAIL>
        technicalOwner: Raveen Rathnayake
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2021-12-18 12:35:53.87"
  - subscriptionId: 6a2ce7cf-0971-409f-9dcf-c2b86751a000
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6486b08be88a407b4f8f5ff1
    apiInfo:
      id: 6486b08be88a407b4f8f5ff1
      name: Marketplace
      displayName: Marketplace
      description: This api is used to connect to the Marketplace service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/marketplace
      version: 0.1.0
      type: HTTP
      createdTime: "1686548619356"
      provider: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-06-13 15:49:07.537"
  - subscriptionId: 70e61a1a-c483-4db3-bc15-dd6744d33486
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 667e7240a3a9912fa3455193
    apiInfo:
      id: 667e7240a3a9912fa3455193
      name: Milvus Proxy
      displayName: Milvus Proxy
      description: API to interact with the milvus vector DB
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy
      version: v1.0
      type: HTTP
      createdTime: "1719562816821"
      provider: 278ca2f9-1c2e-44f8-8966-eacb35954a99
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 278ca2f9-1c2e-44f8-8966-eacb35954a99
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 667e7240a3a9912fa3455193
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-06-28 08:25:09.677"
  - subscriptionId: 21b0f66d-6ed2-435c-9f86-73d0b3c48e9e
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 648031628219b95767c68caa
    apiInfo:
      id: 648031628219b95767c68caa
      name: Moesif Key
      displayName: Moesif Key
      description: This api is used to connect to the Moesif Key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/moesif-key
      version: 0.1.0
      type: HTTP
      createdTime: "1686122850577"
      provider: 2f146911-4a19-4906-9109-bcd6e60feba1
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2f146911-4a19-4906-9109-bcd6e60feba1
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-06-07 12:12:56.467"
  - subscriptionId: a26919e0-710d-4f3b-a11f-065e5851c946
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 64b4e04e6f73c71fa9969691
    apiInfo:
      id: 64b4e04e6f73c71fa9969691
      name: Observability Manager
      displayName: Observability Manager
      description: This api is used to connect to the Observability Manager service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager
      version: 0.1.0
      type: HTTP
      createdTime: "1689575502411"
      provider: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Silver
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-07-21 05:50:36.37"
  - subscriptionId: be9571e8-df77-4bd0-93c1-01839641248b
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 61bd9a5d6c70d4270abf41b3
    apiInfo:
      id: 61bd9a5d6c70d4270abf41b3
      name: ObservabilityApplication
      displayName: ObservabilityApplication
      description: Choreo control plane Observability API for fetching application level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/application
      version: 0.1.0
      type: HTTP
      createdTime: "1639815773440"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2021-12-18 11:27:44.803"
  - subscriptionId: 686d6b28-0e76-4bb9-a2e5-b0ba5ad19432
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 61bd9d156c70d4270abf41b5
    apiInfo:
      id: 61bd9d156c70d4270abf41b5
      name: ObservabilityLogging
      displayName: ObservabilityLogging
      description: Choreo control plane Observability API for fetching logs of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/logging
      version: 0.1.0
      type: HTTP
      createdTime: "1639816469410"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2021-12-18 11:27:46.383"
  - subscriptionId: fd9c1aa6-af77-4817-8f7d-0843a53beee1
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 61bd9d9482ae4a5211ea7924
    apiInfo:
      id: 61bd9d9482ae4a5211ea7924
      name: ObservabilitySystem
      displayName: ObservabilitySystem
      description: Choreo control plane Observability API for fetching system level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/system
      version: 0.1.0
      type: HTTP
      createdTime: "1639816596688"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2021-12-18 11:27:47.76"
  - subscriptionId: 8f797146-1584-4ce9-8126-9eb755d11a7a
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6423d5729382a77e3d50c19e
    apiInfo:
      id: 6423d5729382a77e3d50c19e
      name: OnPremKey Management
      displayName: OnPremKey Management
      description: This is the Choreo APIInsights AccessKeys API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/onprem-key-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1680070002236"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-03-29 06:53:07.077"
  - subscriptionId: 6c4780c0-5718-4b24-a5a6-48b00203b6a9
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 64742bf8240d4717b1131bcc
    apiInfo:
      id: 64742bf8240d4717b1131bcc
      name: Organization API
      displayName: Organization API
      description: This is the Choreo Runtime Organization API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/orgs
      version: 1.0.0
      type: HTTP
      createdTime: "1685335032726"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-07-04 09:00:58.087"
  - subscriptionId: 21d9580b-dc3e-4131-9ae3-66e51cbd415a
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 62c3ce68f4ea5a773534c0b8
    apiInfo:
      id: 62c3ce68f4ea5a773534c0b8
      name: Organization Management
      displayName: Organization Management
      description: |-
        Owned by Org-Mgt team

        Repo: https://github.com/wso2-enterprise/choreo-organization-management
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/org-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1656999528217"
      provider: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: null
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-07-05 09:37:51.517"
  - subscriptionId: 081708d8-95df-4486-acd9-4fac86d3b498
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 6541172854bcdb21d2a334e4
    apiInfo:
      id: 6541172854bcdb21d2a334e4
      name: Platform Services Manager API
      displayName: Platform Services Manager API
      description: This api is used to connect to the Platform Services Manager API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/platform-services
      version: v1.0
      type: HTTP
      createdTime: "1698764584558"
      provider: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://platform-services-manager.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6541172854bcdb21d2a334e4
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-10-31 15:24:00.917"
  - subscriptionId: 7968cdb5-8645-4a06-80dd-3ecbcfd98b3b
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 62419cc7690bbb0c8c544443
    apiInfo:
      id: 62419cc7690bbb0c8c544443
      name: Project API
      displayName: Project API
      description: This api is used to connect to the Project API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/projects
      version: 1.0.0
      type: HTTP
      createdTime: "1648467143810"
      provider: 2d92c502-cb6c-411e-865d-e25dace20279
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2d92c502-cb6c-411e-865d-e25dace20279
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: null
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-03-29 08:13:59.01"
  - subscriptionId: 1daaf91d-c996-4345-af0d-0f64c9b8dd1d
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 65e82d435e87d93a849b8943
    apiInfo:
      id: 65e82d435e87d93a849b8943
      name: URL Management API
      displayName: URL Management API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/url-mgt
      version: v1.0
      type: HTTP
      createdTime: "1709714755963"
      provider: d962d47d-f855-4202-be7b-3b31e391d1f4
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d962d47d-f855-4202-be7b-3b31e391d1f4
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 65e82d435e87d93a849b8943
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-03-06 09:07:29.207"
  - subscriptionId: 52a6e697-6c8e-4143-b11f-aaf95a3ee5df
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 64daf146d5aa8b4bada40cde
    apiInfo:
      id: 64daf146d5aa8b4bada40cde
      name: User Management
      displayName: User Management
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1692070214459"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://api-server.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-08-16 10:00:05.037"
  - subscriptionId: 1900cb3e-4a8f-4410-abe3-e528ea611fa8
    applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
    apiId: 66ea50d5ebf87a2d63485eb1
    apiInfo:
      id: 66ea50d5ebf87a2d63485eb1
      name: Workflow Management API
      displayName: Workflow Management API
      description: Workflow Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/workflow-mgt
      version: v1.0
      type: HTTP
      createdTime: "1726632149432"
      provider: 678ead13-dc75-4004-9fc0-35cd45eddf8f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 678ead13-dc75-4004-9fc0-35cd45eddf8f
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 66ea50d5ebf87a2d63485eb1
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 6ee0f767-50a3-404c-a243-25608b015a70
      name: Choreo Console
      throttlingPolicy: Unlimited
      description: Choreo Console Application
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isDpSystemApp: "true"
      owner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      createdTime: "1639814619107"
      updatedTime: "1695025140597"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-09-18 04:15:10.687"
