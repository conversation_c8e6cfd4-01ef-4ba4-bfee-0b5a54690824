subscriptions:
  - subscriptionId: 785e2d48-f5ae-4247-b290-0202c06a236a
    applicationId: 39a39e56-1816-4c09-b9ca-cc3285b415c7
    apiId: 6878ad817ab2ee657b846112
    apiInfo:
      id: 6878ad817ab2ee657b846112
      name: Cloud Billing Events Service
      displayName: Cloud Billing Events Service
      description: API for managing cloud billing events.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/billing-events
      version: v1.0
      type: HTTP
      createdTime: "1752739201496"
      provider: a579828c-19e4-4406-92f4-15df9033c2fc
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: a579828c-19e4-4406-92f4-15df9033c2fc
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 39a39e56-1816-4c09-b9ca-cc3285b415c7
      name: Cloud Billing Events App
      throttlingPolicy: Unlimited
      description: This application is used for generating token for cloud-billing-api-service WebSubHub subscription
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: a579828c-19e4-4406-92f4-15df9033c2fc
      createdTime: "1752837468953"
      updatedTime: "1752837468953"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
