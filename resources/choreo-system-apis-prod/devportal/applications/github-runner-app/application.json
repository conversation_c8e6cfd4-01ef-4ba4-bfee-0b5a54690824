{"applicationId": "832545e2-da76-4125-b68a-dec300769cd3", "name": "GitHub Runner App", "throttlingPolicy": "Unlimited", "description": "This application is used by GitHub Runner for cicd callback", "tokenType": "JWT", "status": "APPROVED", "groups": [], "subscriptionCount": 2, "keys": [], "attributes": {"isDpSystemApp": "", "scopes": ""}, "subscriptionScopes": [{"key": "urn:choreosystem:componentutils:component_trigger", "name": "component_trigger", "roles": [], "description": "Trigger component"}, {"key": "urn:choreosystem:componentutils:component_manage", "name": "component_manage", "roles": [], "description": "Manage component"}], "owner": "8d664760-7b57-4638-94d9-74e8dc3d7483", "ownerInfo": {"id": "8d664760-7b57-4638-94d9-74e8dc3d7483", "displayName": "<PERSON><PERSON>", "email": "<EMAIL>"}, "sharedPermissions": ["SHARE", "WRITE", "SUBSCRIBE", "READ"], "sharedWith": [], "hashEnabled": false, "createdTime": "1722495529700", "updatedTime": "1722495529700"}