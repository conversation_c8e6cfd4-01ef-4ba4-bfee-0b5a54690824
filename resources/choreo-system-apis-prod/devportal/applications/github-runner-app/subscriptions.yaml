subscriptions:
  - subscriptionId: 94d8f343-aa54-46fa-b3e2-9f7758ea8dfd
    applicationId: 832545e2-da76-4125-b68a-dec300769cd3
    apiId: 66f15212fe856218b0c32c84
    apiInfo:
      id: 66f15212fe856218b0c32c84
      name: Choreo Governance Service API
      displayName: Choreo Governance Service API
      description: API for governing Choreo APIs/Components and other resources.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/governance
      version: v1.0
      type: HTTP
      createdTime: "1727091218612"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 66f15212fe856218b0c32c84
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 832545e2-da76-4125-b68a-dec300769cd3
      name: GitHub Runner App
      throttlingPolicy: Unlimited
      description: This application is used by GitHub Runner for cicd callback
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1722495529700"
      updatedTime: "1722495529700"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-10-22 18:02:09.397"
  - subscriptionId: 14c00dd9-c867-41ff-ad1a-30aad025c464
    applicationId: 832545e2-da76-4125-b68a-dec300769cd3
    apiId: 670e3f6055f33d048d031340
    apiInfo:
      id: 670e3f6055f33d048d031340
      name: Component Utils
      displayName: Component Utils
      description: This is Component Management Utils API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils
      version: 1.0.0
      type: HTTP
      createdTime: "1728986976365"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 832545e2-da76-4125-b68a-dec300769cd3
      name: GitHub Runner App
      throttlingPolicy: Unlimited
      description: This application is used by GitHub Runner for cicd callback
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1722495529700"
      updatedTime: "1722495529700"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-15 10:34:07.543"
