subscriptions:
  - subscriptionId: 7797ae04-fd69-4494-8651-82d20aa64b38
    applicationId: 9e811eae-183f-4fc3-93d2-c39523245de7
    apiId: 67504961d4aa1b404b9d20f9
    apiInfo:
      id: 67504961d4aa1b404b9d20f9
      name: copilot-data-collector
      displayName: Copilot Data Collector
      description: This api is used to connect to the Copilot Data Collector service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/copilotdatacollector
      version: v1.0
      type: HTTP
      createdTime: "1733314913283"
      provider: 678ead13-dc75-4004-9fc0-35cd45eddf8f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 678ead13-dc75-4004-9fc0-35cd45eddf8f
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 9e811eae-183f-4fc3-93d2-c39523245de7
      name: Choreo Copilot
      throttlingPolicy: Unlimited
      description: This application is for data collection API
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 678ead13-dc75-4004-9fc0-35cd45eddf8f
      createdTime: "1733376648870"
      updatedTime: "1733376648870"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
