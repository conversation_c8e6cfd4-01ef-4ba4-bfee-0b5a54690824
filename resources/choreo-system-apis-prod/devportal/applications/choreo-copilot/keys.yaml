keys:
  - keyMappingId: c0e38dff-f5c6-4503-a840-ddbb13a27efd
    keyManager: Resident Key Manager
    consumerKey: Ksor4uxrFc9BLEt14Tp3PkTJ4osa
    consumerSecret: '***'
    supportedGrantTypes:
      - client_credentials
    callbackUrl: ""
    keyState: APPROVED
    keyType: PRODUCTION
    mode: CREATED
    groupId: null
    token:
      accessToken: '***'
      tokenScopes:
        - default
      validityTime: 3600
    additionalProperties:
      id_token_expiry_time: 3600
      application_access_token_expiry_time: 3600
      user_access_token_expiry_time: 3600
      bypassClientCredentials: false
      pkceMandatory: false
      pkceSupportPlain: false
      refresh_token_expiry_time: 86400
