{"applicationId": "01497e74-5925-4c47-9f31-a1e1e612347f", "name": "Site24x7 Monitors App", "throttlingPolicy": "Unlimited", "description": "Credentials generated from this applicaion are configured in the Site24x7. ", "tokenType": "JWT", "status": "APPROVED", "groups": [], "subscriptionCount": 17, "keys": [], "attributes": {"isDpSystemApp": "", "scopes": ""}, "subscriptionScopes": [{"key": "urn:choreosystem:componentsmanagement:component_init_view", "name": "component_init_view", "roles": [], "description": "View component init status"}, {"key": "urn:choreosystem:componentutils:component_trigger", "name": "component_trigger", "roles": [], "description": "Trigger component"}, {"key": "urn:choreosystem:choreodevopsportalapi:component_manage", "name": "component_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:choreodevopsportalapi:deployment_view", "name": "deployment_view", "roles": [], "description": ""}, {"key": "urn:choreosystem:choreoauditloggingapi:audit_logs_manage", "name": "audit_logs_manage", "roles": [], "description": "Manage audit logs"}, {"key": "urn:choreosystem:configmanagement:global_config_delete", "name": "global_config_delete", "roles": [], "description": "Delete global configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_config_view", "name": "component_config_view", "roles": [], "description": "View component configs"}, {"key": "urn:choreosystem:configmanagement:config_delete", "name": "config_delete", "roles": [], "description": "Delete configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_logs_view", "name": "component_logs_view", "roles": [], "description": "View component logs"}, {"key": "urn:choreosystem:configmanagement:config_manage", "name": "config_manage", "roles": [], "description": "Manage configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_create", "name": "component_create", "roles": [], "description": "Create component"}, {"key": "urn:choreosystem:configmanagement:global_config_manage", "name": "global_config_manage", "roles": [], "description": "Manage global configuration"}, {"key": "urn:choreosystem:choreodevopsportalapi:deployment_manage", "name": "deployment_manage", "roles": [], "description": ""}, {"key": "urn:choreosystem:choreoauditloggingapi:audit_logs_view", "name": "audit_logs_view", "roles": [], "description": "View audit logs"}, {"key": "urn:choreosystem:componentsmanagement:component_trigger", "name": "component_trigger", "roles": [], "description": "Trigger component"}, {"key": "urn:choreosystem:componentutils:component_manage", "name": "component_manage", "roles": [], "description": "Manage component"}, {"key": "urn:choreosystem:configmanagement:global_config_create", "name": "global_config_create", "roles": [], "description": "Create global configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_manage", "name": "component_manage", "roles": [], "description": "Manage component"}, {"key": "urn:choreosystem:configmanagement:config_create", "name": "config_create", "roles": [], "description": "Create configuration"}, {"key": "urn:choreosystem:configmanagement:config_view", "name": "config_view", "roles": [], "description": "View configuration"}, {"key": "urn:choreosystem:configmanagement:global_config_update", "name": "global_config_update", "roles": [], "description": "Update global configuration"}, {"key": "urn:choreosystem:componentsmanagement:component_file_view", "name": "component_file_view", "roles": [], "description": "View component file"}, {"key": "urn:choreosystem:configmanagement:global_config_view", "name": "global_config_view", "roles": [], "description": "View global configuration"}], "owner": "da9c10db-d44c-4151-a9b5-aa8300ee280c", "ownerInfo": {"id": "da9c10db-d44c-4151-a9b5-aa8300ee280c", "displayName": "Choreo Uptime", "email": "<EMAIL>"}, "sharedPermissions": ["SHARE", "WRITE", "SUBSCRIBE", "READ"], "sharedWith": [], "hashEnabled": false, "createdTime": "1641789194880", "updatedTime": "1641789194880"}