subscriptions:
  - subscriptionId: a43e28c9-d68a-4824-a1cf-bb2e6df19983
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 6482e60403e62408c6c8be6e
    apiInfo:
      id: 6482e60403e62408c6c8be6e
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: 1.0.0
      type: HTTP
      createdTime: "1686300164732"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-09-20 10:22:18.517"
  - subscriptionId: ccb9af69-c644-4bda-aa27-e31c3c01c466
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 631aa4195fb46c53f7971f88
    apiInfo:
      id: 631aa4195fb46c53f7971f88
      name: Choreo DevOps Portal API
      displayName: Choreo DevOps Portal API
      description: This api is used to connect to the Choreo DevOps Portal API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/devops
      version: 1.0.0
      type: HTTP
      createdTime: "1662690329318"
      provider: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-03-03 03:36:21.06"
  - subscriptionId: 3f7cb6dd-00ab-4376-ae65-49ede024553a
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 650142320a6377400ddc4a99
    apiInfo:
      id: 650142320a6377400ddc4a99
      name: Choreo TestGPT API
      displayName: Choreo TestGPT API
      description: API for using Choreo TestGPT for API testing.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreotestgpt
      version: 0.1.0
      type: HTTP
      createdTime: "1694581298180"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://testgpt.choreo-ai.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-10-24 04:56:31.937"
  - subscriptionId: 0dcf21fa-b5fa-4cbc-94cc-57defa42b356
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 63f89b690ba4773450505c81
    apiInfo:
      id: 63f89b690ba4773450505c81
      name: CIO Incident Configurator
      displayName: CIO Incident Configurator
      description: This API will act as the configurator for CIO dashboard. API will store configuration  details required for incident scraper and serve them as a REST API on demand
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-incident-configurator
      version: 1.0.0
      type: HTTP
      createdTime: "1677237097935"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-04-18 04:51:38.91"
  - subscriptionId: d1805113-cefd-441f-848b-d4e18e370490
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 63e62ff6f690b440c088e121
    apiInfo:
      id: 63e62ff6f690b440c088e121
      name: CIO Metric Collector
      displayName: CIO Metric Collector
      description: This API will act as the metric collector for CIO dashboard. API will handle the authorization and will then forward the payload to downstream processing pipeline.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-metric-collector
      version: 1.0.0
      type: HTTP
      createdTime: "1676029942495"
      provider: 2d92c502-cb6c-411e-865d-e25dace20279
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2d92c502-cb6c-411e-865d-e25dace20279
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-04-18 04:51:44.28"
  - subscriptionId: cdfcbcc8-a9a4-4db6-a532-d830ba74fab5
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 63a2d42a17a2541a07342aeb
    apiInfo:
      id: 63a2d42a17a2541a07342aeb
      name: CIO Query API
      displayName: CIO Query API
      description: This api is used to connect to the CIO Query API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-query-api
      version: 1.0.0
      type: HTTP
      createdTime: "1671615530332"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-04-18 04:51:49.087"
  - subscriptionId: a10d84fa-6b37-422e-981f-6981841bdb90
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 670e3f6055f33d048d031340
    apiInfo:
      id: 670e3f6055f33d048d031340
      name: Component Utils
      displayName: Component Utils
      description: This is Component Management Utils API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils
      version: 1.0.0
      type: HTTP
      createdTime: "1728986976365"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-10-15 10:36:20.597"
  - subscriptionId: 445aca26-8740-4c7f-b8b8-091638e2f262
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 645c87bf29258d5e5cd9125d
    apiInfo:
      id: 645c87bf29258d5e5cd9125d
      name: Components Management
      displayName: Components Management
      description: This is the Choreo Components Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1683785663821"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-01-10 11:54:00.933"
  - subscriptionId: 06702f2c-9c82-4d4f-a8e5-9d248a2b46fa
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 64140b1f0b438a1df721516c
    apiInfo:
      id: 64140b1f0b438a1df721516c
      name: Config Management
      displayName: Config Management
      description: This is the Choreo Global Configuration Service API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1679035167851"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-07-18 06:22:56.547"
  - subscriptionId: 81ce3ce3-ae67-4c1a-b21c-e536523d6919
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 6423c6c318381a2b93e26270
    apiInfo:
      id: 6423c6c318381a2b93e26270
      name: Crypto Key Service
      displayName: Crypto Key Service
      description: Api to expose crypto key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/crypto-key-service
      version: 0.1.0
      type: HTTP
      createdTime: "1680066243461"
      provider: 392560f1-c26f-45f3-89d6-ad0a5cec79af
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 392560f1-c26f-45f3-89d6-ad0a5cec79af
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-09-07 10:23:26.423"
  - subscriptionId: fadedce3-c0f6-4725-9bb5-5c046d518d20
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 61bdd480bd84cd17397efee1
    apiInfo:
      id: 61bdd480bd84cd17397efee1
      name: Insights
      displayName: Insights
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insights
      version: 1.0.0
      type: HTTP
      createdTime: "1639830656122"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "5.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: fcbbb075-1891-4756-ae44-1a9432e13d0a
        vendor: WSO2
      businessInformation:
        businessOwner: Fazlan Nazeem
        businessOwnerEmail: <EMAIL>
        technicalOwner: Fazlan Nazeem
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-02-03 05:55:11.33"
  - subscriptionId: 3202a057-5b46-40c1-9e39-e38ab2185f65
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 61bdd3ccbd84cd17397efedf
    apiInfo:
      id: 61bdd3ccbd84cd17397efedf
      name: InsightsAlert
      displayName: InsightsAlert
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insightsalert
      version: 1.0.0
      type: HTTP
      createdTime: "1639830476199"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 7a90890c-9c7d-4f13-86ea-93c74a75f801
        vendor: WSO2
      businessInformation:
        businessOwner: Raveen Rathnayake
        businessOwnerEmail: <EMAIL>
        technicalOwner: Raveen Rathnayake
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-02-03 05:55:14.477"
  - subscriptionId: f2a6f3fb-6647-4587-a0d1-cc6cda68715a
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 64b4e04e6f73c71fa9969691
    apiInfo:
      id: 64b4e04e6f73c71fa9969691
      name: Observability Manager
      displayName: Observability Manager
      description: This api is used to connect to the Observability Manager service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager
      version: 0.1.0
      type: HTTP
      createdTime: "1689575502411"
      provider: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Silver
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-09-12 17:12:58.507"
  - subscriptionId: a2be1c19-305b-4d09-be66-57fa37e9c04e
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 61bd9a5d6c70d4270abf41b3
    apiInfo:
      id: 61bd9a5d6c70d4270abf41b3
      name: ObservabilityApplication
      displayName: ObservabilityApplication
      description: Choreo control plane Observability API for fetching application level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/application
      version: 0.1.0
      type: HTTP
      createdTime: "1639815773440"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-01-21 07:29:01.217"
  - subscriptionId: 211071fb-6acf-4567-9304-bee63bdbc587
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 61bd9d156c70d4270abf41b5
    apiInfo:
      id: 61bd9d156c70d4270abf41b5
      name: ObservabilityLogging
      displayName: ObservabilityLogging
      description: Choreo control plane Observability API for fetching logs of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/logging
      version: 0.1.0
      type: HTTP
      createdTime: "1639816469410"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-01-21 07:29:04.587"
  - subscriptionId: 41f8fde4-c925-4238-a496-aba8cc3a21f9
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 61bd9d9482ae4a5211ea7924
    apiInfo:
      id: 61bd9d9482ae4a5211ea7924
      name: ObservabilitySystem
      displayName: ObservabilitySystem
      description: Choreo control plane Observability API for fetching system level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/system
      version: 0.1.0
      type: HTTP
      createdTime: "1639816596688"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-01-21 07:29:06.837"
  - subscriptionId: 56a97342-2b65-4dea-be00-667782763088
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 62419cc7690bbb0c8c544443
    apiInfo:
      id: 62419cc7690bbb0c8c544443
      name: Project API
      displayName: Project API
      description: This api is used to connect to the Project API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/projects
      version: 1.0.0
      type: HTTP
      createdTime: "1648467143810"
      provider: 2d92c502-cb6c-411e-865d-e25dace20279
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2d92c502-cb6c-411e-865d-e25dace20279
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: null
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-03-31 09:07:51.5"
  - subscriptionId: b7bf9232-eca7-4ac1-995a-8fed85f2a94e
    applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
    apiId: 6757f66a542ddb34b244bb53
    apiInfo:
      id: 6757f66a542ddb34b244bb53
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: v1.0
      type: HTTP
      createdTime: "1733817962976"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 01497e74-5925-4c47-9f31-a1e1e612347f
      name: Site24x7 Monitors App
      throttlingPolicy: Unlimited
      description: 'Credentials generated from this applicaion are configured in the Site24x7. '
      status: APPROVED
      groups: []
      subscriptionCount: 18
      attributes: {}
      owner: da9c10db-d44c-4151-a9b5-aa8300ee280c
      createdTime: "1641789194880"
      updatedTime: "1641789194880"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
