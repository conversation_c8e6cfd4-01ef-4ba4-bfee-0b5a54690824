subscriptions:
  - subscriptionId: e0b502e5-72c0-4357-bdfc-8dd678247c6d
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 62cd4f3fafa3f22aadee5e8f
    apiInfo:
      id: 62cd4f3fafa3f22aadee5e8f
      name: Alert Configuration Service
      displayName: Alert Configuration Service
      description: This api is used to connect to the Alert Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/alert-configuration-service
      version: 1.0.0
      type: HTTP
      createdTime: "1657622335295"
      provider: 531b4953-bc28-40fb-8794-41834bd55438
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 531b4953-bc28-40fb-8794-41834bd55438
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-07-21 12:31:34.6"
  - subscriptionId: 2cc232d1-d8e8-428b-9e3f-c58c956fe2dd
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 65448ed2be658723011579a7
    apiInfo:
      id: 65448ed2be658723011579a7
      name: APIM AppDev APIs
      displayName: APIM AppDev APIs
      description: This api is used to connect to the APIM AppDev APIs service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/apim-appdev
      version: v1.0
      type: HTTP
      createdTime: "1698991826606"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 65448ed2be658723011579a7
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-06-06 08:49:18.003"
  - subscriptionId: 00d72c3b-9b6a-43ea-b00f-907d4e1fa599
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 654489dabe6587230115797a
    apiInfo:
      id: 654489dabe6587230115797a
      name: Built-in IdP User Management
      displayName: Built-in IdP User Management
      description: This is the RESTful API for Choreo Resident IdP User Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-store-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698990554639"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 654489dabe6587230115797a
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-05-16 09:45:15.477"
  - subscriptionId: 1f7e3824-9230-40e0-abef-cae7360638bb
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 65448bbbbe6587230115797d
    apiInfo:
      id: 65448bbbbe6587230115797d
      name: Built-in STS Authorization Service
      displayName: Built-in STS Authorization Service
      description: |
        This API specification outlines the endpoints and operations for the Authorization Service.  It enables users to handle roles, role-permission mappings, role-group mappings, and process authorization requests.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/authz-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698991035696"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 65448bbbbe6587230115797d
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-05-16 09:45:18.753"
  - subscriptionId: 9c84e59c-a1e9-49e7-9cff-c1ffb63ff052
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 633be0a544f40d1b459e5a9f
    apiInfo:
      id: 633be0a544f40d1b459e5a9f
      name: Choreo Admin API
      displayName: Choreo Admin API
      description: |
        This is a RESTFul API to perform Administrative operations in Choreo
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api
      version: 1.0.0
      type: HTTP
      createdTime: "1664868517194"
      provider: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-07-11 07:06:30.323"
  - subscriptionId: ccb40335-ec9f-4deb-9d8c-0d859c5b2e8a
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 67289e13d90467127752125e
    apiInfo:
      id: 67289e13d90467127752125e
      name: Choreo Appdev STS Management Service
      displayName: Choreo Appdev STS Management Service
      description: API for the Choreo Appdev STS Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-appdev-sts-management-service
      version: v1.0
      type: HTTP
      createdTime: "1730715155674"
      provider: a579828c-19e4-4406-92f4-15df9033c2fc
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: a579828c-19e4-4406-92f4-15df9033c2fc
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 67289e13d90467127752125e
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-12-02 09:15:28.257"
  - subscriptionId: 20a936fd-a041-41fe-996c-adad3020df19
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 6482e48e03e62408c6c8be4f
    apiInfo:
      id: 6482e48e03e62408c6c8be4f
      name: Choreo Audit Event Listener API
      displayName: Choreo Audit Event Listener API
      description: |-
        Owned by Org-Mgt team

        Ballerina websubhub module(https://central.ballerina.io/ballerina/websubhub/latest) itself supports security during content distribution by giving an option to configure a hub secret (https://central.ballerina.io/ballerina/websubhub/latest#Subscription) during subscription at the listener service side where a signature is generated based on this secret and signed during content distribution. The websub protocol itself address the subscriber side security using the hub secret. This security mechanism is already enabled in the Choreo audit event listener service by configuring a hub secret.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-event-listener
      version: 1.0.0
      type: HTTP
      createdTime: "1686299790430"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-05-16 09:45:39.993"
  - subscriptionId: d4834bcf-5be5-4e06-9f4f-1babb864b1fb
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 6482e60403e62408c6c8be6e
    apiInfo:
      id: 6482e60403e62408c6c8be6e
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: 1.0.0
      type: HTTP
      createdTime: "1686300164732"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-05-16 09:45:35.043"
  - subscriptionId: 0639120a-8960-4236-a079-74c2e744ed98
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 631aa4195fb46c53f7971f88
    apiInfo:
      id: 631aa4195fb46c53f7971f88
      name: Choreo DevOps Portal API
      displayName: Choreo DevOps Portal API
      description: This api is used to connect to the Choreo DevOps Portal API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/devops
      version: 1.0.0
      type: HTTP
      createdTime: "1662690329318"
      provider: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-03-30 06:20:51.99"
  - subscriptionId: 6b5323a4-3b92-43a7-9ba5-f6167a84d4a2
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 64bfa4eb012b9122ece541b1
    apiInfo:
      id: 64bfa4eb012b9122ece541b1
      name: Choreo Logging API
      displayName: Choreo Logging API
      description: API for retrieving logs of choreo user applications.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreologgingapi
      version: 0.2.0
      type: HTTP
      createdTime: "1690281195822"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-05-16 09:46:10.497"
  - subscriptionId: 1cf64e2b-282f-4387-a8cc-8226edbd7557
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 651123216255bb2d58e10b39
    apiInfo:
      id: 651123216255bb2d58e10b39
      name: Choreo Observability API
      displayName: Choreo Observability API
      description: API for retrieving telemetry data of choreo user applications.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreoobsapi
      version: 0.2.0
      type: HTTP
      createdTime: "1695621921532"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-obsapi.choreo-observability.svc.cluster.local:9095/obsapi
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-05-16 09:46:02.84"
  - subscriptionId: 5d21d65d-c4b6-4bee-a0db-725fba9c259a
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 6694dfcb9c63ed2c4b1fdbd4
    apiInfo:
      id: 6694dfcb9c63ed2c4b1fdbd4
      name: Choreo Observability API
      displayName: Choreo Observability API
      description: API for retrieving telemetry data of choreo user applications.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreoobsapi
      version: 0.3.0
      type: HTTP
      createdTime: "1721032651279"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://5659b6b7-1063-41ed-8e39-d91857699255-systemapis.e1-us-east-azure.st.choreoapis.dev/systemapis/choreoobsapi/0.3.0
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-07-15 08:42:32.727"
  - subscriptionId: 4e433ad2-d3c1-435a-904c-5fafed7bbf00
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 651122da0d850b2210172fe9
    apiInfo:
      id: 651122da0d850b2210172fe9
      name: Choreo System Observability API
      displayName: Choreo System Observability API
      description: API for retrieving system metrics of choreo user applications.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreosysobsapi
      version: 0.2.0
      type: HTTP
      createdTime: "1695621850841"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-sys-obsapi.choreo-observability.svc.cluster.local:9098/sysobsapi
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-05-16 09:46:21.437"
  - subscriptionId: 90f20c9e-b3d0-4788-b25d-064e2e75ebf9
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 645c87bf29258d5e5cd9125d
    apiInfo:
      id: 645c87bf29258d5e5cd9125d
      name: Components Management
      displayName: Components Management
      description: This is the Choreo Components Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1683785663821"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-07-11 07:07:30.08"
  - subscriptionId: 74d0fbd9-5ed6-4610-b9bd-f173686095d1
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 64140b1f0b438a1df721516c
    apiInfo:
      id: 64140b1f0b438a1df721516c
      name: Config Management
      displayName: Config Management
      description: This is the Choreo Global Configuration Service API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1679035167851"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-03-30 06:21:20.157"
  - subscriptionId: 122f01c8-e19b-4a37-83d3-22d1742b8b2f
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 654350269e53c83b329ab097
    apiInfo:
      id: 654350269e53c83b329ab097
      name: Configuration Mapping Service
      displayName: Configuration Mapping Service
      description: This api is used to connect to the Configuration Mapping Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910246275"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 654350269e53c83b329ab097
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-05-16 09:46:42.107"
  - subscriptionId: a44c676c-e8ba-460d-90ce-ae7911e5a568
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 6543529f9e53c83b329ab0a5
    apiInfo:
      id: 6543529f9e53c83b329ab0a5
      name: Configuration Service
      displayName: Configuration Service
      description: This api is used to connect to the Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910879023"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6543529f9e53c83b329ab0a5
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2024-05-16 09:46:48.857"
  - subscriptionId: 08fec96a-1e62-4d43-959f-132d6f1570b4
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 6544d9e29a886f6d1afddd22
    apiInfo:
      id: 6544d9e29a886f6d1afddd22
      name: Connection Management
      displayName: Connection Management
      description: This api is used to connect to the Connection Management service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections
      version: v1.0
      type: HTTP
      createdTime: "1699011042225"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://connection-service.prod-choreo-system:9000/connections/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6544d9e29a886f6d1afddd22
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: v1
    createdTime: "2023-12-19 06:01:27.6"
  - subscriptionId: cea7917c-60da-422c-920f-0554055718b0
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 61bdd47abd84cd17397efee0
    apiInfo:
      id: 61bdd47abd84cd17397efee0
      name: ConnectorBuilder
      displayName: ConnectorBuilder
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connectorbuilder
      version: 0.1.0
      type: HTTP
      createdTime: "1639830650431"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: e859aabe-1642-48ff-845e-a631303fd9e7
        vendor: WSO2
      businessInformation:
        businessOwner: Isuru Boyagane
        businessOwnerEmail: <EMAIL>
        technicalOwner: Isuru Boyagane
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-07-21 12:31:32.79"
  - subscriptionId: 128eede5-ed27-4d63-9863-677768621cef
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 6423c6c318381a2b93e26270
    apiInfo:
      id: 6423c6c318381a2b93e26270
      name: Crypto Key Service
      displayName: Crypto Key Service
      description: Api to expose crypto key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/crypto-key-service
      version: 0.1.0
      type: HTTP
      createdTime: "1680066243461"
      provider: 392560f1-c26f-45f3-89d6-ad0a5cec79af
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 392560f1-c26f-45f3-89d6-ad0a5cec79af
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-07-11 08:00:43.293"
  - subscriptionId: d6f50da3-7834-4153-93c5-48d5d4fea7fd
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 61bdd480bd84cd17397efee1
    apiInfo:
      id: 61bdd480bd84cd17397efee1
      name: Insights
      displayName: Insights
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insights
      version: 1.0.0
      type: HTTP
      createdTime: "1639830656122"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "5.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: fcbbb075-1891-4756-ae44-1a9432e13d0a
        vendor: WSO2
      businessInformation:
        businessOwner: Fazlan Nazeem
        businessOwnerEmail: <EMAIL>
        technicalOwner: Fazlan Nazeem
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-03-28 12:05:59.793"
  - subscriptionId: 7b77a0da-bff4-464c-ba03-193e8a7b3249
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 61bdd3ccbd84cd17397efedf
    apiInfo:
      id: 61bdd3ccbd84cd17397efedf
      name: InsightsAlert
      displayName: InsightsAlert
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insightsalert
      version: 1.0.0
      type: HTTP
      createdTime: "1639830476199"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 7a90890c-9c7d-4f13-86ea-93c74a75f801
        vendor: WSO2
      businessInformation:
        businessOwner: Raveen Rathnayake
        businessOwnerEmail: <EMAIL>
        technicalOwner: Raveen Rathnayake
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-03-28 12:06:01.063"
  - subscriptionId: a7ce8656-1ed5-484d-89cc-43d861816fe3
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 6486b08be88a407b4f8f5ff1
    apiInfo:
      id: 6486b08be88a407b4f8f5ff1
      name: Marketplace
      displayName: Marketplace
      description: This api is used to connect to the Marketplace service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/marketplace
      version: 0.1.0
      type: HTTP
      createdTime: "1686548619356"
      provider: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-12-19 06:01:15.983"
  - subscriptionId: f28acf8f-613a-491c-a072-d7b078c7708b
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 61d293d43234be4e91a47af3
    apiInfo:
      id: 61d293d43234be4e91a47af3
      name: NotificationService
      displayName: NotificationService
      description: This API is used to connect to the Service Openapi Yaml service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/notification-service
      version: 1.0.0
      type: HTTP
      createdTime: "1641190356623"
      provider: 4c1b2770-c9ac-49aa-a5ec-08d90014e95b
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4c1b2770-c9ac-49aa-a5ec-08d90014e95b
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-03-28 12:05:43.59"
  - subscriptionId: 42fe22fe-cb42-4d0c-9426-0778df289bf0
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 64b4e04e6f73c71fa9969691
    apiInfo:
      id: 64b4e04e6f73c71fa9969691
      name: Observability Manager
      displayName: Observability Manager
      description: This api is used to connect to the Observability Manager service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager
      version: 0.1.0
      type: HTTP
      createdTime: "1689575502411"
      provider: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Silver
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-05-16 09:47:02.247"
  - subscriptionId: 8ff1f196-dab8-45e8-b0ff-c1478fb96b0d
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 61bd9a5d6c70d4270abf41b3
    apiInfo:
      id: 61bd9a5d6c70d4270abf41b3
      name: ObservabilityApplication
      displayName: ObservabilityApplication
      description: Choreo control plane Observability API for fetching application level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/application
      version: 0.1.0
      type: HTTP
      createdTime: "1639815773440"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-05-17 07:02:33.827"
  - subscriptionId: 1ac1dfd7-1502-4389-b190-169b2ba555ab
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 61bd9d156c70d4270abf41b5
    apiInfo:
      id: 61bd9d156c70d4270abf41b5
      name: ObservabilityLogging
      displayName: ObservabilityLogging
      description: Choreo control plane Observability API for fetching logs of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/logging
      version: 0.1.0
      type: HTTP
      createdTime: "1639816469410"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-05-17 07:02:32.633"
  - subscriptionId: 92926b54-b01c-4c91-ad30-2a107dde3c27
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 61bd9d9482ae4a5211ea7924
    apiInfo:
      id: 61bd9d9482ae4a5211ea7924
      name: ObservabilitySystem
      displayName: ObservabilitySystem
      description: Choreo control plane Observability API for fetching system level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/system
      version: 0.1.0
      type: HTTP
      createdTime: "1639816596688"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-05-17 07:02:35.857"
  - subscriptionId: a90cb866-6374-455b-b3a9-00c275bbe5c3
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 6423d5729382a77e3d50c19e
    apiInfo:
      id: 6423d5729382a77e3d50c19e
      name: OnPremKey Management
      displayName: OnPremKey Management
      description: This is the Choreo APIInsights AccessKeys API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/onprem-key-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1680070002236"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-03-30 06:21:27.147"
  - subscriptionId: 4a6c0b8c-f346-48ff-8fef-22f2dae1f95e
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 64742bf8240d4717b1131bcc
    apiInfo:
      id: 64742bf8240d4717b1131bcc
      name: Organization API
      displayName: Organization API
      description: This is the Choreo Runtime Organization API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/orgs
      version: 1.0.0
      type: HTTP
      createdTime: "1685335032726"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2023-07-11 07:06:59.633"
  - subscriptionId: 600f980e-a897-4d99-8c84-2982c207cee0
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 62c3ce68f4ea5a773534c0b8
    apiInfo:
      id: 62c3ce68f4ea5a773534c0b8
      name: Organization Management
      displayName: Organization Management
      description: |-
        Owned by Org-Mgt team

        Repo: https://github.com/wso2-enterprise/choreo-organization-management
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/org-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1656999528217"
      provider: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-07-21 12:31:38.86"
  - subscriptionId: aaa6a574-9202-476e-ae5a-877c914cd6d4
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 62419cc7690bbb0c8c544443
    apiInfo:
      id: 62419cc7690bbb0c8c544443
      name: Project API
      displayName: Project API
      description: This api is used to connect to the Project API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/projects
      version: 1.0.0
      type: HTTP
      createdTime: "1648467143810"
      provider: 2d92c502-cb6c-411e-865d-e25dace20279
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2d92c502-cb6c-411e-865d-e25dace20279
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: null
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2022-04-20 11:03:56.923"
  - subscriptionId: 323a3f80-2b12-4960-8f8e-fec50b013096
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 64daf146d5aa8b4bada40cde
    apiInfo:
      id: 64daf146d5aa8b4bada40cde
      name: User Management
      displayName: User Management
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1692070214459"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://api-server.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2024-05-16 09:47:17.75"
  - subscriptionId: dc9c77b6-e53f-4862-a3a5-4554ffea572a
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 670768cb2584260c4314ce42
    apiInfo:
      id: 670768cb2584260c4314ce42
      name: API Key Service
      displayName: API Key Service
      description: This api is used to API Key management operations
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/api-key-service
      version: v1.0
      type: HTTP
      createdTime: "1728538827190"
      provider: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 34
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: e5581fbe-5f07-4b82-8064-4effb0fc422b
    applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
    apiId: 67038cf4f86e5c6a5a320084
    apiInfo:
      id: 67038cf4f86e5c6a5a320084
      name: Configuration Schema Service
      displayName: Configuration Schema Service
      description: This api is used to connect to the Configuration Schema Service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/configuration-schema
      version: v1.0
      type: HTTP
      createdTime: "1728285940200"
      provider: 8e923942-ae07-4a50-a426-3c6467624ee4
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 8e923942-ae07-4a50-a426-3c6467624ee4
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 488f52a9-b8ab-4323-a4e1-7ef7b9a00169
      name: choreo-integration-test-app
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 36
      attributes: {}
      owner: 85128232-a1c4-4cb2-9828-46f414db52f5
      createdTime: "1648469093703"
      updatedTime: "1648469093703"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
