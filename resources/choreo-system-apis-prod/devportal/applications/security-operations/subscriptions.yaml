subscriptions:
  - subscriptionId: 47179454-5397-4ad3-ab3b-9ea50a2bd8fb
    applicationId: b6040c70-49b8-495d-bfa6-fba8f870b570
    apiId: 633be0a544f40d1b459e5a9f
    apiInfo:
      id: 633be0a544f40d1b459e5a9f
      name: Choreo Admin API
      displayName: Choreo Admin API
      description: Owned by the Org Mgt Team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api
      version: 1.0.0
      type: HTTP
      createdTime: "1664868517194"
      provider: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: b6040c70-49b8-495d-bfa6-fba8f870b570
      name: Security Operations
      throttlingPolicy: Unlimited
      description: This application is used by the Security Operations team to access the Choreo system APIs
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 0228631a-5468-445b-af43-ba78b5468457
      createdTime: "1721887023003"
      updatedTime: "1721887023003"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
