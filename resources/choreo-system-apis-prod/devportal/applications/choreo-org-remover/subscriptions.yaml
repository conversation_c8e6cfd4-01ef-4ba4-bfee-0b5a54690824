subscriptions:
  - subscriptionId: "a075e37b-2d69-4f34-a233-2db1befe5d34"
    applicationId: "a36ef7ef-e4b1-412f-b0cf-9a85d064ed2c"
    apiId: "661e1b0773243166afb811b1"
    apiInfo:
      id: "661e1b0773243166afb811b1"
      name: "organizationremover"
      displayName: "Organization Remover"
      description: "API for Organization Removal"
      context: "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/org-remover"
      version: "v1.0"
      type: "HTTP"
      createdTime: "1713249031364"
      provider: "678ead13-dc75-4004-9fc0-35cd45eddf8f"
      lifeCycleStatus: "PUBLISHED"
      thumbnailUri:
      avgRating: "0.0"
      throttlingPolicies:
      - "Unlimited"
      advertiseInfo:
        advertised: "false"
        apiExternalProductionEndpoint:
        apiExternalSandboxEndpoint:
        originalDevPortalUrl:
        apiOwner: "678ead13-dc75-4004-9fc0-35cd45eddf8f"
        vendor: "WSO2"
      businessInformation:
        businessOwner:
        businessOwnerEmail:
        technicalOwner:
        technicalOwnerEmail:
      isSubscriptionAvailable: "true"
      monetizationLabel: "FREE"
      gatewayVendor: "wso2"
      versionInfo:
      endpointURLs:
      - environmentName: "control-plane"
        environmentDisplayName: "control-plane"
        environmentType: "hybrid"
        URLs:
          http: "http://choreo.dev/93tu/org-remover/v1.0"
          https: "https://choreo.dev/93tu/org-remover/v1.0"
          ws:
          wss:
        defaultVersionURLs:
          http:
          https:
          ws:
          wss:
      additionalProperties:
      - name: "accessibility"
        value: "external"
        display: "true"
    applicationInfo:
      applicationId: "a36ef7ef-e4b1-412f-b0cf-9a85d064ed2c"
      name: "Choreo Org Remover"
      throttlingPolicy: "Unlimited"
      description: "Application used for stale org deletion authentication"
      status: "APPROVED"
      groups:
      subscriptionCount: "1"
      attributes:
      owner: "678ead13-dc75-4004-9fc0-35cd45eddf8f"
      createdTime: "1713249351847"
      updatedTime: "1713249351847"
    throttlingPolicy: "Unlimited"
    requestedThrottlingPolicy: "Unlimited"
    status: "UNBLOCKED"
    redirectionParams:
    versionRange:
    createdTime:
