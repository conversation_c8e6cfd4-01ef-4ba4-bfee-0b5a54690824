keys:
  - keyMappingId: "5cb78ab3-ebb0-42d7-a266-89a6327082a0"
    keyManager: "Resident Key Manager"
    consumerKey: "OgzE61ni2gX_VczHcclkXuVmQwwa"
    consumerSecret: "*****"
    supportedGrantTypes:
    - "client_credentials"
    callbackUrl: ""
    keyState: "APPROVED"
    keyType: "PRODUCTION"
    mode: "CREATED"
    groupId:
    token:
      accessToken: "eyJ4NXQ*****"
      tokenScopes:
      - "default"
      validityTime: "3600"
    additionalProperties:
      id_token_expiry_time: "3600"
      application_access_token_expiry_time: "3600"
      user_access_token_expiry_time: "3600"
      bypassClientCredentials: "false"
      pkceMandatory: "false"
      pkceSupportPlain: "false"
      refresh_token_expiry_time: "86400"
