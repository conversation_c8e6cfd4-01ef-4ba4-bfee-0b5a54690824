subscriptions:
  - subscriptionId: ca88fe05-0561-4e54-91c5-add8b90bf17b
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 662f36a6e23797639415e2ba
    apiInfo:
      id: 662f36a6e23797639415e2ba
      name: AI Copilot
      displayName: AI Copilot
      description: AI Copilot service that provide chat experience in Chore Console.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/aicopilot
      version: v1.0
      type: HTTP
      createdTime: "1714370214962"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://ai-copilot.choreo-ai:9090/
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 204ede49-3aea-4588-83d1-674407b0f92c
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 62cd4f3fafa3f22aadee5e8f
    apiInfo:
      id: 62cd4f3fafa3f22aadee5e8f
      name: Alert Configuration Service
      displayName: Alert Configuration Service
      description: This api is used to connect to the Alert Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/alert-configuration-service
      version: 1.0.0
      type: HTTP
      createdTime: "1657622335295"
      provider: 531b4953-bc28-40fb-8794-41834bd55438
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 531b4953-bc28-40fb-8794-41834bd55438
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 2
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 3faa62b4-195d-4841-80d5-5885bf9c3b5f
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 670768cb2584260c4314ce42
    apiInfo:
      id: 670768cb2584260c4314ce42
      name: API Key Service
      displayName: API Key Service
      description: This api is used to API Key management operations
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/api-key-service
      version: v1.0
      type: HTTP
      createdTime: "1728538827190"
      provider: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a7f54c8-6346-4fce-b7fa-34e13f8d8764
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 3
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 7dada05c-6136-4b07-8fda-92e18c94bd4c
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 65448ed2be658723011579a7
    apiInfo:
      id: 65448ed2be658723011579a7
      name: APIM AppDev APIs
      displayName: APIM AppDev APIs
      description: This api is used to connect to the APIM AppDev APIs service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/apim-appdev
      version: v1.0
      type: HTTP
      createdTime: "1698991826606"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 4
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 61072f42-9069-4088-aba0-3fc761263a74
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 654489dabe6587230115797a
    apiInfo:
      id: 654489dabe6587230115797a
      name: Built-in IdP User Management
      displayName: Built-in IdP User Management
      description: This is the RESTful API for Choreo Resident IdP User Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-store-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698990554639"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 5
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: cad82207-b85e-487e-abd8-ef38cb751d8c
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 65448bbbbe6587230115797d
    apiInfo:
      id: 65448bbbbe6587230115797d
      name: Built-in STS Authorization Service
      displayName: Built-in STS Authorization Service
      description: |
        This API specification outlines the endpoints and operations for the Authorization Service.  It enables users to handle roles, role-permission mappings, role-group mappings, and process authorization requests.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/authz-mgt
      version: v1.0
      type: HTTP
      createdTime: "1698991035696"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 6
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: a7c3b3d7-0196-40f1-8d45-e10e307f7140
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 633be0a544f40d1b459e5a9f
    apiInfo:
      id: 633be0a544f40d1b459e5a9f
      name: Choreo Admin API
      displayName: Choreo Admin API
      description: |
        This is a RESTFul API to perform Administrative operations in Choreo
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api
      version: 1.0.0
      type: HTTP
      createdTime: "1664868517194"
      provider: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 92fd492b-11ce-4f64-a4be-d03c0d1a4aa7
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 7
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: ede00c24-7b76-4f1f-bed1-3c83aae3ad4d
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 63a447d4afc39e72d98ea2be
    apiInfo:
      id: 63a447d4afc39e72d98ea2be
      name: Choreo AI Test Assistant
      displayName: Choreo AI Test Assistant
      description: |
        The Test Assistant API provides example payload data for testing Choreo components.
        It considers the payload schemas of the Open API specification and generates sample data that conforms to the
        schema and enriches the specification with the generated examples.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/ai-test-assistant
      version: 1.0.0
      type: HTTP
      createdTime: "1671710676949"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 8
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 67a74f06-eb45-41e2-bdb8-c11bf60e2d14
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 67289e13d90467127752125e
    apiInfo:
      id: 67289e13d90467127752125e
      name: Choreo Appdev STS Management Service
      displayName: Choreo Appdev STS Management Service
      description: API for the Choreo Appdev STS Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-appdev-sts-management-service
      version: v1.0
      type: HTTP
      createdTime: "1730715155674"
      provider: a579828c-19e4-4406-92f4-15df9033c2fc
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: a579828c-19e4-4406-92f4-15df9033c2fc
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 9
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: b0c1b5f2-486f-4b3e-b7cc-31f33e3746ea
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6757f66a542ddb34b244bb53
    apiInfo:
      id: 6757f66a542ddb34b244bb53
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: v1.0
      type: HTTP
      createdTime: "1733817962976"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 10
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 80b54a6f-0488-4767-8f35-d24f79f491cd
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6482e60403e62408c6c8be6e
    apiInfo:
      id: 6482e60403e62408c6c8be6e
      name: Choreo Audit Logging API
      displayName: Choreo Audit Logging API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging
      version: 1.0.0
      type: HTTP
      createdTime: "1686300164732"
      provider: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: Sumedha Kodithuwakku
        technicalOwner: Roland Hewage
        technicalOwnerEmail: Roland Hewage
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 11
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: d21d2d14-9e0c-4b5c-9c9a-2a067b395d8d
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 631aa4195fb46c53f7971f88
    apiInfo:
      id: 631aa4195fb46c53f7971f88
      name: Choreo DevOps Portal API
      displayName: Choreo DevOps Portal API
      description: This api is used to connect to the Choreo DevOps Portal API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/devops
      version: 1.0.0
      type: HTTP
      createdTime: "1662690329318"
      provider: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 12
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: e251d501-dcb3-4eeb-93fd-64cc81b1e5be
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 66f15212fe856218b0c32c84
    apiInfo:
      id: 66f15212fe856218b0c32c84
      name: Choreo Governance Service API
      displayName: Choreo Governance Service API
      description: API for governing Choreo APIs/Components and other resources.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/governance
      version: v1.0
      type: HTTP
      createdTime: "1727091218612"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 13
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: ec3d7670-331b-487a-944e-c14ef9632b1e
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 650142320a6377400ddc4a99
    apiInfo:
      id: 650142320a6377400ddc4a99
      name: Choreo TestGPT API
      displayName: Choreo TestGPT API
      description: API for using Choreo TestGPT for API testing.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreotestgpt
      version: 0.1.0
      type: HTTP
      createdTime: "1694581298180"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://testgpt.choreo-ai.svc.cluster.local:9090
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 14
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 8a384ebd-cc85-4002-b421-2db382cf6970
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 63f89b690ba4773450505c81
    apiInfo:
      id: 63f89b690ba4773450505c81
      name: CIO Incident Configurator
      displayName: CIO Incident Configurator
      description: This API will act as the configurator for CIO dashboard. API will store configuration  details required for incident scraper and serve them as a REST API on demand
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-incident-configurator
      version: 1.0.0
      type: HTTP
      createdTime: "1677237097935"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 15
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 94514025-8d29-4d39-b60b-e110139aaaaa
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 63a2d42a17a2541a07342aeb
    apiInfo:
      id: 63a2d42a17a2541a07342aeb
      name: CIO Query API
      displayName: CIO Query API
      description: This api is used to connect to the CIO Query API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cio-query-api
      version: 1.0.0
      type: HTTP
      createdTime: "1671615530332"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 16
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 8386bf85-cc4f-4959-bf1c-d08dbccfbb71
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 65a108071b4376654a0141ef
    apiInfo:
      id: 65a108071b4376654a0141ef
      name: Component Creation Status
      displayName: Component Creation Status
      description: This api is used to connect to the Component Creation Status service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-creation
      version: v1.0
      type: HTTP
      createdTime: "1705052167906"
      provider: 51c0ae66-b0cd-42b2-90b5-2605f856bf24
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 51c0ae66-b0cd-42b2-90b5-2605f856bf24
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 17
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 1de367c8-7fba-42d9-b167-845780fa5720
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 670e3f6055f33d048d031340
    apiInfo:
      id: 670e3f6055f33d048d031340
      name: Component Utils
      displayName: Component Utils
      description: This is Component Management Utils API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils
      version: 1.0.0
      type: HTTP
      createdTime: "1728986976365"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 18
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 02b179d3-faf5-4d29-abcb-778981b7c902
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 645c87bf29258d5e5cd9125d
    apiInfo:
      id: 645c87bf29258d5e5cd9125d
      name: Components Management
      displayName: Components Management
      description: This is the Choreo Components Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1683785663821"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 19
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: c312c74e-ad6b-4746-aeb2-d1b4ab8c0937
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 64140b1f0b438a1df721516c
    apiInfo:
      id: 64140b1f0b438a1df721516c
      name: Config Management
      displayName: Config Management
      description: This is the Choreo Global Configuration Service API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1679035167851"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 20
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: fc577504-9ca0-4806-b468-e549d04a9a6d
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 654350269e53c83b329ab097
    apiInfo:
      id: 654350269e53c83b329ab097
      name: Configuration Mapping Service
      displayName: Configuration Mapping Service
      description: This api is used to connect to the Configuration Mapping Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910246275"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 21
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 978caa4b-36fb-4b46-9a24-d202bcbacf69
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6543529f9e53c83b329ab0a5
    apiInfo:
      id: 6543529f9e53c83b329ab0a5
      name: Configuration Service
      displayName: Configuration Service
      description: This api is used to connect to the Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910879023"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 22
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 4f3e39ec-4764-47a1-8df4-b6d058f79e78
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6544d9e29a886f6d1afddd22
    apiInfo:
      id: 6544d9e29a886f6d1afddd22
      name: Connection Management
      displayName: Connection Management
      description: This api is used to connect to the Connection Management service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections
      version: v1.0
      type: HTTP
      createdTime: "1699011042225"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://connection-service.prod-choreo-system:9000/connections/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 23
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 6509d010-5d49-46f4-8539-23a2c791e6be
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 61bdd47abd84cd17397efee0
    apiInfo:
      id: 61bdd47abd84cd17397efee0
      name: ConnectorBuilder
      displayName: ConnectorBuilder
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connectorbuilder
      version: 0.1.0
      type: HTTP
      createdTime: "1639830650431"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: e859aabe-1642-48ff-845e-a631303fd9e7
        vendor: WSO2
      businessInformation:
        businessOwner: Isuru Boyagane
        businessOwnerEmail: <EMAIL>
        technicalOwner: Isuru Boyagane
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 24
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: e8b8996d-7832-41ad-ad3d-7db0a6db5ab0
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6423c6c318381a2b93e26270
    apiInfo:
      id: 6423c6c318381a2b93e26270
      name: Crypto Key Service
      displayName: Crypto Key Service
      description: Api to expose crypto key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/crypto-key-service
      version: 0.1.0
      type: HTTP
      createdTime: "1680066243461"
      provider: 392560f1-c26f-45f3-89d6-ad0a5cec79af
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 392560f1-c26f-45f3-89d6-ad0a5cec79af
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 25
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: f3480621-99ec-40ec-87b1-1222bc0e4dbf
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6541ed3532503454e1085aa3
    apiInfo:
      id: 6541ed3532503454e1085aa3
      name: Declarative API
      displayName: Declarative API
      description: This api is used to connect to the Declarative API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/declarative-api
      version: v1.0
      type: HTTP
      createdTime: "1698819381865"
      provider: 5072771b-6e31-41d3-8964-2f4fb991d6e3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://declarative-api.prod-choreo-system:8080/api
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5072771b-6e31-41d3-8964-2f4fb991d6e3
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 26
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: f10ed8b1-e1aa-403b-b669-1acb7416a6a1
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 61bdd480bd84cd17397efee1
    apiInfo:
      id: 61bdd480bd84cd17397efee1
      name: Insights
      displayName: Insights
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insights
      version: 1.0.0
      type: HTTP
      createdTime: "1639830656122"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "5.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: fcbbb075-1891-4756-ae44-1a9432e13d0a
        vendor: WSO2
      businessInformation:
        businessOwner: Fazlan Nazeem
        businessOwnerEmail: <EMAIL>
        technicalOwner: Fazlan Nazeem
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 27
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: bb9a4938-7463-4f06-b776-76ec6cd6ac92
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 61bdd3ccbd84cd17397efedf
    apiInfo:
      id: 61bdd3ccbd84cd17397efedf
      name: InsightsAlert
      displayName: InsightsAlert
      description: ""
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insightsalert
      version: 1.0.0
      type: HTTP
      createdTime: "1639830476199"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 7a90890c-9c7d-4f13-86ea-93c74a75f801
        vendor: WSO2
      businessInformation:
        businessOwner: Raveen Rathnayake
        businessOwnerEmail: <EMAIL>
        technicalOwner: Raveen Rathnayake
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 28
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 799cd294-1bd5-441a-b455-cbc0415b9d33
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6486b08be88a407b4f8f5ff1
    apiInfo:
      id: 6486b08be88a407b4f8f5ff1
      name: Marketplace
      displayName: Marketplace
      description: This api is used to connect to the Marketplace service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/marketplace
      version: 0.1.0
      type: HTTP
      createdTime: "1686548619356"
      provider: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 29
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 7463e47b-23b5-4e78-91a4-fe1e53b622d2
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 667e7240a3a9912fa3455193
    apiInfo:
      id: 667e7240a3a9912fa3455193
      name: Milvus Proxy
      displayName: Milvus Proxy
      description: API to interact with the milvus vector DB
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy
      version: v1.0
      type: HTTP
      createdTime: "1719562816821"
      provider: 278ca2f9-1c2e-44f8-8966-eacb35954a99
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 278ca2f9-1c2e-44f8-8966-eacb35954a99
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 30
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 76211c2b-33bb-4e4d-8da1-20a54beae993
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 648031628219b95767c68caa
    apiInfo:
      id: 648031628219b95767c68caa
      name: Moesif Key
      displayName: Moesif Key
      description: This api is used to connect to the Moesif Key service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/moesif-key
      version: 0.1.0
      type: HTTP
      createdTime: "1686122850577"
      provider: 2f146911-4a19-4906-9109-bcd6e60feba1
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2f146911-4a19-4906-9109-bcd6e60feba1
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 31
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: e6f0052b-810a-4c17-ae8c-28b80191b75f
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 64b4e04e6f73c71fa9969691
    apiInfo:
      id: 64b4e04e6f73c71fa9969691
      name: Observability Manager
      displayName: Observability Manager
      description: This api is used to connect to the Observability Manager service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager
      version: 0.1.0
      type: HTTP
      createdTime: "1689575502411"
      provider: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Silver
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 32
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 84eb5132-0bdf-45ee-944c-cfba0d9355c0
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 61bd9a5d6c70d4270abf41b3
    apiInfo:
      id: 61bd9a5d6c70d4270abf41b3
      name: ObservabilityApplication
      displayName: ObservabilityApplication
      description: Choreo control plane Observability API for fetching application level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/application
      version: 0.1.0
      type: HTTP
      createdTime: "1639815773440"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 33
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 5ed683a4-560c-4ea9-ab64-ccea581e736f
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 61bd9d156c70d4270abf41b5
    apiInfo:
      id: 61bd9d156c70d4270abf41b5
      name: ObservabilityLogging
      displayName: ObservabilityLogging
      description: Choreo control plane Observability API for fetching logs of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/logging
      version: 0.1.0
      type: HTTP
      createdTime: "1639816469410"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 34
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: c044d182-e204-4322-bacf-e18164647ebb
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 61bd9d9482ae4a5211ea7924
    apiInfo:
      id: 61bd9d9482ae4a5211ea7924
      name: ObservabilitySystem
      displayName: ObservabilitySystem
      description: Choreo control plane Observability API for fetching system level observability data of a user component
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/system
      version: 0.1.0
      type: HTTP
      createdTime: "1639816596688"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2be9113b-df82-4677-b445-e7c849c75adf
        vendor: WSO2
      businessInformation:
        businessOwner: Nadun De Silva
        businessOwnerEmail: <EMAIL>
        technicalOwner: Nadun De Silva
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 35
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 7d2eb50a-c537-4ae2-8937-4f1acafa4f98
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6423d5729382a77e3d50c19e
    apiInfo:
      id: 6423d5729382a77e3d50c19e
      name: OnPremKey Management
      displayName: OnPremKey Management
      description: This is the Choreo APIInsights AccessKeys API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/onprem-key-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1680070002236"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 36
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: a4ba8034-0e7b-4bbc-8fd8-01ddeedc7980
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 64742bf8240d4717b1131bcc
    apiInfo:
      id: 64742bf8240d4717b1131bcc
      name: Organization API
      displayName: Organization API
      description: This is the Choreo Runtime Organization API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/orgs
      version: 1.0.0
      type: HTTP
      createdTime: "1685335032726"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 37
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 4ea7a38e-8078-42b8-96d0-146364abb065
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 62c3ce68f4ea5a773534c0b8
    apiInfo:
      id: 62c3ce68f4ea5a773534c0b8
      name: Organization Management
      displayName: Organization Management
      description: |-
        Owned by Org-Mgt team

        Repo: https://github.com/wso2-enterprise/choreo-organization-management
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/org-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1656999528217"
      provider: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4a361b8b-e3bb-44d1-bea4-dfac4944ffc3
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 38
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 3149fc78-3654-4669-a27d-cf42544cbf0b
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 6541172854bcdb21d2a334e4
    apiInfo:
      id: 6541172854bcdb21d2a334e4
      name: Platform Services Manager API
      displayName: Platform Services Manager API
      description: This api is used to connect to the Platform Services Manager API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/platform-services
      version: v1.0
      type: HTTP
      createdTime: "1698764584558"
      provider: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://platform-services-manager.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 39
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 1f2ffd57-997d-4d13-ad4a-5c0f002b43a0
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 62419cc7690bbb0c8c544443
    apiInfo:
      id: 62419cc7690bbb0c8c544443
      name: Project API
      displayName: Project API
      description: This api is used to connect to the Project API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/projects
      version: 1.0.0
      type: HTTP
      createdTime: "1648467143810"
      provider: 2d92c502-cb6c-411e-865d-e25dace20279
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2d92c502-cb6c-411e-865d-e25dace20279
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: null
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 40
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 4c1ed309-b0b7-4c27-8ffb-f61004f11b6b
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 65e82d435e87d93a849b8943
    apiInfo:
      id: 65e82d435e87d93a849b8943
      name: URL Management API
      displayName: URL Management API
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/url-mgt
      version: v1.0
      type: HTTP
      createdTime: "1709714755963"
      provider: d962d47d-f855-4202-be7b-3b31e391d1f4
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d962d47d-f855-4202-be7b-3b31e391d1f4
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 41
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: e4c8d2b7-c896-454e-9e13-089ee2771d5a
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 64daf146d5aa8b4bada40cde
    apiInfo:
      id: 64daf146d5aa8b4bada40cde
      name: User Management
      displayName: User Management
      description: Owned by Org-Mgt team
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1692070214459"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://api-server.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: Sumedha Kodithuwakku
        businessOwnerEmail: <EMAIL>
        technicalOwner: Sumedha Kodithuwakku
        technicalOwnerEmail: <EMAIL>
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 42
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 5c50e42a-593e-4468-a47d-4cc4ce9d5400
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 66ea50d5ebf87a2d63485eb1
    apiInfo:
      id: 66ea50d5ebf87a2d63485eb1
      name: Workflow Management API
      displayName: Workflow Management API
      description: Workflow Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/workflow-mgt
      version: v1.0
      type: HTTP
      createdTime: "1726632149432"
      provider: 678ead13-dc75-4004-9fc0-35cd45eddf8f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 678ead13-dc75-4004-9fc0-35cd45eddf8f
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 43
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 2b5d77b1-13ba-4117-988f-19c6bc061665
    applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
    apiId: 67cb15de8f66af757b4daa37
    apiInfo:
      id: 67cb15de8f66af757b4daa37
      name: OAS Source Provider Service
      displayName: OAS Source Provider Service
      description: API for the OAS Source Provider Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/oas-provider-service
      version: 1.0.0
      type: HTTP
      createdTime: "1741362654526"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 72d54ff6-12b1-49c2-94cf-354e45c54d72
      name: Godzilla
      throttlingPolicy: Unlimited
      description: Godzilla UI application
      status: APPROVED
      groups: []
      subscriptionCount: 44
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1740657891310"
      updatedTime: "1740657891310"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
