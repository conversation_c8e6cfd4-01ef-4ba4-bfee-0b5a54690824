subscriptions:
  - subscriptionId: 3e9b04b3-1611-4a2e-a284-9ed3fdca30de
    applicationId: faea8c9e-9da1-4b49-bab1-66af9c6e5519
    apiId: 667e6d06ea5fb93b6741cf29
    apiInfo:
      id: 667e6d06ea5fb93b6741cf29
      name: <PERSON><PERSON><PERSON><PERSON> Proxy Docs Assistant
      displayName: Milvus Proxy Docs Assistant
      description: API to interact with the milvus vector DB for docs search
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy-docs-assistant
      version: v1.0
      type: HTTP
      createdTime: "1719561478142"
      provider: 278ca2f9-1c2e-44f8-8966-eacb35954a99
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 278ca2f9-1c2e-44f8-8966-eacb35954a99
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: faea8c9e-9da1-4b49-bab1-66af9c6e5519
      name: Milvus Proxy Docs Assistant ECIRFP
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 8d664760-7b57-4638-94d9-74e8dc3d7483
      createdTime: "1729165332563"
      updatedTime: "1729165332563"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
