subscriptions:
  - subscriptionId: 314146eb-879f-447e-a9ef-0f4e4b3f1605
    applicationId: 27a755a7-9361-4c92-adc0-a8d39ed224d4
    apiId: 672b4f0ffc8ed50ac30988e0
    apiInfo:
      id: 672b4f0ffc8ed50ac30988e0
      name: Appdev Authorization Service
      displayName: Appdev Authorization Service
      description: API for the Appdev Authorization Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/appdev-authz
      version: v1.0
      type: HTTP
      createdTime: "1730891535319"
      provider: 749f8806-73d2-4a26-99d0-399de85e5586
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 749f8806-73d2-4a26-99d0-399de85e5586
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 27a755a7-9361-4c92-adc0-a8d39ed224d4
      name: Appdev STS App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for Choreo Appdev STS
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 0228631a-5468-445b-af43-ba78b5468457
      createdTime: "1730179082677"
      updatedTime: "1730179082677"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: f8546f47-4a4c-4288-a43e-736c5f2a638d
    applicationId: 27a755a7-9361-4c92-adc0-a8d39ed224d4
    apiId: 672b52252b85a02e9583cf6a
    apiInfo:
      id: 672b52252b85a02e9583cf6a
      name: Appdev User Management Service
      displayName: Appdev User Management Service
      description: API for the Appdev User Management Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/appdev-user-management
      version: v1.0
      type: HTTP
      createdTime: "1730892325294"
      provider: 749f8806-73d2-4a26-99d0-399de85e5586
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 749f8806-73d2-4a26-99d0-399de85e5586
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 27a755a7-9361-4c92-adc0-a8d39ed224d4
      name: Appdev STS App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for Choreo Appdev STS
      status: APPROVED
      groups: []
      subscriptionCount: 2
      attributes: {}
      owner: 0228631a-5468-445b-af43-ba78b5468457
      createdTime: "1730179082677"
      updatedTime: "1730179082677"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 1043bfbf-f16c-46e6-af42-632174f2ecb1
    applicationId: 27a755a7-9361-4c92-adc0-a8d39ed224d4
    apiId: 67adf68adff4a5334bdd64ff
    apiInfo:
      id: 67adf68adff4a5334bdd64ff
      name: Choreo Token Revocation Service
      displayName: Choreo Token Revocation Service
      description: API for the Choreo Token Revocation Service.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-token-revocation-service
      version: v1.0
      type: HTTP
      createdTime: "1739454090685"
      provider: a579828c-19e4-4406-92f4-15df9033c2fc
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: a579828c-19e4-4406-92f4-15df9033c2fc
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 27a755a7-9361-4c92-adc0-a8d39ed224d4
      name: Appdev STS App
      throttlingPolicy: Unlimited
      description: This application is used to generates OAuth tokens for Choreo Appdev STS
      status: APPROVED
      groups: []
      subscriptionCount: 4
      attributes: {}
      owner: 0228631a-5468-445b-af43-ba78b5468457
      createdTime: "1730179082677"
      updatedTime: "1730179082677"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
