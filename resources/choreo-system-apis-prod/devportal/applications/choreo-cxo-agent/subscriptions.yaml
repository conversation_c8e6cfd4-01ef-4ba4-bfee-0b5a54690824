subscriptions:
  - subscriptionId: c5ae362d-0acd-4c9e-aec8-99660a1364db
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 631aa4195fb46c53f7971f88
    apiInfo:
      id: 631aa4195fb46c53f7971f88
      name: Choreo DevOps Portal API
      displayName: Choreo DevOps Portal API
      description: This api is used to connect to the Choreo DevOps Portal API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/devops
      version: 1.0.0
      type: HTTP
      createdTime: "1662690329318"
      provider: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 1cb5568c-c33c-41c4-b1bb-d62a3d40209a
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 1
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 3a96f292-4bb5-4692-b329-56cddc5de6fa
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 66f15212fe856218b0c32c84
    apiInfo:
      id: 66f15212fe856218b0c32c84
      name: Choreo Governance Service API
      displayName: Choreo Governance Service API
      description: API for governing Choreo APIs/Components and other resources.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/governance
      version: v1.0
      type: HTTP
      createdTime: "1727091218612"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 2
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 2011fcd3-b4ed-4db5-9c90-3fc85138101c
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 64bfa4eb012b9122ece541b1
    apiInfo:
      id: 64bfa4eb012b9122ece541b1
      name: Choreo Logging API
      displayName: Choreo Logging API
      description: API for retrieving logs of choreo user applications.
      context: /5659b6b7-1063-41ed-8e39-d91857699255/systemapis/choreologgingapi
      version: 0.2.0
      type: HTTP
      createdTime: "1690281195822"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://choreo-loggingapi.choreo-observability.svc.cluster.local:9097/loggingapi
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 3
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 590920fc-c79c-4d82-b2ce-cb1e672fb044
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 670e3f6055f33d048d031340
    apiInfo:
      id: 670e3f6055f33d048d031340
      name: Component Utils
      displayName: Component Utils
      description: This is Component Management Utils API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils
      version: 1.0.0
      type: HTTP
      createdTime: "1728986976365"
      provider: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d03b0b16-7db3-4ba1-a60b-e8601d0998cb
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 4
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: f10616e3-d9eb-476b-83ed-b4c1bb201d7c
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 645c87bf29258d5e5cd9125d
    apiInfo:
      id: 645c87bf29258d5e5cd9125d
      name: Components Management
      displayName: Components Management
      description: This is the Choreo Components Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1683785663821"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 5
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 9aa0204f-cc6b-4a91-a179-c573a8d2f6dd
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 64140b1f0b438a1df721516c
    apiInfo:
      id: 64140b1f0b438a1df721516c
      name: Config Management
      displayName: Config Management
      description: This is the Choreo Global Configuration Service API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mgt
      version: 1.0.0
      type: HTTP
      createdTime: "1679035167851"
      provider: 417d51b1-f15b-489a-881a-4bfd29a22117
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 417d51b1-f15b-489a-881a-4bfd29a22117
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 6
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: b744a4ed-e7c5-4970-acbb-47988ee1d6e3
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 654350269e53c83b329ab097
    apiInfo:
      id: 654350269e53c83b329ab097
      name: Configuration Mapping Service
      displayName: Configuration Mapping Service
      description: This api is used to connect to the Configuration Mapping Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910246275"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 7
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: f025074d-5312-4fd2-b5d4-784bd56a84c0
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 6544d9e29a886f6d1afddd22
    apiInfo:
      id: 6544d9e29a886f6d1afddd22
      name: Connection Management
      displayName: Connection Management
      description: This api is used to connect to the Connection Management service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections
      version: v1.0
      type: HTTP
      createdTime: "1699011042225"
      provider: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://connection-service.prod-choreo-system:9000/connections/v1
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 89c19b60-6bdc-4893-becd-ab7a6eff29b2
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 8
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 13cf74e7-a833-4ff4-987b-68f82d4911e9
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 6541ed3532503454e1085aa3
    apiInfo:
      id: 6541ed3532503454e1085aa3
      name: Declarative API
      displayName: Declarative API
      description: This api is used to connect to the Declarative API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/declarative-api
      version: v1.0
      type: HTTP
      createdTime: "1698819381865"
      provider: 5072771b-6e31-41d3-8964-2f4fb991d6e3
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://declarative-api.prod-choreo-system:8080/api
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5072771b-6e31-41d3-8964-2f4fb991d6e3
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 9
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 62cd5602-1793-43e4-ac20-9d72f35b31ce
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 6486b08be88a407b4f8f5ff1
    apiInfo:
      id: 6486b08be88a407b4f8f5ff1
      name: Marketplace
      displayName: Marketplace
      description: This api is used to connect to the Marketplace service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/marketplace
      version: 0.1.0
      type: HTTP
      createdTime: "1686548619356"
      provider: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 5c7374dd-2b9f-432c-b174-9ac4e2bc6caa
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 10
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 499497bf-c452-49d1-bb10-240fff9f5c8c
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 64b4e04e6f73c71fa9969691
    apiInfo:
      id: 64b4e04e6f73c71fa9969691
      name: Observability Manager
      displayName: Observability Manager
      description: This api is used to connect to the Observability Manager service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager
      version: 0.1.0
      type: HTTP
      createdTime: "1689575502411"
      provider: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Silver
        - Gold
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: eb01e9eb-0118-4eab-abb4-3b414b00ca2c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 11
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: be8fd65b-343e-4f0f-a754-b53285a9d93b
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 64742bf8240d4717b1131bcc
    apiInfo:
      id: 64742bf8240d4717b1131bcc
      name: Organization API
      displayName: Organization API
      description: This is the Choreo Runtime Organization API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/orgs
      version: 1.0.0
      type: HTTP
      createdTime: "1685335032726"
      provider: d63bd494-74d1-4ee6-a98d-c303dcfb6623
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: d63bd494-74d1-4ee6-a98d-c303dcfb6623
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 12
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: a1811d28-c2e8-4fe6-a842-27c7caf84d6e
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 62419cc7690bbb0c8c544443
    apiInfo:
      id: 62419cc7690bbb0c8c544443
      name: Project API
      displayName: Project API
      description: This api is used to connect to the Project API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/projects
      version: 1.0.0
      type: HTTP
      createdTime: "1648467143810"
      provider: 2d92c502-cb6c-411e-865d-e25dace20279
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 2d92c502-cb6c-411e-865d-e25dace20279
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: null
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 13
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: a0697ee6-390e-4bb3-a827-db876734a18e
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 66ea50d5ebf87a2d63485eb1
    apiInfo:
      id: 66ea50d5ebf87a2d63485eb1
      name: Workflow Management API
      displayName: Workflow Management API
      description: Workflow Management API
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/workflow-mgt
      version: v1.0
      type: HTTP
      createdTime: "1726632149432"
      provider: 678ead13-dc75-4004-9fc0-35cd45eddf8f
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 678ead13-dc75-4004-9fc0-35cd45eddf8f
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 14
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
  - subscriptionId: 1deee1db-b58a-4bd9-8e4e-f9db8d9f2ba0
    applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
    apiId: 6543529f9e53c83b329ab0a5
    apiInfo:
      id: 6543529f9e53c83b329ab0a5
      name: Configuration Service
      displayName: Configuration Service
      description: This api is used to connect to the Configuration Service service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-svc
      version: v1.0
      type: HTTP
      createdTime: "1698910879023"
      provider: 0db20236-8369-43bc-92de-bdac53f3fd7c
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: https://api.choreo.dev
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0db20236-8369-43bc-92de-bdac53f3fd7c
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 51d6e2dc-0bde-4fe0-a460-5b7dc17b4de6
      name: Choreo CxO Agent
      throttlingPolicy: Unlimited
      description: ""
      status: APPROVED
      groups: []
      subscriptionCount: 15
      attributes: {}
      owner: 2197b2ec-669b-4d6c-a64a-714569289df2
      createdTime: "1751341677263"
      updatedTime: "1751341677263"
    throttlingPolicy: Bronze
    requestedThrottlingPolicy: Bronze
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: null
