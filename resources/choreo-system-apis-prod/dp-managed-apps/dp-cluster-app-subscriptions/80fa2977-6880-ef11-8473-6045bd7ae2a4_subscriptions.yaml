subscriptions:
  - subscriptionId: 8c0ac5cd-65f0-4864-8cff-53faffb686d3
    applicationId: ff07bc41-a408-4b1f-a6ec-9c7a14223e7e
    apiId: 67ca8c1946d9173bc9cc0a24
    apiInfo:
      id: 67ca8c1946d9173bc9cc0a24
      name: cost optimizer cp service
      displayName: cost optimizer cp service
      description: This api is used to connect to the cost optimizer cp service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cost-optimizer-cp
      version: v1.0
      type: HTTP
      createdTime: "1741327385172"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 67ca8c1946d9173bc9cc0a24
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: ff07bc41-a408-4b1f-a6ec-9c7a14223e7e
      name: pdp-system-app-80fa2977-6880-ef11-8473-6045bd7ae2a4
      throttlingPolicy: Unlimited
      description: 'Choreo Managed System Application created for org: e255af5a-b68b-4821-aa87-3058d39c35b7 and ClusterId: 80fa2977-6880-ef11-8473-6045bd7ae2a4'
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isManagementApp: "true"
        isChoreoManaged: "true"
      owner: prod-pdp-system-user
      createdTime: "1747626547187"
      updatedTime: "1747626547187"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-07-07 10:36:46.65"
  - subscriptionId: a781d945-4565-4f36-a471-a8c42136c7b2
    applicationId: ff07bc41-a408-4b1f-a6ec-9c7a14223e7e
    apiId: 61d293d43234be4e91a47af3
    apiInfo:
      id: 61d293d43234be4e91a47af3
      name: NotificationService
      displayName: NotificationService
      description: This API is used to connect to the Service Openapi Yaml service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/notification-service
      version: 1.0.0
      type: HTTP
      createdTime: "1641190356623"
      provider: 4c1b2770-c9ac-49aa-a5ec-08d90014e95b
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4c1b2770-c9ac-49aa-a5ec-08d90014e95b
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: ff07bc41-a408-4b1f-a6ec-9c7a14223e7e
      name: pdp-system-app-80fa2977-6880-ef11-8473-6045bd7ae2a4
      throttlingPolicy: Unlimited
      description: 'Choreo Managed System Application created for org: e255af5a-b68b-4821-aa87-3058d39c35b7 and ClusterId: 80fa2977-6880-ef11-8473-6045bd7ae2a4'
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isManagementApp: "true"
        isChoreoManaged: "true"
      owner: prod-pdp-system-user
      createdTime: "1747626547187"
      updatedTime: "1747626547187"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-05-19 03:49:09.107"
  - subscriptionId: 89ec5614-8740-4501-82a0-99be081b47b1
    applicationId: ff07bc41-a408-4b1f-a6ec-9c7a14223e7e
    apiId: 6541172854bcdb21d2a334e4
    apiInfo:
      id: 6541172854bcdb21d2a334e4
      name: Platform Services Manager API
      displayName: Platform Services Manager API
      description: This api is used to connect to the Platform Services Manager API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/platform-services
      version: v1.0
      type: HTTP
      createdTime: "1698764584558"
      provider: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://platform-services-manager.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6541172854bcdb21d2a334e4
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: ff07bc41-a408-4b1f-a6ec-9c7a14223e7e
      name: pdp-system-app-80fa2977-6880-ef11-8473-6045bd7ae2a4
      throttlingPolicy: Unlimited
      description: 'Choreo Managed System Application created for org: e255af5a-b68b-4821-aa87-3058d39c35b7 and ClusterId: 80fa2977-6880-ef11-8473-6045bd7ae2a4'
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isManagementApp: "true"
        isChoreoManaged: "true"
      owner: prod-pdp-system-user
      createdTime: "1747626547187"
      updatedTime: "1747626547187"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-05-19 03:49:08.293"
