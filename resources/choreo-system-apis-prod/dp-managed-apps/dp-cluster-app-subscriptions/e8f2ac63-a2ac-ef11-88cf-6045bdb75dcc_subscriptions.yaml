subscriptions:
  - subscriptionId: de7f8217-195b-4e50-8bf7-44ae53da3209
    applicationId: 1266d4fc-4f04-48f3-8cda-b4f8858e0b17
    apiId: 67ca8c1946d9173bc9cc0a24
    apiInfo:
      id: 67ca8c1946d9173bc9cc0a24
      name: cost optimizer cp service
      displayName: cost optimizer cp service
      description: This api is used to connect to the cost optimizer cp service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cost-optimizer-cp
      version: v1.0
      type: HTTP
      createdTime: "1741327385172"
      provider: 0228631a-5468-445b-af43-ba78b5468457
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 0228631a-5468-445b-af43-ba78b5468457
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 67ca8c1946d9173bc9cc0a24
      additionalProperties:
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 1266d4fc-4f04-48f3-8cda-b4f8858e0b17
      name: pdp-system-app-e8f2ac63-a2ac-ef11-88cf-6045bdb75dcc
      throttlingPolicy: Unlimited
      description: 'Choreo Managed System Application created for org: 997d45a7-39a3-4ee2-a158-5ffd8eab4697 and ClusterId: e8f2ac63-a2ac-ef11-88cf-6045bdb75dcc'
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isManagementApp: "true"
        isChoreoManaged: "true"
      owner: prod-pdp-system-user
      createdTime: "1747635051463"
      updatedTime: "1747635051463"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-07-07 10:37:32.493"
  - subscriptionId: 3eca3f1e-0833-472b-8ed4-1594acace7e0
    applicationId: 1266d4fc-4f04-48f3-8cda-b4f8858e0b17
    apiId: 61d293d43234be4e91a47af3
    apiInfo:
      id: 61d293d43234be4e91a47af3
      name: NotificationService
      displayName: NotificationService
      description: This API is used to connect to the Service Openapi Yaml service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/notification-service
      version: 1.0.0
      type: HTTP
      createdTime: "1641190356623"
      provider: 4c1b2770-c9ac-49aa-a5ec-08d90014e95b
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Bronze
        - Unlimited
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: null
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 4c1b2770-c9ac-49aa-a5ec-08d90014e95b
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo: null
      additionalProperties: []
    applicationInfo:
      applicationId: 1266d4fc-4f04-48f3-8cda-b4f8858e0b17
      name: pdp-system-app-e8f2ac63-a2ac-ef11-88cf-6045bdb75dcc
      throttlingPolicy: Unlimited
      description: 'Choreo Managed System Application created for org: 997d45a7-39a3-4ee2-a158-5ffd8eab4697 and ClusterId: e8f2ac63-a2ac-ef11-88cf-6045bdb75dcc'
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isManagementApp: "true"
        isChoreoManaged: "true"
      owner: prod-pdp-system-user
      createdTime: "1747635051463"
      updatedTime: "1747635051463"
    throttlingPolicy: Unlimited
    requestedThrottlingPolicy: Unlimited
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-05-19 06:10:53.427"
  - subscriptionId: b83ce96d-4539-4fc8-b580-fa5c97d0f2d0
    applicationId: 1266d4fc-4f04-48f3-8cda-b4f8858e0b17
    apiId: 6541172854bcdb21d2a334e4
    apiInfo:
      id: 6541172854bcdb21d2a334e4
      name: Platform Services Manager API
      displayName: Platform Services Manager API
      description: This api is used to connect to the Platform Services Manager API service
      context: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/platform-services
      version: v1.0
      type: HTTP
      createdTime: "1698764584558"
      provider: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
      lifeCycleStatus: PUBLISHED
      thumbnailUri: null
      avgRating: "0.0"
      throttlingPolicies:
        - Gold
      advertiseInfo:
        advertised: false
        apiExternalProductionEndpoint: http://platform-services-manager.prod-choreo-system.svc.cluster.local:80
        apiExternalSandboxEndpoint: null
        originalDevPortalUrl: null
        apiOwner: 3a81d2b5-d642-4719-b529-5b0b7f7ecae6
        vendor: WSO2
      businessInformation:
        businessOwner: null
        businessOwnerEmail: null
        technicalOwner: null
        technicalOwnerEmail: null
      isSubscriptionAvailable: true
      monetizationLabel: FREE
      gatewayVendor: wso2
      versionInfo:
        version: v1.0
        majorRange:
          isLatest: true
          latestVersion: v1.0
          latestVersionAPIId: 6541172854bcdb21d2a334e4
      additionalProperties:
        - name: projectId
          value: 2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8
          display: true
        - name: accessibility
          value: external
          display: true
    applicationInfo:
      applicationId: 1266d4fc-4f04-48f3-8cda-b4f8858e0b17
      name: pdp-system-app-e8f2ac63-a2ac-ef11-88cf-6045bdb75dcc
      throttlingPolicy: Unlimited
      description: 'Choreo Managed System Application created for org: 997d45a7-39a3-4ee2-a158-5ffd8eab4697 and ClusterId: e8f2ac63-a2ac-ef11-88cf-6045bdb75dcc'
      status: APPROVED
      groups: []
      subscriptionCount: 0
      attributes:
        isManagementApp: "true"
        isChoreoManaged: "true"
      owner: prod-pdp-system-user
      createdTime: "1747635051463"
      updatedTime: "1747635051463"
    throttlingPolicy: Gold
    requestedThrottlingPolicy: Gold
    status: UNBLOCKED
    redirectionParams: null
    versionRange: null
    createdTime: "2025-05-19 06:10:52.61"
