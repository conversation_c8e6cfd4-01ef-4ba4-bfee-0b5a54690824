# choreo-system-apis/dp-managed-apps

This folder contains the following file:

1. subscriptions.yaml
This file lists the internal system API IDs along with their throttling policies that the Choreo PDP Management Applications should subscribe to.

All internal system APIs that require subscription by choreo-managed applications should be added here.

Example:-

```
subscriptions:
  - apiId: 62ade5ecde04114123456
    throttlingPolicy: Bronze
```

## List of API subscriptions

| API Name | API Id | Documentation | Throttling Policy |
| --- | --- | --- | --- |
| platform-services-manager-api| 6541172854bcdb21d2a334e4 | [OpenAPI Spec](../cp/platform-services-manager-api/v1.0/openapi.yaml) | Gold |
| NotificationService | 61d293d43234be4e91a47af3 | [OpenAPI Spec](../cp/notificationservice/1.0.0/openapi.yaml) | Unlimited |
| cost-optimizer-cp-service| 67ca8c1946d9173bc9cc0a24 | [OpenAPI Spec](../cp/cost-optimizer-cp-service/1.0.0/openapi.yaml) | Unlimited |
