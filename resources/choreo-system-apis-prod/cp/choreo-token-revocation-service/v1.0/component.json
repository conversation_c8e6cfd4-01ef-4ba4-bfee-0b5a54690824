{"data": {"component": {"id": "2235289c-e30a-4b69-842a-81f9aaeabcca", "name": "choreo-token-revoke-svc", "handler": "choreo-token-revoke-svc", "description": " ", "displayType": "proxy", "displayName": "Choreo Token Revocation Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-02-13T14:01:02.013Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Choreo Token Revocation Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-token-revocation-service", "proxyId": "67adf68adff4a5334bdd64ff", "id": "67adf68adff4a5334bdd64ff", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67adf68adff4a5334bdd64ff", "createdAt": "1739454090685", "updatedAt": "2025-02-13 13:41:30.685", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "2235289c-e30a-4b69-842a-81f9aaeabcca", "latest": true, "versionStrategy": ""}]}}}