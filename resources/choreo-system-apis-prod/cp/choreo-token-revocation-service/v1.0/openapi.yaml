openapi: 3.0.3
info:
  title: Choreo Token Revocation Service
  description: API for the Choreo Token Revocation Service.
  contact: {}
  version: v1.0
servers:
  - url: https://app.choreo.dev/93tu/choreo-token-revocation-service/v1.0
security:
  - default: []
paths:
  /notification/revoke:
    post:
      summary: Revoke OAuth2 tokens
      operationId: revokeToken
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRevocationRequest'
      responses:
        "200":
          description: No Content.
          content: {}
        "400":
          description: Invalid input request.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthError'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthError'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthError'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    TokenRevocationRequest:
      type: object
      properties:
        accessToken:
          type: string
          example: ntjLVu4hymelMbJ3Z2VKOqS5C8ps1zKK.DFH3ur6L7tRtl1fuHuux36BjNfC2Y7Y1.w6xRRcLElFCV12XH27bMbwJemMCFLfUV
    OAuthError:
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: string
          example: AAA-00000
        message:
          type: string
          example: Some Error Message
        description:
          type: string
          example: Some Error Description
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://sts-mgt-service.prod-choreo-system.svc.cluster.local:3000/api/v1/sts-mgt
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://sts-mgt-service.prod-choreo-system.svc.cluster.local:3000/api/v1/sts-mgt
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-token-revocation-service/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
