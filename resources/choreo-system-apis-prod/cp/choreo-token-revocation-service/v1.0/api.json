{"id": "67adf68adff4a5334bdd64ff", "name": "Choreo Token Revocation Service", "displayName": "Choreo Token Revocation Service", "description": "API for the Choreo Token Revocation Service.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-token-revocation-service", "version": "v1.0", "provider": "a579828c-19e4-4406-92f4-15df9033c2fc", "lifeCycleStatus": "CREATED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": ["admin"], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": false, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1739454090685", "lastUpdatedTime": "2025-02-13 13:41:30.685", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://sts-mgt-service.prod-choreo-system.svc.cluster.local:3000/api/v1/sts-mgt"}, "production_endpoints": {"url": "http://sts-mgt-service.prod-choreo-system.svc.cluster.local:3000/api/v1/sts-mgt"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/notification/revoke", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "a579828c-19e4-4406-92f4-15df9033c2fc", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "2235289c-e30a-4b69-842a-81f9aaeabcca", "versionId": "67adf68adff4a5334bdd64ff"}}