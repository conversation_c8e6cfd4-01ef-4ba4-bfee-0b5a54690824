{"data": {"component": {"id": "b58e4761-7371-406d-9620-3b8d5600c2f4", "name": "Choreo Governance Service API", "handler": "Choreo Governance Service API", "description": " ", "displayType": "proxy", "displayName": "Choreo Governance Service API", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-09-23T12:00:51.798Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Choreo Governance Service API", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/governance", "proxyId": "66f15212fe856218b0c32c84", "id": "66f15212fe856218b0c32c84", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "66f15212fe856218b0c32c84", "createdAt": "1727091218612", "updatedAt": "2024-09-23 11:33:38.612", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "b58e4761-7371-406d-9620-3b8d5600c2f4", "latest": true, "versionStrategy": ""}]}}}