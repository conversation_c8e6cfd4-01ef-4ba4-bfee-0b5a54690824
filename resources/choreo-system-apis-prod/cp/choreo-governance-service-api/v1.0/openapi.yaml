openapi: 3.0.3
info:
  title: Choreo Governance Service API
  description: API for managing rulesets, governance policies, and validation statistics.
  contact: {}
  version: v1.0
servers:
  - url: http://localhost:8080/93tu/governance/v1.0
    description: Local server
security:
  - default: []
tags:
  - name: Rulesets
    description: API for managing rulesets.
  - name: Governance Policies
    description: API for managing governance policies.
  - name: Compliance Assessment
    description: API for accessing compliance details.
  - name: Compliance Details
    description: API for managing compliance details.
  - name: Adherence Details
    description: API for accessing adherence details.
  - name: Organization Level Dashboard
    description: API for organization level dashboard.
  - name: Project Level Dashboard
    description: API for project level dashboard.
  - name: Component Level Dashboard
    description: API for component level dashboard.
  - name: Health Check
    description: API for health check.
  - name: External
    description: API resources that are exposed to the external world via gateway.
  - name: Internal
    description: API resources that are not exposed to the external world.
  - name: Deprecated
    description: API resources that are deprecated.
paths:
  /rulesets:
    get:
      tags:
        - Rulesets
        - External
      summary: Retrieves a list of rulesets.
      description: Returns a list of all rulesets associated with the requested organization.
      operationId: getRulesets
      responses:
        "200":
          description: OK. Successful response with a list of rulesets.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RulesetList'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - Rulesets
        - External
      summary: Create a new ruleset.
      description: Creates a new ruleset in the user's organization.
      operationId: createRuleset
      requestBody:
        description: JSON object containing the details of the new ruleset.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Ruleset'
        required: true
      responses:
        "201":
          description: OK. Ruleset created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ruleset'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /rulesets/{rulesetId}:
    get:
      tags:
        - Rulesets
        - External
      summary: Retrieves details of a specific ruleset.
      description: Retrieves details of the ruleset identified by the rulesetId.
      operationId: getRulesetById
      parameters:
        - name: rulesetId
          in: path
          description: UUID of the ruleset.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Ruleset details retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RulesetInfo'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - Rulesets
        - External
      summary: Updates a specific ruleset.
      description: Updates the details of the ruleset identified by the `rulesetId`.
      operationId: updateRulesetById
      parameters:
        - name: rulesetId
          in: path
          description: UUID of the ruleset.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: JSON object containing the updated ruleset details.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Ruleset'
        required: true
      responses:
        "200":
          description: OK. Ruleset updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Ruleset'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - Rulesets
        - External
      summary: Deletes a specific ruleset.
      description: Deletes an existing ruleset identified by the rulesetId.
      operationId: deleteRuleset
      parameters:
        - name: rulesetId
          in: path
          description: UUID of the ruleset.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "204":
          description: OK. Ruleset deleted successfully.
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /rulesets/{rulesetId}/content:
    get:
      tags:
        - Rulesets
        - External
      summary: Retrieves the content of a specific ruleset.
      description: Retrieves the content of the ruleset identified by the rulesetId.
      operationId: getRulesetContent
      parameters:
        - name: rulesetId
          in: path
          description: UUID of the ruleset.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Ruleset content retrieved successfully.
          content:
            application/x-yaml:
              schema:
                type: string
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /documents:
    get:
      tags:
        - Documents
        - External
      summary: Retrieves a list of documents.
      description: Returns a list of all documents associated with the requested organization.
      operationId: getDocuments
      responses:
        "200":
          description: OK. Successful response with a list of rulesets.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentList'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - Documents
        - External
      summary: Create a new document.
      description: Creates a new document in the user's organization.
      operationId: createDocument
      requestBody:
        description: JSON object containing the details of the new ruleset.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentInfo'
        required: true
      responses:
        "201":
          description: OK. Document created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentInfo'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /documents/{documentId}:
    get:
      tags:
        - Documents
        - External
      summary: Retrieves details of a specific document.
      description: Retrieves details of the document identified by the documentId.
      operationId: getDocumentById
      parameters:
        - name: documentId
          in: path
          description: UUID of the document.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Document details retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentInfo'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - Documents
        - External
      summary: Updates a specific document.
      description: Updates the details of the document identified by the documentId.
      operationId: updateDocumentById
      parameters:
        - name: documentId
          in: path
          description: UUID of the document.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: JSON object containing the updated document details.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentInfo'
        required: true
      responses:
        "200":
          description: OK. Document updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentInfo'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - Documents
        - External
      summary: Deletes a specific document.
      description: Deletes an existing document identified by the documentId.
      operationId: deleteDocument
      parameters:
        - name: documentId
          in: path
          description: UUID of the document.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "204":
          description: OK. Document deleted successfully.
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /governance-policies:
    get:
      tags:
        - Governance Policies
        - External
      summary: Retrieves a list of all governance policies.
      description: Retrieves a list of governance policies for the user's organization.
      operationId: getGovernancePolicies
      responses:
        "200":
          description: OK. Successful response with a list of governance policies.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GovernancePolicyList'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - Governance Policies
        - External
      summary: Creates a new governance policy.
      description: Creates a new governance policy for the user's organization.
      operationId: createGovernancePolicy
      requestBody:
        description: JSON object containing the details of the new governance policy.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GovernancePolicyInfoWithRulesetOrDocumentIds'
        required: true
      responses:
        "201":
          description: OK. Governance policy created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GovernancePolicyInfo'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /governance-policies/{policyId}:
    get:
      tags:
        - Governance Policies
        - External
      summary: Get a specific governance policy
      description: Retrieves details of a specific governance policy identified by the policyId.
      operationId: getGovernancePolicyById
      parameters:
        - name: policyId
          in: path
          description: UUID of the governance policy.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Governance policy details retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GovernancePolicyInfo'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - Governance Policies
        - External
      summary: Update a specific governance policy
      description: Updates the details of an existing governance policy identified by the policyId.
      operationId: updateGovernancePolicyById
      parameters:
        - name: policyId
          in: path
          description: UUID of the governance policy.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: JSON object containing the updated governance policy details.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GovernancePolicyInfoWithRulesetOrDocumentIds'
        required: true
      responses:
        "200":
          description: OK. Governance policy updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GovernancePolicyInfo'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - Governance Policies
        - External
      summary: Delete a specific governance policy
      description: Deletes an existing governance policy identified by the policyId.
      operationId: deleteGovernancePolicy
      parameters:
        - name: policyId
          in: path
          description: UUID of the governance policy.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "204":
          description: OK. Governance policy deleted successfully.
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /project-compliance:
    get:
      tags:
        - Compliance Details
        - Organization Level Dashboard
        - External
      summary: Retrieves compliance details of all projects of an organization.
      description: Retrieves compliance details of all projects of a given organization.
      operationId: getProjectComplianceDetails
      responses:
        "200":
          description: OK. Successful response with project compliance details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectComplianceDetails'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /projects/{projectId}/component-compliance:
    get:
      tags:
        - Compliance Details
        - Project Level Dashboard
        - External
      summary: Retrieves compliance details of all components of a project.
      description: Retrieves compliance details of all components of a given project.
      operationId: getComponentComplianceDetails
      parameters:
        - name: projectId
          in: path
          description: UUID of the project.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Successful response with component compliance details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentComplianceDetails'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /policy-adherence:
    get:
      tags:
        - Adherence Details
        - Organization Level Dashboard
        - External
      summary: Retrieves policy adherence of all projects of an organization.
      description: Retrieves policy adherence details of all projects of a given organization.
      operationId: getOrganizationPolicyAdherenceDetails
      responses:
        "200":
          description: OK. Successful response with policy adherence details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationPolicyAdherenceDetails'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /projects/{projectId}/policy-adherence:
    get:
      tags:
        - Adherence Details
        - Project Level Dashboard
        - External
      summary: Retrieves policy adherence details for a specific project.
      description: Retrieves detailed adherence information for policies within the specified project.
      operationId: getProjectPolicyAdherenceDetails
      parameters:
        - name: projectId
          in: path
          description: UUID of the project.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Successful response with project policy adherence details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectPolicyAdherenceDetails'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /projects/{projectId}/components/{componentId}/endpoints/{endpointId}/policy-adherence:
    get:
      tags:
        - Adherence Details
        - Component Level Dashboard
        - External
        - Deprecated
      summary: Retrieves policy adherence of a specific endpoint.
      description: Retrieves policy adherence details of a specific endpoint.
      operationId: getEndpointPolicyAdherenceDetails
      parameters:
        - name: projectId
          in: path
          description: UUID of the project.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: componentId
          in: path
          description: UUID of the component.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: endpointId
          in: path
          description: UUID of the endpoint.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Successful response with policy adherence details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EndpointPolicyAdherenceDetails'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /projects/{projectId}/components/{componentId}/endpoints/{endpointId}/ruleset-adherence:
    get:
      tags:
        - Adherence Details
        - Component Level Dashboard
        - External
        - Deprecated
      summary: Retrieves ruleset adherence details.
      description: Retrieves detailed adherence information for rulesets within the specified endpoint.
      operationId: getEndpointRulesetAdherenceDetails
      parameters:
        - name: projectId
          in: path
          description: UUID of the project.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: componentId
          in: path
          description: UUID of the component.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: endpointId
          in: path
          description: UUID of the endpoint.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Successful response with ruleset adherence details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EndpointRulesetAdherenceDetails'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /projects/{projectId}/components/{componentId}/endpoints/{endpointId}/ruleset-adherence/{rulesetId}/rule-adherence:
    get:
      tags:
        - Adherence Details
        - Component Level Dashboard
        - External
        - Deprecated
      summary: Retrieves rule-level compliance details for a specific ruleset.
      description: Retrieves detailed compliance information for individual rules within the specified ruleset.
      operationId: getEndpointRulesetRuleAdherenceDetails
      parameters:
        - name: projectId
          in: path
          description: UUID of the project.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: componentId
          in: path
          description: UUID of the component.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: endpointId
          in: path
          description: UUID of the endpoint.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: rulesetId
          in: path
          description: UUID of the ruleset.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Successful response with rules compliance details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ViolatedRules'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /projects/{projectId}/components/{componentId}/endpoints/{endpointId}/rule-adherence:
    get:
      tags:
        - Adherence Details
        - Component Level Dashboard
        - External
      summary: Retrieves all rule-level compliance details of an endpoint.
      description: Retrieves detailed compliance information for individual rules of an endpoint.
      operationId: getEndpointRuleAdherenceDetails
      parameters:
        - name: projectId
          in: path
          description: UUID of the project.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: componentId
          in: path
          description: UUID of the component.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: endpointId
          in: path
          description: UUID of the endpoint.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK. Successful response with rules compliance details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EndpointRuleAdherenceDetails'
        "400":
          description: Client error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    RulesetList:
      type: object
      properties:
        count:
          type: integer
          description: Number of rulesets returned.
          example: 2
        list:
          type: array
          description: List of rulesets.
          items:
            $ref: '#/components/schemas/RulesetInfo'
      description: A list of rulesets.
    Ruleset:
      required:
        - appliesTo
        - name
        - provider
        - rulesetContent
      type: object
      properties:
        id:
          type: string
          description: UUID of the ruleset.
          readOnly: true
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: Name of the ruleset.
          example: API Security Ruleset
        description:
          type: string
          description: A brief description of the ruleset.
          example: A ruleset designed to enforce security standards for APIs.
        rulesetContent:
          type: string
          description: The content of the ruleset file (YAML or JSON).
          example: |
            rules:
              oas2-always-use-https:
                given:
                  - '$.schemes[*]'
                severity: error
                then:
                  function: enumeration
                  functionOptions:
                    values:
                      - https
                description: >-
                  Host schemes must use the HTTPS protocol. Applies to: OpenAPI 2.0`
                message: API host schemes must use the HTTPS protocol.
                formats:
                  - oas2
        appliesTo:
          type: string
          description: Context or area to which the ruleset applies.
          example: api_definition
          enum:
            - api_metadata
            - api_definition
            - documentation
        documentationLink:
          type: string
          description: URL to the documentation related to the ruleset.
          example: https://example.com/docs/api-security-ruleset
        provider:
          type: string
          description: Entity or individual providing the ruleset.
          example: TechWave
        createdBy:
          type: string
          description: Identifier of the user who created the ruleset.
          readOnly: true
          example: <EMAIL>
        createdTime:
          type: string
          description: Timestamp when the ruleset was created.
          readOnly: true
          example: "2024-08-01T12:00:00Z"
        updatedBy:
          type: string
          description: Identifier of the user who last updated the ruleset.
          readOnly: true
          example: <EMAIL>
        updatedTime:
          type: string
          description: Timestamp when the ruleset was last updated.
          readOnly: true
          example: "2024-08-10T12:00:00Z"
      description: Detailed information about a ruleset.
    RulesetInfo:
      required:
        - appliesTo
        - name
        - provider
        - ruleset_file
      type: object
      properties:
        id:
          type: string
          description: UUID of the ruleset.
          readOnly: true
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: Name of the ruleset.
          example: API Security Ruleset
        description:
          type: string
          description: A brief description of the ruleset.
          example: A ruleset designed to enforce security standards for APIs.
        appliesTo:
          type: string
          description: Context or area to which the ruleset applies.
          enum:
            - api_metadata
            - api_definition
            - documentation
        documentationLink:
          type: string
          description: URL to the documentation related to the ruleset.
          example: https://example.com/docs/api-security-ruleset
        provider:
          type: string
          description: Entity or individual providing the ruleset.
          example: TechWave
        createdBy:
          type: string
          description: Identifier of the user who created the ruleset.
          readOnly: true
          example: <EMAIL>
        createdTime:
          type: string
          description: Timestamp when the ruleset was created.
          readOnly: true
          example: "2024-08-01T12:00:00Z"
        updatedBy:
          type: string
          description: Identifier of the user who last updated the ruleset.
          readOnly: true
          example: <EMAIL>
        updatedTime:
          type: string
          description: Timestamp when the ruleset was last updated.
          readOnly: true
          example: "2024-08-02T12:00:00Z"
        isDefault:
          type: boolean
          description: Whether the ruleset is a default one or not.
          example: true
      description: Detailed information about a ruleset.
    DocumentList:
      type: object
      properties:
        count:
          type: integer
          description: Number of documents returned.
          example: 2
        list:
          type: array
          description: List of documents.
          items:
            $ref: '#/components/schemas/DocumentInfo'
      description: A list of documents.
    DocumentInfo:
      required:
        - appliesTo
        - content
        - name
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the document.
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: Name of the document.
          example: API Security Standards
        description:
          type: string
          description: A brief description of the document.
          example: A document designed to enforce security standards for APIs.
        appliesTo:
          type: string
          description: Context or area to which the document applies.
          enum:
            - api_metadata
            - api_definition
            - documentation
          default: api_definition
        content:
          type: string
          description: Binary content of the document, encoded as a base64 string.
          format: byte
        createdBy:
          type: string
          description: Identifier of the user who created the document.
          readOnly: true
          example: <EMAIL>
        createdTime:
          type: string
          description: Timestamp when the document was created.
          readOnly: true
          example: "2024-08-01T12:00:00Z"
        updatedBy:
          type: string
          description: Identifier of the user who last updated the document.
          readOnly: true
          example: <EMAIL>
        updatedTime:
          type: string
          description: Timestamp when the document was last updated.
          readOnly: true
          example: "2024-08-10T12:00:00Z"
      description: Details of a document associated with a governance policy.
    GovernancePolicyInfo:
      required:
        - name
        - rulesets
      type: object
      properties:
        id:
          type: string
          description: UUID of the governance policy.
          readOnly: true
          example: 987e6543-d21b-34d5-b678-************
        name:
          type: string
          description: Name of the governance policy.
          example: API Security Policy
        description:
          type: string
          description: A brief description of the governance policy.
          example: Policy for enforcing security standards across all APIs.
        policyType:
          type: string
          description: Type of the policy.
          example: ruleset
          enum:
            - ruleset
            - ai
          default: ruleset
        rulesets:
          type: array
          description: List of rulesets associated with the governance policy.
          items:
            $ref: '#/components/schemas/RulesetInfo'
        documents:
          type: array
          description: List of documents associated with the governance policy.
          items:
            $ref: '#/components/schemas/DocumentInfo'
        labels:
          type: array
          description: Labels associated with the governance policy.
          example:
            - security
          items:
            type: string
        createdBy:
          type: string
          description: Identifier of the user who created the governance policy.
          readOnly: true
          example: <EMAIL>
        createdTime:
          type: string
          description: Timestamp when the governance policy was created.
          readOnly: true
          example: "2024-08-01T12:00:00Z"
        updatedBy:
          type: string
          description: Identifier of the user who last updated the governance policy.
          readOnly: true
          example: <EMAIL>
        updatedTime:
          type: string
          description: Timestamp when the governance policy was last updated.
          readOnly: true
          example: "2024-08-02T12:00:00Z"
      description: Detailed information about a governance policy.
    GovernancePolicyInfoWithRulesetOrDocumentIds:
      required:
        - name
      type: object
      properties:
        id:
          type: string
          description: UUID of the governance policy.
          readOnly: true
          example: 987e6543-d21b-34d5-b678-************
        name:
          type: string
          description: Name of the governance policy.
          example: API Security Policy
        policyType:
          type: string
          description: Type of the policy.
          example: ruleset
          enum:
            - ruleset
            - ai
          default: ruleset
        description:
          type: string
          description: A brief description of the governance policy.
          example: Policy for enforcing security standards across all APIs.
        rulesets:
          type: array
          description: List of ruleset IDs the governance policy.
          items:
            $ref: '#/components/schemas/RulesetId'
        documents:
          type: array
          description: List of document IDs of the governance policy
          items:
            $ref: '#/components/schemas/DocumentId'
        labels:
          type: array
          description: Labels or tags associated with the governance policy.
          example:
            - security
          items:
            type: string
        createdBy:
          type: string
          description: Identifier of the user who created the governance policy.
          readOnly: true
          example: <EMAIL>
        createdTime:
          type: string
          description: Timestamp when the governance policy was created.
          readOnly: true
          example: "2024-08-01T12:00:00Z"
        updatedBy:
          type: string
          description: Identifier of the user who last updated the governance policy.
          readOnly: true
          example: <EMAIL>
        updatedTime:
          type: string
          description: Timestamp when the governance policy was last updated.
          readOnly: true
          example: "2024-08-02T12:00:00Z"
      description: Governance policy information with ruleset IDs.
    RulesetId:
      type: object
      properties:
        id:
          type: string
          description: UUID of the ruleset.
          example: 987e6543-d21b-34d5-b678-************
      description: Ruleset Id information.
    DocumentId:
      type: object
      properties:
        id:
          type: string
          description: UUID of the document.
          example: 987e6543-d21b-34d5-b678-************
      description: Document Id information.
    GovernancePolicyList:
      type: object
      properties:
        count:
          type: integer
          description: Number of governance policies returned.
          example: 10
        list:
          type: array
          description: List of governance policies.
          items:
            $ref: '#/components/schemas/GovernancePolicyInfo'
      description: A list of governance policies.
    ProjectComplianceDetails:
      type: object
      properties:
        summary:
          type: object
          properties:
            project:
              $ref: '#/components/schemas/ComplianceSummaryWithNotApplicable'
            component:
              $ref: '#/components/schemas/ComplianceSummaryWithNotApplicable'
          description: Summary of compliance details of all projects of an organization.
        count:
          type: integer
          description: Total number of projects in the list.
          example: 2
        list:
          type: array
          description: List of project compliance details.
          items:
            $ref: '#/components/schemas/ProjectCompliance'
      description: Compliance details of all the projects of an organization, including the summary of the compliance.
    ProjectCompliance:
      type: object
      properties:
        projectId:
          type: string
          description: UUID of the project.
          example: 123e4567-e89b-12d3-a456-************
        projectName:
          type: string
          description: Name of the project.
          example: Project1
        status:
          $ref: '#/components/schemas/ComplianceStatus'
        ruleViolations:
          $ref: '#/components/schemas/RuleViolationStats'
        policies:
          type: object
          properties:
            count:
              type: integer
              description: Total number of policies.
              example: 2
            list:
              type: array
              description: Policies applied on the project.
              items:
                $ref: '#/components/schemas/PolicyAdherenceInfo'
          description: Policies applied on the project.
      description: Provides compliance details of a project.
    ComponentComplianceDetails:
      type: object
      properties:
        summary:
          type: object
          properties:
            component:
              $ref: '#/components/schemas/ComplianceSummary'
          description: Summary of compliance details of all components of a project.
        count:
          type: integer
          description: Total number of components in the list.
          example: 2
        list:
          type: array
          description: List of component compliance details.
          items:
            $ref: '#/components/schemas/ComponentCompliance'
      description: Compliance details of all the components of a project, including the summary of the compliance.
    ComponentCompliance:
      type: object
      properties:
        componentId:
          type: string
          description: UUID of the component.
          example: 123e4567-e89b-12d3-a456-************
        componentName:
          type: string
          description: Name of the component.
          example: Component1
        status:
          $ref: '#/components/schemas/ComplianceStatus'
        ruleViolations:
          $ref: '#/components/schemas/RuleViolationStats'
        policies:
          type: object
          properties:
            count:
              type: integer
              description: Total number of policies.
              example: 2
            list:
              type: array
              description: Policies applied on the component.
              items:
                $ref: '#/components/schemas/PolicyAdherenceInfo'
          description: Policies applied on the component.
        versions:
          type: object
          properties:
            count:
              type: integer
              description: Total number of versions.
              example: 2
            list:
              type: array
              description: List of component versions.
              items:
                $ref: '#/components/schemas/ComponentVersionCompliance'
          description: Compliance details of all the versions of a component.
      description: Provides compliance details of a component.
    ComponentVersionCompliance:
      type: object
      properties:
        versionId:
          type: string
          description: UUID of the component version.
          example: 123e4567-e89b-12d3-a456-************
        version:
          type: string
          description: Version of the component.
          example: v1.0
        status:
          $ref: '#/components/schemas/ComplianceStatus'
        ruleViolations:
          $ref: '#/components/schemas/RuleViolationStats'
        policies:
          type: object
          properties:
            count:
              type: integer
              description: Total number of policies.
              example: 2
            list:
              type: array
              description: Policies applied on the component.
              items:
                $ref: '#/components/schemas/PolicyAdherenceInfo'
          description: Policies applied on the component.
      description: Provides compliance details of a component version.
    OrganizationPolicyAdherenceDetails:
      type: object
      properties:
        summary:
          type: object
          properties:
            policy:
              $ref: '#/components/schemas/AdherenceSummaryWithUnapplied'
        count:
          type: integer
          description: Total number of policies.
          example: 2
        list:
          type: array
          description: List of policy adherence details.
          items:
            $ref: '#/components/schemas/OrganizationPolicyAdherence'
      description: Adherence details for policies in an organization.
    OrganizationPolicyAdherence:
      type: object
      properties:
        policyId:
          type: string
          description: UUID of the policy.
          example: 123e4567-e89b-12d3-a456-************
        policyName:
          type: string
          description: Name of the policy.
          example: Policy1
        policyType:
          type: string
          description: Type of the policy
          example: ruleset
        status:
          $ref: '#/components/schemas/AdherenceStatus'
        projects:
          type: object
          properties:
            count:
              type: integer
              description: Total number of projects.
              example: 2
            summary:
              $ref: '#/components/schemas/ComplianceSummaryWithNotApplicable'
            list:
              type: array
              description: List of project adherence details.
              items:
                $ref: '#/components/schemas/ProjectComplianceStatus'
          description: Adherence details for projects of the policy.
      description: Provides adherence details of a policy.
    ProjectComplianceStatus:
      type: object
      properties:
        projectId:
          type: string
          description: UUID of the project.
          example: 123e4567-e89b-12d3-a456-************
        projectName:
          type: string
          description: Name of the project.
          example: Project1
        status:
          $ref: '#/components/schemas/ComplianceStatus'
      description: Provides compliance status of a project.
    ProjectPolicyAdherenceDetails:
      type: object
      properties:
        summary:
          type: object
          properties:
            policy:
              $ref: '#/components/schemas/AdherenceSummary'
        count:
          type: integer
          description: Total number of policies.
          example: 2
        list:
          type: array
          description: List of policy adherence details.
          items:
            $ref: '#/components/schemas/ProjectPolicyAdherence'
      description: Adherence details for policies in a project.
    ProjectPolicyAdherence:
      type: object
      properties:
        policyId:
          type: string
          description: UUID of the policy.
          example: 123e4567-e89b-12d3-a456-************
        policyName:
          type: string
          description: Name of the policy.
          example: Policy1
        policyType:
          type: string
          description: Type of the policy.
          example: ruleset
        status:
          $ref: '#/components/schemas/AdherenceStatus'
        components:
          type: object
          properties:
            count:
              type: integer
              description: Total number of components.
              example: 2
            summary:
              $ref: '#/components/schemas/ComplianceSummary'
            list:
              type: array
              description: List of component adherence details.
              items:
                $ref: '#/components/schemas/ComponentComplianceStatus'
          description: Adherence details for components of the policy.
    ComponentComplianceStatus:
      type: object
      properties:
        componentId:
          type: string
          description: UUID of the component.
          example: 123e4567-e89b-12d3-a456-************
        componentName:
          type: string
          description: Name of the component.
          example: Component1
        status:
          $ref: '#/components/schemas/ComplianceStatus'
        versions:
          type: object
          properties:
            count:
              type: integer
              description: Total number of versions.
              example: 2
            list:
              type: array
              items:
                $ref: '#/components/schemas/ComponentVersionComplianceStatus'
      description: Provides compliance status of a component.
    ComponentVersionComplianceStatus:
      type: object
      properties:
        versionId:
          type: string
          description: UUID of the version.
          example: 123e4567-e89b-12d3-a456-************
        version:
          type: string
          description: Version string.
          example: v1.0
        status:
          $ref: '#/components/schemas/ComplianceStatus'
    EndpointPolicyAdherenceDetails:
      type: object
      properties:
        summary:
          type: object
          properties:
            policy:
              $ref: '#/components/schemas/AdherenceSummary'
        count:
          type: integer
          description: Total number of policies.
          example: 2
        list:
          type: array
          description: List of policy adherence details.
          items:
            $ref: '#/components/schemas/EndpointPolicyAdherence'
      description: Adherence details for policies in an endpoint.
    EndpointPolicyAdherence:
      type: object
      properties:
        policyId:
          type: string
          description: UUID of the policy.
          example: 123e4567-e89b-12d3-a456-************
        policyName:
          type: string
          description: Name of the policy.
          example: Policy1
        status:
          $ref: '#/components/schemas/AdherenceStatus'
        rulesets:
          type: object
          properties:
            count:
              type: integer
              description: Total number of rulesets.
              example: 2
            list:
              type: array
              description: List of ruleset adherence details.
              items:
                $ref: '#/components/schemas/EndpointRulesetAdherenceStatus'
          description: Adherence details for rulesets of the policy.
    EndpointRulesetAdherenceStatus:
      type: object
      properties:
        rulesetId:
          type: string
          description: UUID of the ruleset.
          example: 123e4567-e89b-12d3-a456-************
        rulesetName:
          type: string
          description: Name of the ruleset.
          example: Ruleset1
        status:
          $ref: '#/components/schemas/AdherenceStatus'
    EndpointRulesetAdherenceDetails:
      type: object
      properties:
        summary:
          type: object
          properties:
            ruleset:
              $ref: '#/components/schemas/AdherenceSummary'
        count:
          type: integer
          description: Total number of rulesets in the list.
          example: 2
        list:
          type: array
          description: List of ruleset adherence details.
          items:
            $ref: '#/components/schemas/RulesetAdherenceInfo'
      description: A list of ruleset adherence details.
    ViolatedRules:
      type: object
      properties:
        count:
          type: integer
          description: Total number of rules in the list.
          example: 2
        list:
          type: array
          description: List of rule violation details.
          items:
            $ref: '#/components/schemas/ViolatedRule'
      description: Provide details about individual rule violations.
    AdheredRules:
      type: object
      properties:
        count:
          type: integer
          description: Total number of rules in the list.
          example: 2
        list:
          type: array
          description: List of adhered rule details.
          items:
            $ref: '#/components/schemas/AdheredRule'
      description: Provide details about adhered rules.
    EndpointRuleAdherenceDetails:
      type: object
      properties:
        summary:
          type: object
          properties:
            ruleset:
              $ref: '#/components/schemas/AdherenceSummary'
        count:
          type: integer
          description: Total number of rulesets in the list.
          example: 2
        list:
          type: array
          description: List of ruleset adherence details.
          items:
            $ref: '#/components/schemas/RulesetAdherenceDetails'
      description: Adherence details for all rules of an endpoint.
    ViolatedRule:
      type: object
      properties:
        ruleId:
          type: string
          description: UUID of the rule.
          example: 123e4567-e89b-12d3-a456-************
        ruleName:
          type: string
          description: Name of the rule.
          example: Rule1
        severity:
          $ref: '#/components/schemas/Severity'
        message:
          type: string
          description: Error message of the rule.
          example: Servers must use the HTTPS protocol except when using localhost
        paths:
          type: object
          properties:
            count:
              type: integer
              description: Total number of paths.
              example: 2
            list:
              type: array
              description: List of paths.
              items:
                type: string
                description: Path where the rule is violated.
                example: $.servers[0].url
          description: Paths where the rule is violated.
        pathDetails:
          type: object
          properties:
            count:
              type: integer
              description: Total number of path details.
              example: 2
            list:
              type: array
              description: List of path details with violation messages.
              items:
                $ref: '#/components/schemas/PathDetail'
          description: Path details with violation message for each path
      description: Provides details about individual rule violations.
    AdheredRule:
      type: object
      properties:
        ruleId:
          type: string
          description: UUID of the rule.
          example: 123e4567-e89b-12d3-a456-************
        ruleName:
          type: string
          description: Name of the rule.
          example: Rule1
        severity:
          $ref: '#/components/schemas/Severity'
        message:
          type: string
          description: Error message of the rule.
          example: Servers must use the HTTPS protocol except when using localhost
      description: Provides details about individual rule violations.
    PathDetail:
      type: object
      properties:
        path:
          type: string
          description: Path where the rule is violated.
          example: $.components.schemas.Pet.properties.photoUrls
        message:
          type: string
          description: Detailed violation message for the path.
          example: 'Schema validation failed. Added properties: [petid, shipdate]. Removed properties: [petId, shipDate].'
    PolicyAdherenceInfo:
      type: object
      properties:
        policyId:
          type: string
          description: UUID of the policy.
          example: 123e4567-e89b-12d3-a456-************
        policyName:
          type: string
          description: Name of the policy.
          example: Policy1
        policyType:
          type: string
          description: Type of the policy.
          example: ruleset
          default: ruleset
        status:
          $ref: '#/components/schemas/AdherenceStatus'
        rulesets:
          type: object
          properties:
            count:
              type: integer
              description: Total number of rulesets.
              example: 2
            list:
              type: array
              description: List of ruleset adherence details.
              items:
                $ref: '#/components/schemas/RulesetAdherenceInfo'
          description: Rulesets of the policy.
      description: Provides compliance details of a policy.
    RulesetAdherenceInfo:
      type: object
      properties:
        rulesetId:
          type: string
          description: UUID of the ruleset.
          example: 123e4567-e89b-12d3-a456-************
        rulesetName:
          type: string
          description: Name of the ruleset.
          example: API Security Ruleset
        status:
          $ref: '#/components/schemas/AdherenceStatus'
        ruleViolations:
          $ref: '#/components/schemas/RuleViolationStats'
      description: Adherence information for the ruleset.
    RulesetAdherenceDetails:
      type: object
      properties:
        rulesetId:
          type: string
          description: UUID of the ruleset.
          example: 123e4567-e89b-12d3-a456-************
        rulesetName:
          type: string
          description: Name of the ruleset.
          example: API Security Ruleset
        provider:
          type: string
          description: Entity or individual providing the ruleset.
          example: TechWave
        status:
          $ref: '#/components/schemas/AdherenceStatus'
        documentationLink:
          type: string
          description: URL to the documentation related to the ruleset.
          example: https://example.com/docs/api-security-ruleset
        ruleViolations:
          $ref: '#/components/schemas/RuleViolationStats'
        violatedRules:
          $ref: '#/components/schemas/ViolatedRules'
        adheredRules:
          $ref: '#/components/schemas/AdheredRules'
      description: Adherence details for the ruleset.
    ComplianceSummary:
      type: object
      properties:
        total:
          type: integer
          description: Total number of resources.
          example: 10
        compliant:
          type: integer
          description: Number of compliant resources.
          example: 6
        nonCompliant:
          type: integer
          description: Number of non-compliant resources.
          example: 4
      description: Summary of resource compliance.
    ComplianceSummaryWithNotApplicable:
      type: object
      properties:
        total:
          type: integer
          description: Total number of resources.
          example: 10
        compliant:
          type: integer
          description: Number of compliant resources.
          example: 6
        nonCompliant:
          type: integer
          description: Number of non-compliant resources.
          example: 4
        notApplicable:
          type: integer
          description: Number of not applicable resources.
          example: 0
      description: Summary of resource compliance.
    AdherenceSummary:
      type: object
      properties:
        total:
          type: integer
          description: Total number of resources.
          example: 10
        adhered:
          type: integer
          description: Number of adhered resources.
          example: 6
        violated:
          type: integer
          description: Number of violated resources.
          example: 4
      description: Summary of resource adherence.
    AdherenceSummaryWithUnapplied:
      type: object
      properties:
        total:
          type: integer
          description: Total number of resources.
          example: 10
        adhered:
          type: integer
          description: Number of adhered resources.
          example: 6
        violated:
          type: integer
          description: Number of violated resources.
          example: 4
        unapplied:
          type: integer
          description: Number of unapplied resources.
          example: 0
      description: Summary of resource adherence.
    RuleViolationStats:
      type: object
      properties:
        error:
          type: integer
          description: Number of errors.
          example: 10
        warn:
          type: integer
          description: Number of warnings.
          example: 5
        info:
          type: integer
          description: Number of info.
          example: 6
      description: Rule violations for the endpoint.
    ComplianceStatus:
      type: string
      description: Compliance status of the resource.
      example: compliant
      enum:
        - compliant
        - non-compliant
        - not-applicable
    AdherenceStatus:
      type: string
      description: Adherence status of the rule.
      example: adhered
      enum:
        - adhered
        - violated
        - unapplied
    Severity:
      type: string
      description: Severity of the rule.
      example: error
      enum:
        - error
        - warn
        - info
    Error:
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: string
          example: AAA-00000
        message:
          type: string
          example: Some Error Message
        description:
          type: string
          example: Some Error Description
    HealthzResponse:
      type: object
      properties:
        status:
          type: string
          example: Ok
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://choreo-governance-service.prod-choreo-apim.svc.cluster.local:8080/api/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-governance-service.prod-choreo-apim.svc.cluster.local:8080/api/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/governance/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
