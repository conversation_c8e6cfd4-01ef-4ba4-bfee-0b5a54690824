{"data": {"component": {"id": "31806171-9aaf-4a64-a30d-628ddee7fcf8", "name": "platform-services-manager-api", "handler": "hqrukm", "description": " ", "displayType": "proxy", "displayName": "Platform Services Manager API", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2023-10-31T15:03:05.509Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Platform Services Manager API", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/platform-services", "proxyId": "6541172854bcdb21d2a334e4", "id": "6541172854bcdb21d2a334e4", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "6541172854bcdb21d2a334e4", "createdAt": "1698764584558", "updatedAt": "2024-02-29 11:49:32.198", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "31806171-9aaf-4a64-a30d-628ddee7fcf8", "latest": true, "versionStrategy": ""}]}}}