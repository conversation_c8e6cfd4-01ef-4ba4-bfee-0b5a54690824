openapi: 3.0.0
info:
  title: Platform Services Manager API
  version: v1.0
servers:
  - url: https://choreoapis.dev/93tu/platform-services/v1.0
security:
  - default: []
paths:
  /db-servers:
    get:
      tags:
        - db-servers
      summary: Get database server list
      description: Get database server list
      operationId: db-server-list
      parameters:
        - name: organization_id
          in: query
          description: ID of the organization
          required: true
          style: form
          explode: true
          schema:
            $ref: '#/components/schemas/UUID'
        - name: project_id
          in: query
          description: ID of the project
          required: false
          style: form
          explode: true
          schema:
            $ref: '#/components/schemas/UUID'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServerListResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - db-servers
      summary: Create database server
      description: Create database server
      operationId: db-server-create
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServerCreatePayload'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServerCreateResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /db-servers/{db-server-id}:
    get:
      tags:
        - db-servers
      summary: Get database server
      description: Get database server
      operationId: db-server-get-by-id
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DBServiceGetResponseBody'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - db-servers
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServerPutPayload'
      responses:
        "200":
          description: Update database server properties
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServerCreateResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - db-servers
      summary: Delete database server
      description: Delete database server
      operationId: db-server-delete-by-id
      responses:
        "200":
          description: Success
        "409":
          description: Conflict (connected to components)
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/power:
    put:
      tags:
        - db-servers
      summary: Power on/off database server
      description: Power on/off database server
      operationId: db-server-powered-state-update
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DBServerPowerPayload'
      responses:
        "200":
          description: Success
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/maintenance:
    put:
      tags:
        - db-servers
      summary: Update maintenance window for database server
      description: Update maintenance window for database server
      operationId: db-server-maintenance-window-update
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DBServerMaintenanceWindowUpdatePayload'
      responses:
        "200":
          description: Success
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/allowed-ips:
    put:
      tags:
        - db-servers
      summary: Update allowed IP list for database server
      description: Update allowed IP list for database server
      operationId: db-server-ip-filter-update
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IPAllowList'
      responses:
        "200":
          description: Success
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/ca-certificate:
    get:
      tags:
        - db-servers
      summary: Get CA certificate for database server
      description: Get CA certificate for database server
      operationId: db-server-ca-certificate-get
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CACertificate'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/databases:
    get:
      tags:
        - db-servers
      summary: List databases on db-server
      description: List databases on db-server
      operationId: db-server-database-list
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Database'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - db-servers
      summary: Create database on db-server
      description: Create database on db-server
      operationId: db-server-database-create
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DatabasePayload'
            examples:
              Example 1:
                value:
                  name: newdb
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Database'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/databases/{db-name}:
    put:
      tags:
        - db-servers
      summary: Register database to marketplace
      operationId: put-db-servers-db-server-id-databases-db-name
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Database'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Database'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: db-name
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/reset-admin-pwd:
    post:
      summary: Reset admin password for db-server
      description: Reset admin password for db-server
      operationId: db-server-reset-admin-pwd
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordResetResponsePayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-service-plans:
    get:
      summary: Get service plans
      description: get service plans for given type
      operationId: service-plans-list
      parameters:
        - name: type
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
            enum:
              - postgres
              - mysql
              - redis
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServicePlanAndPricing'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /db-servers/{db-server-id}/pg/query-statistics:
    post:
      tags:
        - db-servers
      summary: Get postgres query stats for db-server
      description: Get postgres query stats for db-server
      operationId: db-server-postgres-query-stats
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QueryStatsRequestParams'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PostgresQueryStats'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/mysql/query-statistics:
    post:
      tags:
        - db-servers
      summary: Get mysql query stats for db-server
      description: Get mysql query stats for db-server
      operationId: db-server-mysql-query-stats
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QueryStatsRequestParams'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MySqlQueryStats'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/metrics:
    post:
      summary: Get db-server metrics
      description: Get metrics for db server
      operationId: db-server-metrics
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DBServerMetricsQuery'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DbServerMetrics'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/logs:
    post:
      tags:
        - db-servers
      summary: Get db-server logs
      description: Get logs for db server
      operationId: db-server-logs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DBServerLogsQuery'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DbServerLogs'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/backups:
    get:
      tags:
        - db-servers
      summary: Get db-server backups
      description: Get backups for db server
      operationId: db-server-backups
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DbServerBackups'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/admin-user:
    get:
      tags:
        - db-servers
      summary: Get Admin User for database server
      operationId: get-db-servers-db-server-id-admin-admin-user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DBServerAdminUserWithPwd'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /organization-availability:
    get:
      summary: Get service availability for organization
      operationId: get-organization-availability
      parameters:
        - name: organization_id
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrgServiceAvailabilityResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /db-servers/{db-server-id}/credentials:
    get:
      tags:
        - db-servers
      summary: Get all database credentials related to a db server
      operationId: get-db-servers-db-server-id-credentials
      parameters:
        - name: dbName
          in: query
          description: Database name
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CredentialResponsePayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - db-servers
      summary: Save use provided databases credential
      operationId: post-db-servers-create-credentials
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CredentialPayload'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CredentialResponsePayload'
      security:
        - default: []
      x-internal: false
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /db-servers/{db-server-id}/credentials/{credential-id}:
    get:
      tags:
        - db-servers
      summary: Get credential by Id
      operationId: get-db-servers-db_server_id-credentials-credential_id
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CredentialResponseWithUsername'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - db-servers
      summary: Update a created database credential
      operationId: put-db-servers-db-server-id-credentials-credential_id
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CredentialPayload'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CredentialResponsePayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - db-servers
      summary: Delete a created database credential
      operationId: delete-db-servers-db-server-id-credentials-credential_id
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    parameters:
      - name: db-server-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: credential-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers:
    get:
      tags:
        - brokers
      operationId: get-message-broker-list
      parameters:
        - name: organization_id
          in: query
          description: ID of the organization
          required: true
          style: form
          explode: true
          schema:
            $ref: '#/components/schemas/UUID'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServerListResponse'
              examples:
                messageBrokerListResponse:
                  $ref: '#/components/examples/MessageBrokerListResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - brokers
      operationId: create-message-broker
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServerCreatePayload'
            examples:
              messageBrokerCreatePayload:
                $ref: '#/components/examples/MessageBrokerCreatePayload'
      responses:
        "201":
          description: Created Message Broker Service
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServerCreateResponse'
              examples:
                messageBrokerCreateResponse:
                  $ref: '#/components/examples/MessageBrokerCreateResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /brokers/{broker-id}:
    get:
      tags:
        - brokers
      summary: Get message broker service
      operationId: get-message-broker-by-id
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageBrokerServiceGetResponseBody'
              examples:
                messageBrokerServiceGetResponseBody:
                  $ref: '#/components/examples/MessageBrokerServiceGetResponseBody'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - brokers
      summary: Delete message broker service
      description: Delete message broker service
      operationId: delete-message-broker-by-id
      responses:
        "200":
          description: Success
        "409":
          description: Conflict (connected to components)
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/metrics:
    post:
      tags:
        - brokers
      summary: Get broker-server metrics
      description: Get metrics for broker server
      operationId: get-message-broker-metrics
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DBServerMetricsQuery'
            examples:
              messageBrokerMetricsGetPayload:
                $ref: '#/components/examples/MessageBrokerMetricsGetPayload'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DbServerMetrics'
              examples:
                messageBrokerMetricsGetResponse:
                  $ref: '#/components/examples/MessageBrokerMetricsGetResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/logs:
    post:
      tags:
        - brokers
      summary: Get broker-server logs
      description: Get logs for broker server
      operationId: get-message-broker-logs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DBServerLogsQuery'
            examples:
              messageBrokerLogsGetPayload:
                $ref: '#/components/examples/MessageBrokerLogsGetPayload'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DbServerLogs'
              examples:
                messageBrokerLogsGetResponse:
                  $ref: '#/components/examples/MessageBrokerLogsGetResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/maintenance:
    put:
      tags:
        - brokers
      summary: Update maintenance window for broker server
      description: Update maintenance window for broker server
      operationId: update-message-broker-maintenance-window
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DBServerMaintenanceWindowUpdatePayload'
            examples:
              maintenanceWindowUpdatePayload:
                $ref: '#/components/examples/MaintenanceWindowUpdatePayload'
      responses:
        "200":
          description: Success
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{brokers-id}/allowed-ips:
    put:
      tags:
        - brokers
      summary: Update allowed IP list for broker server
      description: Update allowed IP list for broker server
      operationId: update-message-broker-allowed-ips
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IPAllowList'
            examples:
              allowedIpListUpdatePayload:
                $ref: '#/components/examples/AllowedIpListUpdatePayload'
      responses:
        "200":
          description: Success
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: brokers-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/kafka/users:
    get:
      tags:
        - brokers
      summary: Get users for message broker
      description: Get users for message broker
      operationId: get-message-broker-users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MessageBrokerUser'
              examples:
                messageBrokerUsersListResponse:
                  $ref: '#/components/examples/MessageBrokerUsersListResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - brokers
      summary: Create user for message broker
      description: Create user for message broker
      operationId: create-message-broker-user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MessageBrokerUserCreatePayload'
            examples:
              messageBrokerUserCreatePayload:
                $ref: '#/components/examples/MessageBrokerUserCreatePayload'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageBrokerUser'
              examples:
                messageBrokerUserCreateResponse:
                  $ref: '#/components/examples/MessageBrokerUserCreateResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/kafka/users/{user-name}:
    delete:
      tags:
        - brokers
      summary: Delete user for message broker
      description: Delete user for message broker
      operationId: delete-message-broker-user-by-name
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: user-name
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/kafka/users/{user-name}/reset-credentials:
    post:
      tags:
        - brokers
      summary: Reset user credentials for message broker
      description: Reset user credentials for message broker
      operationId: reset-message-broker-credentials-for-user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageBrokerUser'
              examples:
                messageBrokerUserResetCredentialsResponse:
                  $ref: '#/components/examples/MessageBrokerUserResetCredentialsResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: user-name
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/ca-certificate:
    get:
      tags:
        - brokers
      summary: Get CA certificate for message broker server
      description: Get CA certificate for message broker server
      operationId: get-message-broker-ca-certificate
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CACertificate'
              examples:
                messageBrokerCaCertificateGetResponse:
                  $ref: '#/components/examples/MessageBrokerCaCertificateGetResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/kafka/acls:
    get:
      tags:
        - brokers
      summary: Get Kafka ACL list for message broker
      description: Get Kafka ACL list for message broker
      operationId: list-message-broker-kafka-acls
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KafkaACLs'
              examples:
                messageBrokerACLListGetResponse:
                  $ref: '#/components/examples/MessageBrokerACLListGetResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - brokers
      summary: Create Kafka ACL for message broker
      description: Create Kafka ACL for message broker
      operationId: create-message-broker-kafka-acl
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KafkaACLCreatePayload'
            examples:
              messageBrokerACLCreatePayload:
                $ref: '#/components/examples/MessageBrokerACLCreatePayload'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KafkaACL'
              examples:
                messageBrokerACLCreateResponse:
                  $ref: '#/components/examples/MessageBrokerACLCreateResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/kafka/acls/{acl-id}:
    delete:
      tags:
        - brokers
      summary: Delete Kafka ACL for message broker
      description: Delete Kafka ACL for message broker
      operationId: delete-message-broker-kafka-acl-by-id
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: acl-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/kafka/topics:
    get:
      tags:
        - brokers
      summary: List Kafka topics for message broker
      description: List Kafka topics for message broker
      operationId: list-kafka-topics-on-message-broker
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KafkaTopicsListResponse'
              examples:
                kafkaTopicsListResponse:
                  $ref: '#/components/examples/KafkaTopicsListResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - brokers
      summary: Create Kafka topic for message broker
      description: Create Kafka topic for message broker
      operationId: create-message-broker-kafka-topic
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KafkaTopicCreatePayload'
            examples:
              kafkaTopicCreatePayload:
                $ref: '#/components/examples/KafkaTopicCreatePayload'
      responses:
        "201":
          description: Created
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/{broker-id}/kafka/topics/{topic-name}:
    put:
      tags:
        - brokers
      summary: Update Kafka topic for message broker
      description: Update Kafka topic for message broker
      operationId: update-message-broker-kafka-topic-by-name
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KafkaTopicUpdatePayload'
            examples:
              kafkaTopicUpdatePayload:
                $ref: '#/components/examples/KafkaTopicUpdatePayload'
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - brokers
      summary: Delete Kafka topic for message broker
      description: Delete Kafka topic for message broker
      operationId: delete-message-broker-kafka-topic-by-name
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: topic-name
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
  /brokers/kafka-user-configs:
    get:
      tags:
        - brokers
      summary: Get Kafka user configs for message broker
      description: Get Kafka user configs for message broker
      operationId: message-broker-kafka-user-config-schema
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/KafkaUserConfigs'
              examples:
                kafkaUserConfigGetResponse:
                  $ref: '#/components/examples/KafkaUserConfigGetResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /brokers/{broker-id}/power:
    put:
      tags:
        - brokers
      summary: Power on/off message broker server
      description: Power on/off message broker server
      operationId: update-message-broker-server-powered-state
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DBServerPowerPayload'
      responses:
        "200":
          description: Success
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    parameters:
      - name: broker-id
        in: path
        required: true
        style: simple
        explode: false
        schema:
          type: string
components:
  schemas:
    MessageBrokerUserCreatePayload:
      title: MessageBrokerUserCreatePayload
      required:
        - username
      type: object
      properties:
        username:
          type: string
    MessageBrokerUser:
      title: MessageBrokerUser
      required:
        - access_cert
        - access_key
        - type
        - username
      type: object
      properties:
        access_cert:
          type: string
        access_cert_expiry:
          type: string
          format: date-time
        access_key:
          type: string
        type:
          type: string
        username:
          type: string
    KafkaACLs:
      title: KafkaACLs
      required:
        - acls
      type: object
      properties:
        acls:
          type: array
          items:
            $ref: '#/components/schemas/KafkaACL'
    KafkaACLCreatePayload:
      title: KafkaACLCreatePayload
      required:
        - permission
        - topic
        - username
      type: object
      properties:
        permission:
          type: string
        topic:
          type: string
        username:
          type: string
    KafkaACL:
      title: KafkaACL
      required:
        - id
        - permission
        - topic
        - username
      type: object
      properties:
        id:
          type: string
        permission:
          type: string
        topic:
          type: string
        username:
          type: string
    KafkaTopicCreatePayload:
      title: KafkaTopicCreatePayload
      required:
        - topic_name
      type: object
      properties:
        topic_name:
          type: string
        replication:
          type: integer
        partitions:
          type: integer
        retention_hours:
          type: integer
          format: int64
        retention_bytes:
          type: integer
        minimum_in_sync_replicas:
          type: integer
        cleanup_policy:
          $ref: '#/components/schemas/CleanupPolicy'
    KafkaTopicsListResponse:
      type: array
      items:
        $ref: '#/components/schemas/KafkaTopicListItem'
    KafkaTopicListItem:
      title: KafkaTopicListItem
      required:
        - cleanup_policy
        - minimum_in_sync_replicas
        - partitions
        - replication
        - retention_bytes
        - state
        - topic_name
      type: object
      properties:
        cleanup_policy:
          $ref: '#/components/schemas/CleanupPolicy'
        minimum_in_sync_replicas:
          type: integer
        partitions:
          type: integer
        replication:
          type: integer
        retention_bytes:
          type: integer
        retention_hours:
          type: integer
          format: int64
        topic_name:
          type: string
        state:
          type: string
    KafkaTopicUpdatePayload:
      title: KafkaTopicUpdatePayload
      type: object
      properties:
        replication:
          type: integer
        partitions:
          type: integer
        retention_hours:
          type: integer
          format: int64
        retention_bytes:
          type: integer
        minimum_in_sync_replicas:
          type: integer
        cleanup_policy:
          $ref: '#/components/schemas/CleanupPolicy'
    CleanupPolicy:
      title: CleanupPolicy
      type: string
      enum:
        - delete
        - compact
        - compact,delete
    KafkaUserConfigs:
      title: KafkaUserConfigs
      required:
        - minimum_in_sync_replicas
        - partitions
        - replication
        - retention_bytes
        - retention_ms
      type: object
      properties:
        replication:
          $ref: '#/components/schemas/UserConfigSchema'
        partitions:
          $ref: '#/components/schemas/UserConfigSchema'
        retention_bytes:
          $ref: '#/components/schemas/UserConfigSchema'
        retention_ms:
          $ref: '#/components/schemas/UserConfigSchema'
        minimum_in_sync_replicas:
          $ref: '#/components/schemas/UserConfigSchema'
    UserConfigSchema:
      title: UserConfigSchema
      required:
        - description
        - maximum
        - minimum
      type: object
      properties:
        minimum:
          type: number
          format: double
        maximum:
          type: number
          format: double
        description:
          type: string
    MessageBrokerServiceGetResponseBody:
      title: DBServiceGetResponseBody
      allOf:
        - $ref: '#/components/schemas/Server'
        - $ref: '#/components/schemas/DBServerState'
        - $ref: '#/components/schemas/DBServerNodes'
        - type: object
          properties:
            connection_params:
              $ref: '#/components/schemas/MessageBrokerConnectionInfo'
        - type: object
          properties:
            service_plan:
              $ref: '#/components/schemas/ServerServicePanInfo'
    MessageBrokerConnectionInfo:
      title: MessageBrokerConnectionInfo
      required:
        - access_cert
        - access_key
        - host
        - port
        - service_uri
      type: object
      properties:
        access_key:
          type: string
        access_cert:
          type: string
        service_uri:
          type: string
        host:
          type: string
        port:
          type: string
    CommonCredentialFields:
      title: CommonCredentialFields
      required:
        - applicable_environments
        - database
        - display_name
      type: object
      properties:
        database:
          type: string
        display_name:
          type: string
        applicable_environments:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
    SuperAdminCredentials:
      required:
        - is_super_admin
      type: object
      properties:
        is_super_admin:
          type: boolean
    UserCredentialsWithPrivileges:
      required:
        - password
        - privilege_levels
        - username
      type: object
      properties:
        username:
          type: string
        password:
          type: string
        privilege_levels:
          type: array
          items:
            type: string
    CredentialResponsePayload:
      title: CredentialResponse
      required:
        - applicable_environments
        - created_at
        - database_name
        - display_name
        - id
        - is_super_admin
        - privilege_levels
        - updated_at
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        display_name:
          type: string
        database_name:
          type: string
        privilege_levels:
          type: array
          items:
            type: string
        applicable_environments:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
        is_super_admin:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    CredentialResponseWithUsername:
      title: CredentialResponseWithUsername
      type: object
      allOf:
        - $ref: '#/components/schemas/CredentialResponsePayload'
        - required:
            - username
          type: object
          properties:
            username:
              type: string
    Server:
      required:
        - cloud_provider
        - cloud_region
        - created_at
        - display_on_marketplace
        - id
        - name
        - service_plan_id
        - status
        - type
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        created_at:
          type: string
          format: date-time
        project_id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          description: Service display name
        service_plan_id:
          $ref: '#/components/schemas/UUID'
        cloud_provider:
          $ref: '#/components/schemas/CloudProvider'
        cloud_region:
          $ref: '#/components/schemas/ChoreoCloudRegion'
        status:
          $ref: '#/components/schemas/ServerStatus'
        type:
          $ref: '#/components/schemas/ServiceType'
        display_on_marketplace:
          type: boolean
    ServerCreatePayload:
      title: ServerCreatePayload
      required:
        - cloud_provider
        - cloud_region
        - name
        - service_plan_id
      type: object
      properties:
        project_id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          x-go-custom-tag: validate:"required"
        service_plan_id:
          $ref: '#/components/schemas/UUID'
        cloud_provider:
          $ref: '#/components/schemas/CloudProvider'
        cloud_region:
          $ref: '#/components/schemas/ChoreoCloudRegion'
    ServerPutPayload:
      title: ServerPutPayload
      type: object
      allOf:
        - $ref: '#/components/schemas/ServerCreatePayload'
        - required:
            - display_on_marketplace
          type: object
          properties:
            display_on_marketplace:
              type: boolean
    Database:
      required:
        - display_on_marketplace
        - name
      type: object
      properties:
        name:
          type: string
        display_on_marketplace:
          type: boolean
    DatabasePayload:
      required:
        - name
      type: object
      properties:
        name:
          type: string
    ServicePlanAndPricing:
      title: ServicePlanAndPricing
      allOf:
        - required:
            - description
            - free_trial_available
            - id
            - name
            - regions
            - type
          type: object
          properties:
            id:
              $ref: '#/components/schemas/UUID'
            name:
              type: string
            description:
              type: string
            regions:
              type: array
              items:
                $ref: '#/components/schemas/ServicePlanRegionalAvailability'
            type:
              $ref: '#/components/schemas/ServiceType'
            free_trial_available:
              type: boolean
        - $ref: '#/components/schemas/ServicePlanSpec'
    ServicePlanSpec:
      title: ServicePlanSpec
      required:
        - backup_interval_hours
        - backup_retention_days
        - node_count
      type: object
      properties:
        node_count:
          type: integer
        backup_retention_days:
          type: integer
        backup_interval_hours:
          type: integer
    ServicePlanNodeSpec:
      title: ServicePlanNodeSpecification
      required:
        - node_cpu_count
        - node_ram_gb
        - storage_gb
      type: object
      properties:
        node_cpu_count:
          type: integer
        node_ram_gb:
          type: integer
        storage_gb:
          type: integer
    ServicePlanRegionalAvailability:
      title: ServicePlanRegionalAvailability
      allOf:
        - required:
            - cloud_provider
            - cloud_region
          type: object
          properties:
            cloud_provider:
              $ref: '#/components/schemas/CloudProvider'
            cloud_region:
              $ref: '#/components/schemas/ChoreoCloudRegion'
        - $ref: '#/components/schemas/ServicePlanNodeSpec'
        - $ref: '#/components/schemas/ServicePlanPricing'
    ServiceType:
      title: ServiceType
      type: string
      enum:
        - postgres
        - mysql
        - redis
        - kafka
    PeriodType:
      title: PeriodType
      type: string
      enum:
        - hour
        - day
        - week
        - month
        - year
    SortingOrderTypes:
      title: SortingOrderTypes
      type: string
      enum:
        - asc
        - desc
    CloudProvider:
      title: CloudProvider
      type: string
      enum:
        - aws
        - gcp
        - azure
        - digitalocean
    UUID:
      title: UUID
      type: string
      format: uuid
      x-go-custom-tag: validate:"required"
    MetricDateTime:
      title: MetricDateTime
      type: string
      format: date-time
    ChoreoCloudRegion:
      title: ChoreoCloudRegion
      type: string
      enum:
        - us
        - eu
        - africa
        - aus
    ServerStatus:
      title: DBServerState
      type: string
      enum:
        - CREATING
        - ACTIVE
        - POWERED_OFF
        - RESUMING
        - DELETING
        - DELETED
        - ERROR
    PostgresQueryStats:
      type: object
      properties:
        queries:
          type: array
          items:
            $ref: '#/components/schemas/PostgresQueryStatsItem'
    QueryStatsRequestParams:
      required:
        - limit
        - offset
        - sort_field
        - sort_order
      type: object
      properties:
        offset:
          type: integer
        limit:
          type: integer
        sort_order:
          $ref: '#/components/schemas/SortingOrderTypes'
        sort_field:
          type: string
    PostgresQueryStatsItem:
      required:
        - calls
        - max_time
        - mean_time
        - min_time
        - query
        - rows
        - stddev_time
        - total_time
      type: object
      properties:
        query:
          type: string
        rows:
          type: integer
        calls:
          type: integer
        min_time:
          type: number
        max_time:
          type: number
        mean_time:
          type: number
        total_time:
          type: number
        stddev_time:
          type: number
    MySqlQueryStatsItem:
      required:
        - calls
        - max_time
        - mean_time
        - min_time
        - query
        - rows
        - total_time
      type: object
      properties:
        query:
          type: string
        rows:
          type: integer
        calls:
          type: integer
        min_time:
          type: integer
          format: int64
        max_time:
          type: integer
          format: int64
        mean_time:
          type: number
        total_time:
          type: integer
          format: int64
    MySqlQueryStats:
      type: object
      properties:
        queries:
          type: array
          items:
            $ref: '#/components/schemas/MySqlQueryStatsItem'
    PasswordResetResponsePayload:
      title: PasswordResetResponsePayload
      required:
        - name
        - password
      type: object
      properties:
        password:
          type: string
        name:
          type: string
    ServerCreateResponse:
      title: ServerCreateResponse
      allOf:
        - $ref: '#/components/schemas/Server'
    DBServiceGetResponseBody:
      title: DBServiceGetResponseBody
      allOf:
        - $ref: '#/components/schemas/Server'
        - $ref: '#/components/schemas/DBServerState'
        - $ref: '#/components/schemas/DBServerNodes'
        - required:
            - display_on_marketplace
          type: object
          properties:
            service_plan:
              $ref: '#/components/schemas/ServerServicePanInfo'
            display_on_marketplace:
              type: boolean
    DBServerAdminUserWithPwd:
      title: DBServerAdminUserWithPwd
      required:
        - name
        - password
      type: object
      properties:
        name:
          type: string
        password:
          type: string
          nullable: true
    CIDRBlockWithDescription:
      title: CIDRBlockWithDescription
      required:
        - description
        - network
      type: object
      properties:
        network:
          type: string
          format: cidr
        description:
          type: string
    DBServerMetricsRow:
      required:
        - date
        - values
      type: object
      properties:
        date:
          $ref: '#/components/schemas/MetricDateTime'
        values:
          type: array
          items:
            type: number
            format: double
            nullable: true
    DBServerMetricsCol:
      required:
        - label
        - type
      type: object
      properties:
        label:
          type: string
        type:
          type: string
    DbServerMetrics:
      required:
        - metrics
      type: object
      properties:
        metrics:
          type: object
          properties:
            cpu_usage:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
            disk_usage:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
            diskio_read:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
            diskio_writes:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
            load_average:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
            mem_available:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
            mem_usage:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
            net_receive:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
            net_send:
              $ref: '#/components/schemas/DBServerMetricsDataItem'
    DBServerLogsQuery:
      required:
        - limit
        - offset
        - sort_order
      type: object
      properties:
        sort_order:
          $ref: '#/components/schemas/SortingOrderTypes'
        limit:
          type: integer
        offset:
          type: string
    DbServerLogs:
      required:
        - first_log_offset
        - logs
        - offset
      type: object
      properties:
        offset:
          type: string
        first_log_offset:
          type: string
        logs:
          type: array
          items:
            required:
              - hostname
              - msg
              - time
              - unit
            type: object
            properties:
              hostname:
                type: string
              msg:
                type: string
              time:
                type: string
              unit:
                type: string
    DbServerBackups:
      required:
        - backups
      type: object
      properties:
        backups:
          type: array
          items:
            required:
              - backup_name
              - backup_time
              - data_size
            type: object
            properties:
              backup_name:
                type: string
              backup_time:
                type: string
              data_size:
                type: integer
    DBServerMetricsQuery:
      required:
        - period
      type: object
      properties:
        period:
          $ref: '#/components/schemas/PeriodType'
    DBServerMetricsDataItem:
      required:
        - data
      type: object
      properties:
        data:
          required:
            - cols
            - rows
          type: object
          properties:
            cols:
              type: array
              items:
                $ref: '#/components/schemas/DBServerMetricsCol'
            rows:
              type: array
              items:
                $ref: '#/components/schemas/DBServerMetricsRow'
    IPAllowList:
      title: IPAllowList
      required:
        - mode
      type: object
      properties:
        allow_list:
          type: array
          items:
            $ref: '#/components/schemas/CIDRBlockWithDescription'
        mode:
          $ref: '#/components/schemas/IPAllowListMode'
    IPAllowListMode:
      title: IPAllowListMode
      type: string
      enum:
        - allow_all
        - restricted
    DBServerNodes:
      title: DBServerNodes
      required:
        - nodes
      type: object
      properties:
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/DBServerNode'
    DBServerState:
      title: DBServerProviderState
      required:
        - allowed_ips
        - connection_params
        - maintenance
        - service_version
      type: object
      properties:
        service_version:
          type: string
        connection_params:
          required:
            - database
            - host
            - password_reset
            - port
            - ssl_required
            - user
          type: object
          properties:
            host:
              type: string
            port:
              type: string
            user:
              type: string
            database:
              type: string
            ssl_required:
              type: boolean
            password_reset:
              type: boolean
        allowed_ips:
          $ref: '#/components/schemas/IPAllowList'
        maintenance:
          $ref: '#/components/schemas/DBServerMaintenanceWindow'
    ServicePlanPricing:
      title: ServicePlanPricing
      required:
        - hourly_price_usd
        - monthly_price_usd
      type: object
      properties:
        monthly_price_usd:
          type: string
        hourly_price_usd:
          type: string
    ServerServicePanInfo:
      title: ServerServicePanInfo
      allOf:
        - required:
            - name
          type: object
          properties:
            name:
              type: string
        - $ref: '#/components/schemas/ServicePlanNodeSpec'
        - $ref: '#/components/schemas/ServicePlanPricing'
        - $ref: '#/components/schemas/ServicePlanSpec'
    ServerListResponseBodyItem:
      title: ServerListResponseBodyItem
      allOf:
        - $ref: '#/components/schemas/Server'
        - required:
            - service_plan
          type: object
          properties:
            service_plan:
              $ref: '#/components/schemas/ServerServicePanInfo'
    ServerListResponse:
      title: ServerListResponse
      type: array
      items:
        $ref: '#/components/schemas/ServerListResponseBodyItem'
    DBServerPowerPayload:
      title: DBServerPowerPayload
      required:
        - action
      type: object
      properties:
        action:
          $ref: '#/components/schemas/DBServerPowerAction'
    DBServerPowerAction:
      title: DBServerPowerAction
      type: string
      enum:
        - power_on
        - power_off
    DBServerMaintenanceWindowUpdatePayload:
      $ref: '#/components/schemas/DBServerMaintenanceWindow'
    DBServerMaintenanceWindow:
      title: DBServerMaintenanceWindow
      required:
        - day
        - time
      type: object
      properties:
        day:
          $ref: '#/components/schemas/DayOfWeek'
        time:
          $ref: '#/components/schemas/TimeOfDay'
    DayOfWeek:
      title: Day of week
      type: string
      enum:
        - monday
        - tuesday
        - wednesday
        - thursday
        - friday
        - saturday
        - sunday
    TimeOfDay:
      pattern: ^(?:[0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$
      type: string
      format: time
    CACertificate:
      title: CACertificate
      required:
        - certificate
      type: object
      properties:
        certificate:
          type: string
    DBServerNode:
      title: DBServerNode
      required:
        - name
        - role
        - state
      type: object
      properties:
        name:
          type: string
        role:
          type: string
        state:
          type: string
    OrgServiceAvailabilityResponse:
      title: OrgServiceAvailabilityResponse
      required:
        - is_available
        - reason
        - service_count_limit
      type: object
      properties:
        is_available:
          type: boolean
        service_count_limit:
          type: integer
        reason:
          $ref: '#/components/schemas/OrgServiceAvailabilityReason'
    OrgServiceAvailabilityReason:
      type: string
      enum:
        - AVAILABLE
        - FREE_SUB_MAX_COUNT_EXCEEDED
        - PAID_SUB_MAX_COUNT_EXCEEDED
        - UNKNOWN
        - ORGANIZATION_NOT_IN_ALLOW_LIST
    CredentialPayload:
      title: CredentialPayload
      allOf:
        - $ref: '#/components/schemas/CommonCredentialFields'
        - oneOf:
            - $ref: '#/components/schemas/SuperAdminCredentials'
            - $ref: '#/components/schemas/UserCredentialsWithPrivileges'
  examples:
    MessageBrokerListResponse:
      description: List of message brokers
      value:
        - id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
          created_at: "2019-08-24T14:15:22Z"
          project_id: 405d8375-3514-403b-8c43-83ae74cfe0e9
          name: test-broker
          service_plan_id: 051cebbc-732d-42d0-afba-a4a8345fbdf5
          cloud_provider: aws
          cloud_region: us
          status: CREATING
          type: postgres
          display_on_marketplace: true
          service_plan:
            name: business-1
            node_cpu_count: 0
            node_ram_gb: 0
            storage_gb: 0
            monthly_price_usd: "200"
            hourly_price_usd: "0.27"
            node_count: 0
            backup_retention_days: 0
            backup_interval_hours: 0
    MessageBrokerCreatePayload:
      description: Create message broker payload
      value:
        project_id: 405d8375-3514-403b-8c43-83ae74cfe0e9
        name: test-broker
        service_plan_id: 051cebbc-732d-42d0-afba-a4a8345fbdf5
        cloud_provider: aws
        cloud_region: us
    MessageBrokerCreateResponse:
      description: Message broker create response
      value:
        id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
        created_at: "2019-08-24T14:15:22Z"
        project_id: 405d8375-3514-403b-8c43-83ae74cfe0e9
        name: test-broker
        service_plan_id: 051cebbc-732d-42d0-afba-a4a8345fbdf5
        cloud_provider: aws
        cloud_region: us
        status: CREATING
        type: postgres
        is_vector_enabled: true
        display_on_marketplace: true
    MessageBrokerServiceGetResponseBody:
      description: Message broker service get response
      value:
        id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
        created_at: "2019-08-24T14:15:22Z"
        project_id: 405d8375-3514-403b-8c43-83ae74cfe0e9
        name: test-broker
        service_plan_id: 051cebbc-732d-42d0-afba-a4a8345fbdf5
        cloud_provider: aws
        cloud_region: us
        status: CREATING
        type: postgres
        is_vector_enabled: true
        service_version: "3.0"
        connection_params:
          host: kafka-4757e592-b961-4e7f-bd95-32ceaea49ccb-ghg4214291680-choreo.l.aivencloud.com
          port: "8080"
          user: avnadmin
          database: defaultdb
          ssl_required: true
          password_reset: true
          access_key: test-access-key
          access_cert: test-access-cert
          service_uri: https://kafka-4757e592-b961-4e7f-bd95-32ceaea49ccb-ghg4214291680-choreo.l.aivencloud.com:8080
        allowed_ips:
          allow_list:
            - network: "0.27"
              description: sample description
          mode: allow_all
        maintenance:
          day: monday
          time: 14:15:22
        nodes:
          - name: test-node
            role: master
            state: running
        service_plan:
          name: business-1
          node_cpu_count: 0
          node_ram_gb: 0
          storage_gb: 0
          monthly_price_usd: "200"
          hourly_price_usd: "0.27"
          node_count: 0
          backup_retention_days: 0
          backup_interval_hours: 0
    MessageBrokerMetricsGetPayload:
      description: Message broker metrics get payload
      value:
        period: hour
    MessageBrokerMetricsGetResponse:
      description: Message broker metrics get response
      value:
        metrics:
          cpu_usage:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
          disk_usage:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
          diskio_read:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
          diskio_writes:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
          load_average:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
          mem_available:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
          mem_usage:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
          net_receive:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
          net_send:
            data:
              cols:
                - label: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
                  type: number
              rows:
                - date: "2019-08-24T14:15:22Z"
                  values:
                    - 0
    MessageBrokerLogsGetPayload:
      description: Message broker logs get payload
      value:
        sort_order: asc
        limit: 0
        offset: "1729503154140000000"
    MessageBrokerLogsGetResponse:
      description: Message broker logs get response
      value:
        offset: "1729503154140000000"
        first_log_offset: "1729503154140000000"
        logs:
          - hostname: kafka-4757e592-b961-4e7f-bd95-32ceaea49c
            msg: sample message
            time: "2019-08-24T14:15:22Z"
            unit: kafka.service
    MaintenanceWindowUpdatePayload:
      description: Maintenance window update payload
      value:
        day: monday
        time: 14:15:22
    AllowedIpListUpdatePayload:
      description: Allowed IP list update payload
      value:
        allow_list:
          - network: "0.27"
            description: sample description
        mode: allow_all
    MessageBrokerUsersListResponse:
      description: Message broker users list response
      value:
        - access_cert: test-access-cert
          access_cert_expiry: "2019-08-24T14:15:22Z"
          access_key: test-access-key
          type: admin
          username: avnadmin
    MessageBrokerUserCreatePayload:
      description: Message broker user create payload
      value:
        username: avnadmin
    MessageBrokerUserCreateResponse:
      description: Message broker user create response
      value:
        access_cert: test-access-cert
        access_cert_expiry: "2019-08-24T14:15:22Z"
        access_key: test-access-key
        type: admin
        username: avnadmin
    MessageBrokerUserResetCredentialsResponse:
      description: Message broker user reset credentials response
      value:
        access_cert: test-access-cert
        access_cert_expiry: "2019-08-24T14:15:22Z"
        access_key: test-access-key
        type: admin
        username: avnadmin
    MessageBrokerCaCertificateGetResponse:
      description: Message broker CA certificate get response
      value:
        certificate: test-certificate
    MessageBrokerACLListGetResponse:
      description: Message broker ACL list get response
      value:
        acls:
          - id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
            permission: read
            topic: test-topic
            username: avnadmin
    MessageBrokerACLCreatePayload:
      description: Message broker ACL create payload
      value:
        permission: read
        topic: test-topic
        username: avnadmin
    MessageBrokerACLCreateResponse:
      description: Message broker ACL create response
      value:
        id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
        permission: read
        topic: test-topic
        username: avnadmin
    KafkaTopicCreatePayload:
      description: Kafka topic create payload
      value:
        topic_name: test-topic
        replication: 0
        partitions: 0
        retention_hours: 0
        retention_bytes: 0
        minimum_in_sync_replicas: 0
        cleanup_policy: delete
    KafkaTopicsListResponse:
      description: Kafka topics list response
      value:
        - cleanup_policy: delete
          minimum_in_sync_replicas: 0
          partitions: 0
          replication: 0
          retention_bytes: 0
          retention_hours: 0
          topic_name: test-topic
          state: running
    KafkaTopicUpdatePayload:
      description: Kafka topic update payload
      value:
        replication: 0
        partitions: 0
        retention_hours: 0
        retention_bytes: 0
        minimum_in_sync_replicas: 0
        cleanup_policy: delete
    KafkaUserConfigGetResponse:
      description: Kafka user config get response
      value:
        - replication:
            minimum: 0
            maximum: 0
            description: replication limits
          partitions:
            minimum: 0
            maximum: 0
            description: partition limits
          retention_bytes:
            minimum: 0
            maximum: 0
            description: retention bytes limits
          retention_ms:
            minimum: 0
            maximum: 0
            description: retention ms limits
          minimum_in_sync_replicas:
            minimum: 0
            maximum: 0
            description: minimum in sync replicas limits
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins: []
  accessControlAllowCredentials: false
  accessControlAllowHeaders: []
  accessControlAllowMethods: []
x-wso2-production-endpoints:
  urls:
    - http://platform-services-manager.prod-choreo-system.svc.cluster.local:80
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://platform-services-manager.prod-choreo-system.svc.cluster.local:80
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/platform-services/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
