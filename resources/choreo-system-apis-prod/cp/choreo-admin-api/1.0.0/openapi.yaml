openapi: 3.0.1
info:
  title: Choreo Admin API
  description: |
    This is a RESTFul API to perform Administrative operations in Choreo
  contact: {}
  version: 1.0.0
servers:
  - url: https://app.choreo.dev/93tu/choreo_admin_api/1.0.0
    variables:
      basePath:
        default: org
security:
  - default: []
tags:
  - name: subscription
    description: Subscription related resources
  - name: org-management
    description: Organization management related resources
  - name: reports
    description: Reports related to the Choreo platform
  - name: actions
    description: Actions that can be performed to the Choreo platform
paths:
  /subscription/{orgId}:
    get:
      tags:
        - subscription
      description: Retrieve subscription details for a given organization
      operationId: getSubscription
      parameters:
        - $ref: '#/components/parameters/orgId'
      responses:
        "200":
          description: |
            OK.
            Retrieve subscription details for a given organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subscription'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - subscription
      description: update an orgnization subscription
      operationId: updateSubscription
      parameters:
        - $ref: '#/components/parameters/orgId'
      requestBody:
        description: Subscription object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionUpdate'
        required: true
      responses:
        "200":
          description: |
            Updated.
            Successful response with the updated object as entity in the body.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subscription'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /subscription/{orgId}/v2:
    get:
      tags:
        - subscription
      description: Retrieve subscriptionV2 details for a given organization
      operationId: getSubscriptionV2
      parameters:
        - $ref: '#/components/parameters/orgId'
      responses:
        "200":
          description: |
            OK.
            Retrieve subscription details for a given organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionV2'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - subscription
      description: update a component based orgnization subscription
      operationId: updateSubscriptionV2
      parameters:
        - $ref: '#/components/parameters/orgId'
      requestBody:
        description: Subscription object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TierDetail'
        required: true
      responses:
        "200":
          description: |
            Updated.
            Successful response with the updated object as entity in the body.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionV2'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /subscription/gcp/{orgUuid}:
    get:
      tags:
        - subscription
      description: Retrieve GCP subscription details for a given organization
      operationId: getGcpSubscription
      parameters:
        - $ref: '#/components/parameters/orgUuid'
      responses:
        "200":
          description: |
            OK.
            Retrieve GCP subscription details for a given organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GcpSubscription'
              examples:
                success:
                  value:
                    id: 6fa459ea-ee8a-3ca4-894e-db77e160355e
                    gcpMarketplaceCustomerId: 90d41507-8905-4f2b-becc-8484360b6f6r
                    gcpMarketplaceCustomerGoogleAccountId: s93e4de4-9fec-4090-abb5-d82b0a87fr3w
                    gcpmarketplaceEntitlementId: 634e4de4-9fec-4090-abb5-d82b0a87fb9s
                    productCode: choreo-test
                    contractType: enterprise
                    subscriptionStatus: active
                    orgUuid: 9f0e1d2c-3b4a-5e6d-7f8g-9h0i1j2k3l4
                    userIdpId: 01ed65dc-e164-122c-9c05-dda14f6893ed
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /subscription/gcp/entitlement/{entitlementId}:
    get:
      tags:
        - subscription
      description: Retrieve GCP entitlement details for a given entitlement identifier
      operationId: getGcpEntitlement
      parameters:
        - $ref: '#/components/parameters/entitlementId'
      responses:
        "200":
          description: |
            OK.
            Retrieve GCP entitlement details for a given entitlement identifier
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GcpEntitlement'
              examples:
                success:
                  value:
                    account: providers/choreo-test/accounts/68d41507-8905-4f2b-becc-8484360b6f5c
                    createTime: "2024-11-05T06:56:44.170427Z"
                    name: providers/choreo-test/entitlements/377e4de4-9fec-4090-abb5-d82b0a87fb5d
                    offer: projects/************/services/choreo.endpoints.choreo-test.cloud.goog/standardOffers/5e40c136-4a8a-42c8-a243-38dfd452d19b
                    orderId: 377e4de4-9fec-4090-abb5-d82b0a87fb5d
                    plan: developer
                    product: choreo.endpoints.choreo-test.cloud.goog
                    productExternalName: choreo.endpoints.choreo-test.cloud.goog
                    provider: choreo-test
                    state: ENTITLEMENT_ACTIVE
                    updateTime: "2024-11-05T07:13:51.899217Z"
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /subscription/gcp/{orgUuid}/entitlement/approve:
    post:
      tags:
        - subscription
      description: Approves GCP entitlement for a given organization
      operationId: approveGcpEntitlement
      parameters:
        - $ref: '#/components/parameters/orgUuid'
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
            examples:
              success:
                summary: Empty request body for successful approval
                description: Send an empty request body to approve the GCP entitlement
                value: {}
      responses:
        "200":
          description: |
            OK.
            Approves GCP entitlement for a given organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GcpEntitlementApprovalStatus'
              examples:
                success:
                  value:
                    status: success
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /self-signup/{orgHandle}/config:
    post:
      tags:
        - org-management
      summary: Creates a new self signup config for a given organization
      description: Creates a new self signup config for a given organization
      operationId: createSelfSignupConfig
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: Self signup config object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SelfSignupConfig'
        required: true
      responses:
        "201":
          description: |
            Created.
            Successful response with the created self signup config object as an entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SelfSignupConfig'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /enterprise-login/{orgHandle}/config:
    get:
      tags:
        - org-management
      summary: Retrieves the enterprise login config for a given organization
      description: Retrieves the enterprise login config for a given organization
      operationId: getEnterpriseLoginConfig
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      responses:
        "200":
          description: |
            OK.
            Retrieve subscription details for a given organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnterpriseLoginConfig'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - org-management
      summary: Creates an enterprise login config for a given organization
      description: Creates an enterprise login config for a given organization
      operationId: createEnterpriseLoginConfig
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: Enterprise login config object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnterpriseLoginConfig'
        required: true
      responses:
        "201":
          description: |
            Created.
            Successful response with the created enterprise login config object as an entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnterpriseLoginConfig'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /reports/recent-deployments:
    get:
      tags:
        - reports
      summary: Get recent deployments across all organizations
      description: |
        Retrieve recent deployments across all organizations for given time up to now
      operationId: getReportsRecentDeployments
      parameters:
        - name: since
          in: query
          description: "The time in RFC3339 format to get the deployments since. (e.g. 2024-06-29T19:00:00.000+05:30)\nMake sure to encode + as %2B in the query parameter. \nIf not provided, return all deployments since the beginning of time.\n"
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: offset
          in: query
          description: |
            The pagination offset to start the list from. If not provided, the list will start from the beginning.
          required: false
          style: form
          explode: true
          schema:
            minimum: 0
            type: integer
            default: 0
        - name: limit
          in: query
          description: |
            The pagination limit to get the number of deployments. If not provided, the list will be paginated with the default limit.
          required: false
          style: form
          explode: true
          schema:
            maximum: 100
            minimum: 1
            type: integer
            default: 50
      responses:
        "200":
          description: |
            OK.
            Retrieve recent deployments across all organizations for given time up to now
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminRecentDeployment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /reports/domain-inspection:
    get:
      tags:
        - reports
      summary: Get domain inspection report for the given FQDN
      description: |
        Generates a domain inspection report for the given FQDN that contains the user, organization, project, and component details
      operationId: getReportsDomainInspection
      parameters:
        - name: fqdn
          in: query
          description: "Fully Qualified Domain Name (FQDN) of the domain to inspect. Should not include the protocol or path.\nExamples: \n- 0fc89fdc-7f1d-4a2e-be3c-595c0dfb0d23.e1-us-east-azure.preview-dv.choreoapps.dev\n- my-short-url.e1-us-east-azure.preview-dv.choreoapps.dev\n"
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            Retrieve domain inspection report for the given FQDN
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportResponseDomainInspection'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /actions/undeploy-component:
    post:
      tags:
        - actions
      operationId: postActionsUndeployComponent
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ActionRequestUndeployComponent'
        required: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActionResponseUndeployComponent'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /actions/undeploy-components/{orgUuid}:
    post:
      tags:
        - actions
      summary: Undeploy all components for a given organization
      description: This will undeploy all components for a given organization. If there are multiple releases for a component, all of them will be undeployed.
      operationId: postActionsUndeployComponentsOrgUuid
      parameters:
        - $ref: '#/components/parameters/orgUuid'
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActionResponseUndeployAllComponents'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /actions/make-read-only:
    post:
      tags:
        - actions
      summary: Make an organization read-only
      description: This will make an organization read-only by moving all the users to read-only group
      operationId: postActionsMakeReadOnly
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MakeReadOnlyRequest'
        required: true
      responses:
        "200":
          description: Ok. Organization was made read-only successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MakeReadOnlyResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /actions/revert-read-only:
    post:
      tags:
        - actions
      summary: Reverts the read-only status of an organization
      description: This will reverts the read-only status of an organization by moving all the users back to their original groups
      operationId: postActionsRevertReadOnly
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RevertReadOnlyStatusRequest'
        required: true
      responses:
        "200":
          description: Ok. Organization's read-only status was reverted successfully. Users are moved back to their original groups.
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /{orgUuid}/status:
    post:
      tags:
        - org-management
      summary: Update the status (ACTIVE/INACTIVE) for a given organization
      description: Update the status (ACTIVE/INACTIVE) for a given organization
      operationId: updateOrganizationStatus
      parameters:
        - $ref: '#/components/parameters/orgUuid'
      requestBody:
        description: Organization status object that needs to be updated
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrganizationStatus'
        required: true
      responses:
        "200":
          description: |
            Updated.
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /{orgUuid}/verification:
    post:
      tags:
        - org-management
      summary: Update the verification (VERIFIED/NON-VERIFIED) for a given organization
      description: Update the verification (VERIFIED/NON-VERIFIED) for a given organization
      operationId: updateVerification
      parameters:
        - $ref: '#/components/parameters/orgUuid'
      requestBody:
        description: Organization verification object that needs to be updated
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrganizationVerification'
        required: true
      responses:
        "200":
          description: |
            Updated.
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /invoices/{customerId}:
    post:
      tags:
        - billing
      summary: Post the invoices of a given customer to Salesforce
      description: Collects the invoices of a given customer from Stripe via the billing service and publishes them to Salesforce
      operationId: collectAndPublishInvoicesToSalesforce
      parameters:
        - name: customerId
          in: path
          description: The ID of the customer in Stripe
          required: true
          style: simple
          explode: false
          schema:
            type: string
            example: cus_1234
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
            examples:
              success:
                summary: Empty request body for successful publishing
                description: Send an empty request body to publish invoices to Salesforce
                value: {}
      responses:
        "200":
          description: |
            Updated.
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    ActionResponseUndeployAllComponents:
      required:
        - components
      type: object
      properties:
        components:
          type: array
          description: List of components that are undeployed
          example:
            - componentId: 0fc89fdc-7f1d-4a2e-be3c-595c0dfb0d23
              releases:
                - releaseId: 0496e611-5152-476c-8a04-358893f906a6
                  status: UNDEPLOYED
                - releaseId: 833c134a-fb47-4d1d-b9c4-5c8ddbefcae1
                  status: UNDEPLOYED
              status: UNDEPLOYED
            - componentId: c99658a9-8949-4d00-8b08-26388faa2d55
              releases:
                - releaseId: a4404e5e-4695-4778-b49a-41eb8ff81cc0
                  status: UNDEPLOYED
                - releaseId: f0b191df-300c-42f2-bf5e-b61a7262f24c
                  status: UNDEPLOY_FAILED
              status: UNDEPLOY_FAILED
          items:
            $ref: '#/components/schemas/UndeployedComponent'
      description: Object representation of the undeploy all components response that is exposed via the Admin API
    UndeployedComponent:
      required:
        - componentId
        - status
      type: object
      properties:
        componentId:
          type: string
          example: 0fc89fdc-7f1d-4a2e-be3c-595c0dfb0d23
        releases:
          type: array
          example:
            - releaseId: 0496e611-5152-476c-8a04-358893f906a6
              status: UNDEPLOYED
            - releaseId: 833c134a-fb47-4d1d-b9c4-5c8ddbefcae1
              status: UNDEPLOY_FAILED
          items:
            $ref: '#/components/schemas/UndeployedRelease'
        status:
          type: string
          example: UNDEPLOYED
      description: Object representation of an undeployed component with the undeployment status of the component including the releases
    UndeployedRelease:
      required:
        - releaseId
        - status
      type: object
      properties:
        releaseId:
          type: string
          example: 0496e611-5152-476c-8a04-358893f906a6
        status:
          type: string
          example: UNDEPLOYED
      description: Object representation of an undeployed release with the undeployment status
    ActionRequestUndeployComponent:
      required:
        - componentId
        - releaseId
      type: object
      properties:
        componentId:
          type: string
        releaseId:
          type: string
    ActionResponseUndeployComponent:
      type: object
      description: Object representation of the undeploy component response that is exposed via the Admin API
      allOf:
        - $ref: '#/components/schemas/Component'
        - type: object
          properties: {}
    AdminRecentDeployment:
      required:
        - componentId
        - componentName
        - componentType
        - createdAt
        - deploymentTrackId
        - kubernetesKind
        - kubernetesName
        - kubernetesNamespace
        - organizationId
        - organizationName
        - projectId
        - publicUrls
        - releaseId
        - updatedAt
      type: object
      properties:
        organizationId:
          type: string
          description: Organization ID of the deployment
        organizationName:
          type: string
          description: Organization name of the deployment
        projectId:
          type: string
          description: Project ID of the deployment
        componentId:
          type: string
          description: Component ID of the deployment
        componentName:
          type: string
          description: Component name of the deployment
        componentType:
          type: string
          description: Component type of the deployment
        kubernetesKind:
          type: string
          description: Kubernetes kind of the deployment (Deployment, Job, CronJob, etc.)
        kubernetesName:
          type: string
          description: Kubernetes name of the deployment
        kubernetesNamespace:
          type: string
          description: Kubernetes namespace of the deployment
        deploymentTrackId:
          type: string
          description: Deployment track ID of the deployment
        releaseId:
          type: string
          description: Release ID of the deployment
        createdAt:
          type: string
          description: Created at timestamp of the deployment
        updatedAt:
          type: string
          description: Updated at timestamp of the deployment
        publicUrls:
          type: array
          description: All the public URLs of the deployment
          items:
            type: string
      description: Type mapping for the admin recent deployments that is exposed via the Admin API
    Component:
      required:
        - componentId
        - componentName
        - componentType
        - deploymentTracks
        - kubernetesKind
        - organizationId
        - organizationName
        - projectId
      type: object
      properties:
        organizationId:
          type: string
          description: Organization ID of the component
        organizationName:
          type: string
          description: Organization name of the component
        projectId:
          type: string
          description: Project ID of the component
        componentId:
          type: string
          description: Component ID of the component
        componentName:
          type: string
          description: Component name of the component
        componentType:
          type: string
          description: Component type of the component
        kubernetesKind:
          type: string
          description: Kubernetes kind of the component (Deployment, Job, CronJob, etc.)
        deploymentTracks:
          type: array
          description: List of available deployment tracks of the component
          items:
            $ref: '#/components/schemas/DeploymentTrack'
        sourceRepository:
          type: string
          description: Source repository of the component
        gitOpsRepository:
          type: string
          description: GitOps repository of the component
      description: Object representation of the component
    DeploymentTrack:
      required:
        - id
        - releases
      type: object
      properties:
        id:
          type: string
          description: Deployment track ID
        branch:
          type: string
          description: Source branch of the deployment track. Empty if the component is BYOI or unlinked from the branch
        apiVersion:
          type: string
          description: API version of the deployment track. Empty for the component types that do not expose managed APIs.
        releases:
          type: array
          description: List of available releases of the deployment track
          items:
            $ref: '#/components/schemas/Release'
      description: Object representation of the deployment track
    Release:
      required:
        - environmentId
        - id
        - kubernetesName
        - publicUrls
        - scalingMethod
      type: object
      properties:
        id:
          type: string
          description: Release ID
        environmentId:
          type: string
          description: Environment ID of the release
        kubernetesName:
          type: string
          description: Kubernetes name of the deployment
        publicUrls:
          type: array
          description: All the public URLs of the deployment
          items:
            type: string
        scalingMethod:
          type: string
          description: Scaling method of the deployed release (HPA or ScaleToZero)
      description: Object representation of the release
    Subscription:
      title: Subscription
      type: object
      properties:
        organization:
          $ref: '#/components/schemas/Organization'
        billingDate:
          type: string
        createdDate:
          type: string
        paidCustomer:
          type: string
        status:
          type: string
        stepQuota:
          type: string
        tierName:
          type: string
          enum:
            - Free
            - Pay As You Go
            - Enterprise
        startDate:
          type: string
        endDate:
          type: string
    GcpSubscription:
      title: GcpSubscription
      required:
        - gcpMarketplaceCustomerId
        - id
        - orgUuid
      type: object
      properties:
        id:
          type: string
          description: Id of the GCP subscription
          readOnly: true
          example: 6fa459ea-ee8a-3ca4-894e-db77e160355e
        gcpMarketplaceCustomerId:
          type: string
          description: GCP Marketplace customer identifier
          example: 90d41507-8905-4f2b-becc-8484360b6f6r
        gcpMarketplaceCustomerGoogleAccountId:
          type: string
          description: GCP Marketplace customer Google account identifier
          example: s93e4de4-9fec-4090-abb5-d82b0a87fr3w
        gcpmarketplaceEntitlementId:
          type: string
          description: GCP Marketplace entitlement identifier
          example: 634e4de4-9fec-4090-abb5-d82b0a87fb9s
        productCode:
          type: string
          description: GCP Marketplace product code
          example: choreo-test
        contractType:
          type: string
          description: GCP Marketplace contract type
          example: enterprise
        subscriptionStatus:
          type: string
          description: GCP marketplace subscription status
          example: active
        orgUuid:
          type: string
          description: Organization UUID
          example: 9f0e1d2c-3b4a-5e6d-7f8g-9h0i1j2k3l4
        userIdpId:
          type: string
          description: User IDP ID
          example: 01ed65dc-e164-122c-9c05-dda14f6893ed
      description: Represents a GCP subscription object
    GcpEntitlement:
      title: GcpEntitlement
      required:
        - account
        - createTime
        - name
        - offer
        - orderId
        - plan
        - product
        - productExternalName
        - provider
        - state
        - updateTime
      type: object
      properties:
        account:
          type: string
          description: GCP customer account name
          example: providers/choreo-test/accounts/68d41507-8905-4f2b-becc-8484360b6f5c
        createTime:
          type: string
          description: GCP entitlement create time
          example: "2024-11-05T06:56:44.170427Z"
        name:
          type: string
          description: GCP entitlement name
          example: providers/choreo-test/entitlements/377e4de4-9fec-4090-abb5-d82b0a87fb5d
        offer:
          type: string
          description: GCP entitlement offer
          example: projects/************/services/choreo.endpoints.choreo-test.cloud.goog/standardOffers/5e40c136-4a8a-42c8-a243-38dfd452d19b
        orderId:
          type: string
          description: GCP entitlement ID
          example: 377e4de4-9fec-4090-abb5-d82b0a87fb5d
        plan:
          type: string
          description: Product plan
          example: developer
        product:
          type: string
          description: Product external name
          example: choreo.endpoints.choreo-test.cloud.goog
        productExternalName:
          type: string
          description: Provider identifier
          example: choreo.endpoints.choreo-test.cloud.goog
        provider:
          type: string
          description: GCP entitlement state
          example: choreo-test
        state:
          type: string
          description: GCP entitlement update time
          example: ENTITLEMENT_CANCELLED
        updateTime:
          type: string
          description: GCP Marketplace entitlement update time
          example: "2024-11-05T07:13:51.899217Z"
      description: Represents a GCP entitlement object
    GcpEntitlementApprovalStatus:
      title: GcpEntitlementApprovalStatus
      required:
        - status
      type: object
      properties:
        status:
          type: string
          description: GCP entitlement approval status
          example: success
      description: Represents a GCP entitlement approval status object
    Organization:
      title: Organization
      type: object
      properties:
        orgUUID:
          type: string
        orgHandle:
          type: string
    OrganizationV2:
      required:
        - displayName
        - id
        - name
        - uuid
      type: object
      properties:
        id:
          type: integer
          description: Int ID of the organization
          format: int64
        uuid:
          type: string
          description: UUID of the organization
        name:
          type: string
          description: Name of the organization
        displayName:
          type: string
          description: Display name of the organization
        isPaid:
          type: boolean
          description: Indicates whether the organization is paid or not
        users:
          type: array
          description: Users of the organization
          items:
            $ref: '#/components/schemas/User'
      additionalProperties: false
      description: "Object representation of the organization. \nThe v2 will be used to differentiate from the existing Organization type in Subscription dao\n"
    ReportResponseDomainInspection:
      required:
        - organization
        - targetComponent
      type: object
      properties:
        organization:
          $ref: '#/components/schemas/OrganizationV2'
        targetComponent:
          $ref: '#/components/schemas/Component'
      additionalProperties: false
      description: Object representation of the domain inspection report
    SubscriptionUpdate:
      title: Subscription Update
      type: object
      properties:
        stepQuota:
          type: string
        tierName:
          type: string
          enum:
            - Free
            - Enterprise
    TierDetail:
      title: New tier detail
      required:
        - tierId
      type: object
      properties:
        tierId:
          type: string
    SubscriptionV2:
      title: Component based subscription
      required:
        - billingDate
        - billingProvider
        - createdAt
        - id
        - infraCostSubscriptionItemId
        - isPaid
        - orgHandle
        - orgId
        - status
        - subscriptionItemId
        - subscriptionType
        - tierId
      type: object
      properties:
        id:
          type: string
        orgId:
          type: string
        orgHandle:
          type: string
        tierId:
          type: string
        subscriptionItemId:
          type: string
        infraCostSubscriptionItemId:
          type: string
        subscriptionType:
          type: string
        billingProvider:
          type: string
        billingDate:
          type: integer
          readOnly: true
        status:
          type: string
        isPaid:
          type: boolean
        createdAt:
          type: integer
          readOnly: true
    SelfSignupConfig:
      title: Self signup config
      required:
        - isAutoApprovalEnabled
        - isCustom
        - isEnabled
      type: object
      properties:
        id:
          type: integer
          description: Self Signup Config ID
          readOnly: true
        orgUUID:
          type: string
          description: Organization UUID
        isEnabled:
          type: boolean
          description: Whether self signup is enabled for the organization
        isAutoApprovalEnabled:
          type: boolean
          description: Whether auto approval is enabled for the organization
        isCustom:
          type: boolean
          description: Whether a custom implementation is provided for the approval process if auto approval is disabled
        customEndpoint:
          type: string
          description: Endpoint of the custom implementation for the approval process
        createdAt:
          type: string
          description: Created datetime
          readOnly: true
        updatedAt:
          type: string
          description: Updated datetime
          readOnly: true
      description: Object representation of the self signup config
    User:
      required:
        - displayName
        - groups
        - id
        - uuid
      type: object
      properties:
        id:
          type: integer
          description: Int ID of the user
          format: int64
        uuid:
          type: string
          description: UUID of the user
        displayName:
          type: string
          description: Display name of the user
        groups:
          type: array
          description: Groups of the user
          items:
            type: string
        organizations:
          type: array
          description: Organizations where the user belongs to
          items:
            $ref: '#/components/schemas/OrganizationV2'
      additionalProperties: false
      description: Object representation of the user within an organization
    EnterpriseLoginConfig:
      title: Enterprise login config
      required:
        - isEidpEnabled
      type: object
      properties:
        id:
          type: integer
          description: Enterprise login Config ID
          readOnly: true
        orgUUID:
          type: string
          description: Organization UUID
        isEidpEnabled:
          type: boolean
          description: Whether Enterprise login is enabled for the organization
        createdAt:
          type: string
          description: Created datetime
          readOnly: true
        updatedAt:
          type: string
          description: Updated datetime
          readOnly: true
      description: Object representation of the enterprise login config
    Error:
      title: Error object
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: integer
          description: Error code (i.e 400, 404 etc)
          format: int64
        message:
          type: string
          description: Error message.
      description: Error object returned with 4XX HTTP Status
    MakeReadOnlyRequest:
      required:
        - orgUuid
      type: object
      properties:
        orgUuid:
          type: string
          description: Organization UUID
          example: myorganization
    MakeReadOnlyResponse:
      required:
        - orgUuid
        - users
      type: object
      properties:
        orgUuid:
          type: string
          description: Organization UUID
          example: myorganization
        users:
          type: array
          description: Groups of the existing users
          example:
            - userId: 65
              groups:
                - 20316
                - 20318
            - userId: 129
              groups:
                - 20316
          items:
            $ref: '#/components/schemas/UserGroups'
    RevertReadOnlyStatusRequest:
      required:
        - orgUuid
        - users
      type: object
      properties:
        orgUuid:
          type: string
          description: Organization UUID
          example: myorganization
        users:
          type: array
          example:
            - userId: 65
              groups:
                - 20316
            - userId: 129
              groups:
                - 20316
          items:
            $ref: '#/components/schemas/UserGroups'
    UserGroups:
      required:
        - groups
        - userId
      type: object
      properties:
        userId:
          type: integer
          description: User ID
          format: int64
          example: 65
        groups:
          type: array
          description: List of groups
          items:
            type: integer
            format: int64
    OrganizationStatus:
      title: OrganizationStatusEntity
      required:
        - status
      type: object
      properties:
        status:
          type: string
          description: status of the organization (ACTIVE/INACTIVE)
          example: ACTIVE
    OrganizationVerification:
      title: OrganizationVerificationEntity
      required:
        - verification
      type: object
      properties:
        verification:
          type: string
          description: Verification of the organization (VERIFIED/NON-VERIFIED)
          example: VERIFIED
  responses:
    BadRequest:
      description: Bad Request. Invalid request or validation error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Bad Request
            description: Invalid request or validation error
    Unauthorized:
      description: Unauthorized. The user is not authorized.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 401
            message: Unauthorized
            description: The user is not authorized
    Forbidden:
      description: Forbidden. The request must be conditional but no condition has been specified.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 403
            message: Forbidden
    NotFound:
      description: Not Found. The specified resource does not exist.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 404
            message: Not Found
            description: The specified resource does not exist
    Conflict:
      description: Conflict. Specified resource already exists.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 409
            message: Conflict
            description: Specified resource already exists
    InternalServerError:
      description: Internal Server Error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 500
            message: Internal Server Error
  parameters:
    orgId:
      name: orgId
      in: path
      description: |
        Organisation ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    orgUuid:
      name: orgUuid
      in: path
      description: |
        Organisation UUID
      required: true
      style: simple
      explode: false
      schema:
        type: string
      example: 9f0e1d2c-3b4a-5e6d-7f8g-9h0i1j2k3l4
    orgHandle:
      name: orgHandle
      in: path
      description: |
        Organization Handle
      required: true
      style: simple
      explode: false
      schema:
        type: string
    entitlementId:
      name: entitlementId
      in: path
      description: |
        Entitlement ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
      example: 634e4de4-9fec-4090-abb5-d82b0a87fb9s
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
