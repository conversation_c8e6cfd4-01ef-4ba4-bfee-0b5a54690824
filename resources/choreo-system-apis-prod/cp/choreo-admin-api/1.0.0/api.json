{"id": "633be0a544f40d1b459e5a9f", "name": "Choreo Admin API", "displayName": "Choreo Admin API", "description": "This is a RESTFul API to perform Administrative operations in Choreo\n", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api", "version": "1.0.0", "provider": "92fd492b-11ce-4f64-a4be-d03c0d1a4aa7", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Bronze", "Gold", "Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": ["admin"], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [], "additionalPropertiesMap": {}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1664868517194", "lastUpdatedTime": "2025-06-17 11:28:47.409", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}, "production_endpoints": {"url": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/subscription/{orgId}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/subscription/{orgId}", "verb": "GET", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/subscription/{orgId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/subscription/{orgId}", "verb": "PUT", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/subscription/{orgId}/v2", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/subscription/{orgId}/v2", "verb": "GET", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/subscription/{orgId}/v2", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/subscription/{orgId}/v2", "verb": "PUT", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/self-signup/{orgHand<PERSON>}/config", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/self-signup/{orgHand<PERSON>}/config", "verb": "POST", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/enterprise-login/{orgHandle}/config", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/enterprise-login/{orgHandle}/config", "verb": "GET", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/enterprise-login/{orgHandle}/config", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/enterprise-login/{orgHandle}/config", "verb": "POST", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/actions/undeploy-components/{orgUuid}", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/actions/undeploy-components/{orgUuid}", "verb": "POST", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/actions/make-read-only", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/actions/make-read-only", "verb": "POST", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/actions/revert-read-only", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/actions/revert-read-only", "verb": "POST", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/{orgUuid}/status", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/{orgUuid}/status", "verb": "POST", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}, {"id": "", "target": "/{orgUuid}/verification", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cfcb8ce9-2dd8-4e93-8874-2c1c64adeebf", "backendOperation": {"target": "/{orgUuid}/verification", "verb": "POST", "endpoint": "http://choreo-admin-api.prod-choreo-system.svc.cluster.local:9090/org"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "92fd492b-11ce-4f64-a4be-d03c0d1a4aa7", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "397c30b1-8169-4d9c-a993-3be1d7b2a484", "versionId": "633be0a544f40d1b459e5a9f"}}