{"data": {"component": {"id": "397c30b1-8169-4d9c-a993-3be1d7b2a484", "name": "choreo-admin-api", "handler": "nxlyra", "description": " ", "displayType": "proxy", "displayName": "Choreo Admin API", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "1.0.0", "labels": [], "createdAt": "2022-10-04T07:28:37.944Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "Choreo Admin API", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo_admin_api", "proxyId": "633be0a544f40d1b459e5a9f", "id": "633be0a544f40d1b459e5a9f", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "633be0a544f40d1b459e5a9f", "createdAt": "1664868517194", "updatedAt": "2023-06-13 03:57:17.624", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "397c30b1-8169-4d9c-a993-3be1d7b2a484", "latest": true, "versionStrategy": ""}]}}}