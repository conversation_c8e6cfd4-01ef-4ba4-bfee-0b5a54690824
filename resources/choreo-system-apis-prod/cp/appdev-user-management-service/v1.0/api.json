{"id": "672b52252b85a02e9583cf6a", "name": "Appdev User Management Service", "displayName": "Appdev User Management Service", "description": "API for the Appdev User Management Service.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/appdev-user-management", "version": "v1.0", "provider": "749f8806-73d2-4a26-99d0-399de85e5586", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": false, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1730892325294", "lastUpdatedTime": "2025-04-01 07:50:25.774", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080"}, "production_endpoints": {"url": "http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/user-stores/{userstore-id}/users/authenticate", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f3f507ca-2268-4612-92e7-3b6071b4764a", "backendOperation": {"target": "/user-stores/{userstore-id}/users/authenticate", "verb": "POST"}}}, {"id": "", "target": "/user-stores/associations", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f3f507ca-2268-4612-92e7-3b6071b4764a", "backendOperation": {"target": "/user-stores/associations", "verb": "GET"}}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "749f8806-73d2-4a26-99d0-399de85e5586", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "12ddf6c5-0a31-40af-bf55-37e6bf903631", "versionId": "672b52252b85a02e9583cf6a"}}