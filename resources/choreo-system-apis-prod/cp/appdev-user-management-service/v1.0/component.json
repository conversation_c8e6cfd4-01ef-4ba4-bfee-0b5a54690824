{"data": {"component": {"id": "12ddf6c5-0a31-40af-bf55-37e6bf903631", "name": "appdev-user-management-service", "handler": "appdev-user-management-service", "description": " ", "displayType": "proxy", "displayName": "Appdev User Management Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-11-06T11:30:17.271Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Appdev User Management Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/appdev-user-management", "proxyId": "672b52252b85a02e9583cf6a", "id": "672b52252b85a02e9583cf6a", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "672b52252b85a02e9583cf6a", "createdAt": "1730892325294", "updatedAt": "2024-11-06 11:25:25.294", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "12ddf6c5-0a31-40af-bf55-37e6bf903631", "latest": true, "versionStrategy": ""}]}}}