openapi: 3.0.3
info:
  title: Appdev User Management Service
  description: This is the RESTful API for Choreo Appdev User Management Service.
  contact: {}
  version: v1.0
servers:
  - url: https://app.choreo.dev/93tu/appdev-user-management/v1.0
security:
  - default: []
paths:
  /user-stores/{userstore-id}/users/authenticate:
    post:
      tags:
        - User Authentication
      summary: Authenticate a user from the user store
      description: Authenticate a user from the user store
      operationId: authenticateUser
      parameters:
        - name: userstore-id
          in: path
          description: ID of the user store
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: e0fbcfeb-3617-43c4-8dd0-7b7d38e13047
        - name: orgId
          in: query
          description: ID of the organization
          required: true
          style: form
          explode: true
          schema:
            type: string
          example: e0fbcfeb-3617-43c4-8dd0-7b7d38e13047
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginReq'
      responses:
        "200":
          description: Successfully authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginRes'
        "400":
          description: Client error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "404":
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /user-stores/associations:
    get:
      tags:
        - User Store Association Management
      summary: List user store associations
      description: List user store associations
      operationId: listUserStoreAssociations
      parameters:
        - name: orgId
          in: query
          description: ID of the organization
          required: false
          style: form
          explode: true
          schema:
            type: string
          example: e0fbcfeb-3617-43c4-8dd0-7b7d38e13047
        - name: environmentId
          in: query
          description: ID of the environment
          required: false
          style: form
          explode: true
          schema:
            type: string
          example: e0fbcfeb-3617-43c4-8dd0-7b7d38e13047
      responses:
        "200":
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AssocRes'
        "400":
          description: Client error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "404":
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    LoginReq:
      type: object
      properties:
        username:
          type: string
          example: <EMAIL>
        password:
          type: string
          example: mypassword
    LoginRes:
      type: object
      properties:
        username:
          type: string
          example: <EMAIL>
      additionalProperties: true
    AssocRes:
      type: object
      properties:
        orgId:
          type: string
          example: e0fbcfeb-3617-43c4-8dd0-7b7d38e13047
        userStoreId:
          type: string
          example: e0fbcfeb-3617-43c4-8dd0-7b7d38e13047
        environmentId:
          type: string
          example: e0fbcfeb-3617-43c4-8dd0-7b7d38e13047
        priority:
          type: integer
          example: 1
    Error:
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: string
          example: AAA-00000
        message:
          type: string
          example: Some Error Message
        description:
          type: string
          example: Some Error Description
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://app-dev-user-mgt-service.prod-choreo-system.svc.cluster.local:8080
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/appdev-user-management/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
