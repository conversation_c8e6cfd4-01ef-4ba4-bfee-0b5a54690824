{"data": {"component": {"id": "d90db053-996e-44d8-8b05-fa469de33feb", "name": "Workflow Management API", "handler": "Workflow Management API", "description": " ", "displayType": "proxy", "displayName": "Workflow Management API", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-09-18T04:07:12.276Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Workflow Management API", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/workflow-mgt", "proxyId": "66ea50d5ebf87a2d63485eb1", "id": "66ea50d5ebf87a2d63485eb1", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "66ea50d5ebf87a2d63485eb1", "createdAt": "1726632149432", "updatedAt": "2024-09-18 04:02:29.432", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "d90db053-996e-44d8-8b05-fa469de33feb", "latest": true, "versionStrategy": ""}]}}}