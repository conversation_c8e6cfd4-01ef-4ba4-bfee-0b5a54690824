openapi: 3.0.1
info:
  title: Workflow Management API
  description: This api is used to connect to the Workflow Management Service
  version: v1.0
servers:
  - url: https://app.choreo.dev/93tu/workflow-mgt/v1.0
security:
  - default: []
paths:
  /workflow/definitions:
    get:
      summary: Get all the workflow definitions defined in Choreo.
      operationId: getWorkflowDefinitions
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WorkflowDefinition'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /workflow/configs:
    get:
      summary: Get all the workflow configurations defined in the organization.
      operationId: getWorkflowConfigs
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrgWorkflowConfig'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      summary: Configure a workflow for the organization.
      operationId: postWorkflowConfigs
      requestBody:
        description: Workflow configuration request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrgWorkflowConfigRequest'
        required: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrgWorkflowConfig'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "409":
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /workflow/configs/{workflow-config-id}:
    put:
      summary: Update a workflow configuration.
      operationId: putWorkflowConfigsWorkflowConfigId
      parameters:
        - name: workflow-config-id
          in: path
          description: Identifier of the workflow configuration to update
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Updated workflow configuration
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrgWorkflowConfigRequest'
        required: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrgWorkflowConfig'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      summary: Delete a workflow configuration.
      operationId: deleteWorkflowConfigsWorkflowConfigId
      parameters:
        - name: workflow-config-id
          in: path
          description: Identifier of the workflow configuration to delete
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrgWorkflowConfig'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /workflow-instances:
    get:
      summary: Get filtered workflow instances active in the organization.
      operationId: getWorkflowInstances
      parameters:
        - name: wkfDefinitionId
          in: query
          description: Workflow definition ID to filter the workflows (Defined in Choreo)
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: offset
          in: query
          description: Offset to start returning workflows
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 0
        - name: resource
          in: query
          description: Resource bound to workflow instances to filter the workflows
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: limit
          in: query
          description: Maximum number of workflows to return
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 20
        - name: created-by
          in: query
          description: User who created the workflows to filter the workflows
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: status
          in: query
          description: Status to filter the workflows
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WorkflowInstanceResponse'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      summary: Create a new workflow instance.
      operationId: postWorkflowInstances
      requestBody:
        description: Workflow request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkflowInstanceCreateRequest'
        required: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "201":
          description: Created
          content:
            text/plain:
              schema:
                type: string
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "409":
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /workflow-instances/{workflow-instance-id}:
    get:
      summary: Get a specific workflow instance.
      operationId: getWorkflowInstancesWorkflowInstanceId
      parameters:
        - name: workflow-instance-id
          in: path
          description: Identifier of the workflow instance
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowInstanceResponse'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      summary: Cancel a workflow request.
      operationId: deleteWorkflowInstancesWorkflowInstanceId
      parameters:
        - name: workflow-instance-id
          in: path
          description: Identifier of the workflow instance
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /workflow-instances/{workflow-instance-id}/cancellation:
    post:
      operationId: postWorkflowInstancesWorkflowInstanceIdCancellation
      parameters:
        - name: workflow-instance-id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "201":
          description: Created
          content:
            text/plain:
              schema:
                type: string
              example: 02ef795f-c7a3-1598-8676-dde7b445ec93
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /workflow-instances/status:
    get:
      summary: |-
        Get the status of workflows related to a given Choreo operation (workflow definition) and a resource.
        This is used to check if workflow is enabled, disabled, pending, rejected or approved.
      operationId: getWorkflowInstancesStatus
      parameters:
        - name: wkfDefinitionId
          in: query
          description: Id of the workflow definition for the relevant Choreo operation
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: resource
          in: query
          description: Resource on which the action is performed
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowMgtStatusInfo'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /review/{workflow-instance-id}/decision:
    post:
      summary: Review a workflow request. This is used to approve or reject a workflow request. The review can be done by privileged users.
      operationId: postReviewWorkflowInstanceIdDecision
      parameters:
        - name: workflow-instance-id
          in: path
          description: Identifier of the workflow instance
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Payload with review details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReviewerDecisionRequest'
        required: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "201":
          description: Created
          content:
            text/plain:
              schema:
                type: string
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /review/{workflow-instance-id}/data:
    get:
      summary: |-
        Get the formatted review data captured at the workflow request in the form of {matadata, requestor comment, data}
        This is used to display the captured data in UI/email/notifications in a presentable manner.
        The schema which is used to format data is defined in the workflow definition.
      operationId: getReviewWorkflowInstanceIdData
      parameters:
        - name: workflow-instance-id
          in: path
          description: Identifier of the workflow instance
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InformationToReview'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /audits:
    get:
      summary: Get all the audits of the workflows in the organization based on the filters.
      operationId: getAudits
      parameters:
        - name: wkfDefinitionId
          in: query
          description: Workflow definition ID (Choreo Operation) to filter the audits
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: offset
          in: query
          description: Offset to start returning audits
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 0
        - name: resource
          in: query
          description: Resource Id to filter the audits
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: event-type
          in: query
          description: Audit event type to filter the audits
          required: false
          style: form
          explode: true
          schema:
            nullable: true
            allOf:
              - $ref: '#/components/schemas/AuditEventType'
        - name: user-id
          in: query
          description: User ID to filter the audits (email of user who performed the action)
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: limit
          in: query
          description: Maximum number of audits to return
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 20
        - name: orgId
          in: query
          description: Organization ID
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: status
          in: query
          description: Status to filter the audits
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditEvent'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /audits/{workflow-instance-id}:
    get:
      summary: Get all the audits of a specific workflow run.
      operationId: getAuditsWorkflowInstanceId
      parameters:
        - name: workflow-instance-id
          in: path
          description: Identifier of the workflow instance
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/HeaderNotFoundError'
                  - $ref: '#/components/schemas/ErrorPayload'
                  - $ref: '#/components/schemas/ErrorDetails'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditEvent'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    ApproverType:
      type: string
      enum:
        - USER
        - ROLE
    AuditEvent:
      type: object
      description: Represents an audit event.
      allOf:
        - $ref: '#/components/schemas/AuditEventRequest'
        - required:
            - id
            - orgId
            - workflowDefinition
          type: object
          properties:
            id:
              type: string
            orgId:
              type: string
            workflowDefinition:
              required:
                - description
                - id
                - name
              type: object
              properties:
                id:
                  type: string
                name:
                  type: string
                description:
                  type: string
              additionalProperties: false
          additionalProperties: false
    AuditEventRequest:
      required:
        - eventType
        - resource
        - timestamp
        - user
        - workflowDefinitionId
        - workflowInstanceId
      type: object
      properties:
        timestamp:
          $ref: '#/components/schemas/Utc'
        eventType:
          $ref: '#/components/schemas/AuditEventType'
        user:
          type: string
        resource:
          type: string
        workflowInstanceId:
          type: string
        comment:
          type: string
        workflowDefinitionId:
          type: string
      additionalProperties: false
      description: Request to create an audit event.
    AuditEventType:
      type: string
      enum:
        - CANCEL
        - EXECUTE
        - REJECT
        - APPROVE
        - CREATE
    Decision:
      type: object
      allOf:
        - $ref: '#/components/schemas/ReviewerDecisionRequest'
        - required:
            - reviewedTime
            - reviewedUser
          type: object
          properties:
            reviewedUser:
              $ref: '#/components/schemas/User'
            reviewedTime:
              type: string
          additionalProperties: false
    ErrorDetails:
      required:
        - details
        - error
      type: object
      properties:
        error:
          type: string
          example: Internal Server Error
        details:
          type: string
          example: Error occurred while processing the request
      additionalProperties: false
      example:
        error: Internal Server Error
        details: Error occurred while processing the request
    ErrorPayload:
      required:
        - message
        - method
        - path
        - reason
        - status
        - timestamp
      type: object
      properties:
        timestamp:
          type: string
          example: "2024-10-16T10:00:00Z"
        status:
          type: integer
          format: int64
          example: 500
        reason:
          type: string
          example: Internal Server Error
        message:
          type: string
          example: Error occurred while processing the request
        path:
          type: string
          example: /workflow-instances/123/cancellation
        method:
          type: string
          example: POST
    FormatSchemaEntry:
      required:
        - dataType
        - displayName
        - extractfrom
        - required
      type: object
      properties:
        displayName:
          type: string
        dataType:
          type: string
        extractfrom:
          type: string
        required:
          type: boolean
      additionalProperties: false
      description: |-
        Schema instruction on how to format input data field to form
        data for the workflow
    HeaderNotFoundError:
      $ref: '#/components/schemas/ErrorPayload'
    InformationToReview:
      required:
        - data
        - metadata
      type: object
      properties:
        metadata:
          required:
            - actionToReview
            - orgName
            - workflowId
          type: object
          properties:
            workflowId:
              type: string
            actionToReview:
              type: string
            orgName:
              type: string
            projectName:
              type: string
            componentName:
              type: string
        comment:
          type: string
        data:
          type: object
      additionalProperties: false
      description: Structure to hold the information to review in a workflow instance.
    OrgWorkflowConfig:
      type: object
      description: |-
        Organizational configuration of a workflow definition.
        A workflow instance is created within the org based on this configuration.
      allOf:
        - $ref: '#/components/schemas/OrgWorkflowConfigRequest'
        - required:
            - id
            - orgId
          type: object
          properties:
            id:
              type: string
            orgId:
              type: string
          additionalProperties: false
    OrgWorkflowConfigRequest:
      required:
        - assigneeRoles
        - assignees
        - enabled
        - workflowDefinitionId
      type: object
      properties:
        workflowDefinitionId:
          type: string
        assigneeRoles:
          type: array
          items:
            type: string
        assignees:
          type: array
          items:
            type: string
        enabled:
          type: boolean
        formatRequestData:
          type: boolean
          default: true
        externalWorkflowEngineEndpoint:
          type: string
      additionalProperties: false
      description: Request to configure a workflow definition for an organization.
    ReviewerDecision:
      type: string
      description: Decision made by the reviewer.
      enum:
        - APPROVED
        - REJECTED
    ReviewerDecisionRequest:
      required:
        - decision
      type: object
      properties:
        decision:
          $ref: '#/components/schemas/ReviewerDecision'
        reviewComment:
          type: string
      additionalProperties: false
      description: Request to update a workflow instance with a reviewer decision.
    User:
      required:
        - displayName
        - email
        - idpId
      type: object
      properties:
        idpId:
          type: string
        email:
          type: string
        displayName:
          type: string
      additionalProperties: false
    Utc:
      type: array
      items:
        oneOf:
          - type: integer
            format: int64
          - type: number
            format: double
    WorkflowContext:
      required:
        - resource
        - workflowDefinitionIdentifier
      type: object
      properties:
        workflowDefinitionIdentifier:
          type: string
        resource:
          type: string
      additionalProperties: false
      description: Context of the workflow. This is used to identify the workflow instance uniquely within the org.
    WorkflowDefinition:
      type: object
      description: |-
        Workflow definition related to a Choreo operation. This defines the behavior
        of the workflow (i.e approval process for a given action).
      allOf:
        - $ref: '#/components/schemas/WorkflowDefinitionIdentifier'
        - required:
            - allowParallelRequests
            - approverTypes
            - executeUponApproval
            - requestFormatSchema
          type: object
          properties:
            approverTypes:
              type: array
              items:
                $ref: '#/components/schemas/ApproverType'
            executeUponApproval:
              type: boolean
            allowParallelRequests:
              type: boolean
            requestFormatSchema:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/FormatSchemaEntry'
          additionalProperties: false
    WorkflowDefinitionIdentifier:
      required:
        - description
        - id
        - name
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
      additionalProperties: false
      description: Identifier of a workflow definition.
    WorkflowInstanceCreateRequest:
      required:
        - context
        - data
      type: object
      properties:
        context:
          $ref: '#/components/schemas/WorkflowContext'
        requestComment:
          type: string
        data:
          $ref: '#/components/schemas/WorkflowInstanceData'
      additionalProperties: false
      description: Request to create a workflow instance
    WorkflowInstanceData:
      required:
        - metadata
        - payload
      type: object
      properties:
        metadata:
          required:
            - orgName
          type: object
          properties:
            orgName:
              type: string
            projectName:
              type: string
            componentName:
              type: string
        payload:
          type: object
      additionalProperties: false
      description: Structure to hold the data related to a workflow instance.
    WorkflowInstanceResponse:
      type: object
      description: Represents a workflow instance created within an organization with workflow definition information.
      allOf:
        - $ref: '#/components/schemas/WorkflowInstanceCreateRequest'
        - required:
            - createdTime
            - createdUser
            - orgId
            - wkfId
            - workflowDefinitionIdentifier
          type: object
          properties:
            wkfId:
              type: string
            orgId:
              type: string
            createdTime:
              type: string
            createdUser:
              $ref: '#/components/schemas/User'
            workflowDefinitionIdentifier:
              $ref: '#/components/schemas/WorkflowDefinitionIdentifier'
            reviewerDecision:
              $ref: '#/components/schemas/Decision'
            status:
              $ref: '#/components/schemas/WorkflowMgtStatus'
          additionalProperties: false
    WorkflowMgtStatus:
      type: string
      description: |-
        Status of a workflow (Choreo operation) within the org.
        This is not necessarily the status of the workflow instance.
      enum:
        - CANCELLED
        - TIMEOUT
        - REJECTED
        - APPROVED
        - PENDING
        - NOT_FOUND
        - DISABLED
        - ENABLED
    WorkflowMgtStatusInfo:
      required:
        - status
      type: object
      properties:
        status:
          $ref: '#/components/schemas/WorkflowMgtStatus'
        wkfInstanceId:
          type: string
      additionalProperties: false
      description: |-
        Status of a workflow (Choreo operation) within the org
        along with the workflow instance ID.
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins: []
  accessControlAllowCredentials: false
  accessControlAllowHeaders: []
  accessControlAllowMethods: []
x-wso2-production-endpoints:
  urls:
    - http://workflow-mgt.prod-choreo-system.svc.cluster.local:9082/workflow-mgt/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://workflow-mgt.prod-choreo-system.svc.cluster.local:9082/workflow-mgt/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/workflow-mgt/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
