{"data": {"component": {"id": "1f3a451e-62fa-4d4c-bc42-03fc0212e5b3", "name": "insightsalert", "handler": "jltoky", "description": " ", "displayType": "proxy", "displayName": "InsightsAlert", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "1.0.0", "labels": [], "createdAt": "2021-12-18T12:30:12.849Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "InsightsAlert", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/insightsalert", "proxyId": "61bdd3ccbd84cd17397efedf", "id": "61bdd3ccbd84cd17397efedf", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "61bdd3ccbd84cd17397efedf", "createdAt": "1639830476199", "updatedAt": "2024-07-12 04:45:59.213", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "1f3a451e-62fa-4d4c-bc42-03fc0212e5b3", "latest": true, "versionStrategy": ""}]}}}