openapi: 3.0.1
info:
  title: InsightsAlert
  description: |
    This is a RESTFul API to configure alerts
  contact:
    name: <PERSON><PERSON><PERSON><PERSON>
    url: https://www.wso2.com
    email: ka<PERSON><EMAIL>
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  version: v0.1.9
servers:
  - url: /
security:
  - default: []
paths:
  /latencyConfigs:
    get:
      description: Get latency alert configurations
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: List of Latency alerts.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfigurationList'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      description: Add latency alert configurations
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
      requestBody:
        description: Latency alert configuration details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlertConfiguration'
        required: true
      responses:
        "201":
          description: Latency alert successfully added.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /latencyConfigs/{alertConfigurationId}:
    get:
      description: Get Latency alert by alert configuration ID
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      responses:
        "200":
          description: Latency alert configuration details.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      description: Update Latency alert configuration
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      requestBody:
        description: Updated Latency alert configuration
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlertConfiguration'
        required: true
      responses:
        "200":
          description: Latency alert configuration successfully updated.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      description: Delete Latency alert by alert configuration ID
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      responses:
        "200":
          description: Latency alert configuration deleted.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /trafficConfigs:
    get:
      description: Get Traffic alert configurations
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: List of Traffic alerts.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfigurationList'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      description: Add Traffic alert configurations
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
      requestBody:
        description: Traffic alert configuration details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlertConfiguration'
        required: true
      responses:
        "201":
          description: Traffic alert configuration successfully added.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /trafficConfigs/{alertConfigurationId}:
    get:
      description: Get Traffic alert by alert configuration ID
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      responses:
        "200":
          description: Traffic alert configuration details.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      description: Update Traffic alert configuration
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      requestBody:
        description: Updated TRaffic alert configuration
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlertConfiguration'
        required: true
      responses:
        "200":
          description: Traffic alert configuration successfully updated.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      description: Delete Traffic alert by alert configuration ID
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      responses:
        "200":
          description: Traffic alert configuration deleted.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /statusCodeConfigs:
    get:
      description: Get Error Status Code alert configurations
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: List of Error Code alerts.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfigurationList'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      description: Add Error Code alert configuration
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
      requestBody:
        description: Status Code alert configuration details
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlertConfiguration'
        required: true
      responses:
        "201":
          description: Error Status Code alert configuration successfully added.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /statusCodeConfigs/{alertConfigurationId}:
    get:
      description: Get Error Status Code alert by alert configuration ID
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      responses:
        "200":
          description: Error Status Code alert configuration details.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      description: Update Error Status Code alert configuration
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      requestBody:
        description: Updated Error Status Code alert configuration
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlertConfiguration'
        required: true
      responses:
        "200":
          description: Error Status Code alert configuration successfully updated.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertConfiguration'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      description: Delete Error Status Code alert by alert configuration ID
      parameters:
        - name: environment
          in: query
          description: environment
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: organization
          in: query
          description: organization
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: tenant
          in: query
          description: tenant
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/alertConfigurationId'
      responses:
        "200":
          description: Error Status Code alert configuration deleted.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          description: Authentication failure.
      security:
        - bearerAuth: []
        - default: []
      x-codegen-request-body-name: body
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    AlertConfigurationUserDetails:
      title: Alert Configuration user details
      type: object
      properties:
        tenant:
          type: string
        environment:
          type: string
        organization:
          type: string
    AlertConfiguration:
      title: Latency Alert configuration
      allOf:
        - $ref: '#/components/schemas/AlertConfigurationUserDetails'
        - required:
            - alertConfig
          type: object
          properties:
            alertConfiguration:
              $ref: '#/components/schemas/Alert'
    AlertConfigurationList:
      title: Alert configuration list
      type: object
      allOf:
        - $ref: '#/components/schemas/AlertConfigurationUserDetails'
        - required:
            - alertConfiguration
          type: object
          properties:
            count:
              type: integer
              description: Number of Latency Alert configurations returned.
            alertConfiguration:
              type: array
              items:
                $ref: '#/components/schemas/Alert'
    Alert:
      title: Alert
      required:
        - apiName
        - apiVersion
        - emails
        - metric
        - threshold
      type: object
      properties:
        id:
          type: string
        apiName:
          type: string
        apiVersion:
          type: string
        threshold:
          type: integer
        emails:
          type: array
          items:
            type: string
        category:
          type: string
        metric:
          type: string
    Error:
      title: Error object returned with 4XX HTTP status
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message.
        description:
          type: string
          description: |
            A detail description about the error message.
  responses:
    BadRequest:
      description: Bad Request. Invalid request or validation error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Bad Request
            description: Invalid request or validation error
            moreInfo: ""
            error: []
  parameters:
    alertConfigurationId:
      name: alertConfigurationId
      in: path
      description: |
        Alert Configuration UUID
      required: true
      style: simple
      explode: false
      schema:
        type: string
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - https://controlplane.choreoanalytics.internal/alert
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - https://controlplane.choreoanalytics.internal/alert
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insightsalert/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
