openapi: 3.0.0
info:
  title: Copilot Feedback Collector
  contact: {}
  version: v1.0
servers:
  - url: https://choreoapis.dev/93tu/copilotfeedbackcollector/v1.0
security:
  - default: []
paths:
  /feedback:
    post:
      summary: Submit feedback
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Feedback'
        required: true
      responses:
        "200":
          description: Feedback received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /disable:
    post:
      summary: Disable data collection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisableDataCollection'
        required: true
      responses:
        "200":
          description: Data collection status updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /permission:
    get:
      summary: Check permission status
      responses:
        "200":
          description: Permission status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  permission:
                    type: boolean
                    description: Indicates whether the user has granted permission
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /health:
    get:
      summary: Health check
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    Feedback:
      type: object
      properties:
        org_id:
          type: string
        session_id:
          type: string
        correlation_id:
          type: string
        feedback:
          type: integer
    DisableDataCollection:
      type: object
      properties:
        org_id:
          type: string
        disabled:
          type: boolean
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-api-key-header: api-key
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://copilot-datacollector.choreo-ai:8080/
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://copilot-datacollector.choreo-ai:8080/
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/copilotfeedbackcollector/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
