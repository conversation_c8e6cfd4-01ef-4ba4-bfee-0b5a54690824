{"data": {"component": {"id": "36806f61-8d37-4e1e-9b84-727494a47e7b", "name": "copilot-fb-collector-cr", "handler": "copilot-fb-collector-cr", "description": " ", "displayType": "proxy", "displayName": "Copilot Feedback Collector f59ad", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-04-24T14:19:35.760Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "copilot-feedback-collector", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/copilotfeedbackcollector", "proxyId": "680a3cb909019922a10c2c81", "id": "680a3cb909019922a10c2c81", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "680a3cb909019922a10c2c81", "createdAt": "1745501369090", "updatedAt": "2025-04-24 13:29:29.09", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "36806f61-8d37-4e1e-9b84-727494a47e7b", "latest": true, "versionStrategy": ""}]}}}