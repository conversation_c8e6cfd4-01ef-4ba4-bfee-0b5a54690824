{"data": {"component": {"id": "4a3c5d35-ca1d-43ce-940f-a6ec2e93e09c", "name": "cost-optimizer-cp-service", "handler": "cost-optimizer-cp-service", "description": " ", "displayType": "proxy", "displayName": "Cost Optimizer CP Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-03-07T06:03:32.338Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "cost optimizer cp service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/cost-optimizer-cp", "proxyId": "67ca8c1946d9173bc9cc0a24", "id": "67ca8c1946d9173bc9cc0a24", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67ca8c1946d9173bc9cc0a24", "createdAt": "1741327385172", "updatedAt": "2025-03-07 06:03:05.172", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "4a3c5d35-ca1d-43ce-940f-a6ec2e93e09c", "latest": true, "versionStrategy": ""}]}}}