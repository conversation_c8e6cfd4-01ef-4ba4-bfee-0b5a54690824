{"id": "67ca8c1946d9173bc9cc0a24", "name": "cost optimizer cp service", "displayName": "cost optimizer cp service", "description": "This api is used to connect to the cost optimizer cp service", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/cost-optimizer-cp", "version": "v1.0", "provider": "0228631a-5468-445b-af43-ba78b5468457", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": ["admin"], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>", "token", "x-request-id"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1741327385172", "lastUpdatedTime": "2025-07-22 09:22:13.582", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://cost-optimizer-cp.choreo-ai:8080/"}, "production_endpoints": {"url": "http://cost-optimizer-cp.choreo-ai:8080/"}}, "endpointImplementationType": "ENDPOINT", "subTypeConfiguration": {"subType": "DEFAULT", "egress": false, "configuration": null}, "tokenBasedThrottlingConfiguration": null, "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/auto-optimize-status", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ad6c0a9d-7d37-4b51-9a3c-d3fecbecec5c", "backendOperation": {"target": "/auto-optimize-status", "verb": "GET", "endpoint": "http://cost-optimizer-cp.choreo-ai:8080/"}}, "operationProxyMapping": null}, {"id": "", "target": "/auto-optimize-status", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ad6c0a9d-7d37-4b51-9a3c-d3fecbecec5c", "backendOperation": {"target": "/auto-optimize-status", "verb": "POST", "endpoint": "http://cost-optimizer-cp.choreo-ai:8080/"}}, "operationProxyMapping": null}, {"id": "", "target": "/recommendation-stats", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ad6c0a9d-7d37-4b51-9a3c-d3fecbecec5c", "backendOperation": {"target": "/recommendation-stats", "verb": "GET", "endpoint": "http://cost-optimizer-cp.choreo-ai:8080/"}}, "operationProxyMapping": null}, {"id": "", "target": "/recommendation-apply-status", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ad6c0a9d-7d37-4b51-9a3c-d3fecbecec5c", "backendOperation": {"target": "/recommendation-apply-status", "verb": "PUT", "endpoint": "http://cost-optimizer-cp.choreo-ai:8080/"}}, "operationProxyMapping": null}, {"id": "", "target": "/recommendations", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ad6c0a9d-7d37-4b51-9a3c-d3fecbecec5c", "backendOperation": {"target": "/recommendations", "verb": "GET", "endpoint": "http://cost-optimizer-cp.choreo-ai:8080/"}}, "operationProxyMapping": null}, {"id": "", "target": "/health", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ad6c0a9d-7d37-4b51-9a3c-d3fecbecec5c", "backendOperation": {"target": "/health", "verb": "GET", "endpoint": "http://cost-optimizer-cp.choreo-ai:8080/"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "0228631a-5468-445b-af43-ba78b5468457", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "4a3c5d35-ca1d-43ce-940f-a6ec2e93e09c", "versionId": "67ca8c1946d9173bc9cc0a24"}}