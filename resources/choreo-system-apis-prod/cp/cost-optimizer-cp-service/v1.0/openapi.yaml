openapi: 3.0.1
info:
  title: cost optimizer cp service
  contact: {}
  version: v1.0
servers:
  - url: http://localhost:8080/93tu/cost-optimizer-cp/v1.0
security:
  - default: []
paths:
  /rates/{cspName}:
    get:
      summary: Retrieve cloud instance rates
      description: |
        Retrieves CPU and Memory rates for a specified instance type and region from a given Cloud Service Provider (CSP).
        Currently, only 'aws' is supported for cspName.
      operationId: getCloudInstanceRates
      parameters:
        - name: cspName
          in: path
          description: Name of the Cloud Service Provider (e.g., 'aws', 'azure', 'gcp').
          required: true
          style: simple
          explode: false
          schema:
            type: string
            enum:
              - aws
            example: aws
        - name: instanceType
          in: query
          description: The specific instance type (e.g., 'm6i.large', 't3.micro').
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: m6i.large
        - name: region
          in: query
          description: The AWS region (e.g., 'us-east-1', 'eu-central-1').
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: us-west-2
      responses:
        "200":
          description: Successfully retrieved cloud instance rates.
          content:
            application/json:
              schema:
                type: object
                example:
                  instanceType: m6i.large
                  vCPUs: 2
                  memory: 8.0
                  region: us-west-2
                  pricePerUnitUSD: "0.1265"
                  hourlyCPURate: $0.0409 (per core/ hour)
                  hourlyMemoryRate: $0.000000000005215406 (per byte/hour)
                  cloudProvider: aws
                properties:
                  instanceType:
                    type: string
                    description: The type of cloud instance.
                  vCPUs:
                    type: integer
                    description: Number of virtual CPUs.
                  memory:
                    type: number
                    format: float
                    description: Amount of memory in GiB.
                  region:
                    type: string
                    description: The cloud region.
                  pricePerUnitUSD:
                    type: string
                    description: On-demand price per hour in USD.
                  hourlyCPURate:
                    type: string
                    description: Hourly CPU rate per core.
                  hourlyMemoryRate:
                    type: string
                    description: Hourly Memory rate per byte.
                  cloudProvider:
                    type: string
                    description: The Cloud Service Provider.
        "400":
          description: Bad Request – missing or invalid parameters, or unsupported CSP.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        "401":
          description: Unauthorized.
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /auto-optimize-status:
    get:
      summary: Retrieve auto optimize status records
      description: |
        Returns a list of auto optimize records for the specified organization and project. Optionally, a release identifier may be provided.
      operationId: getAutoOptimizeStatus
      parameters:
        - name: orgId
          in: query
          description: Organization identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: org123
        - name: projectId
          in: query
          description: Project identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: proj456
        - name: releaseId
          in: query
          description: Optional release identifier.
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: rel789
      responses:
        "200":
          description: A list of auto optimize records.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AutoOptimizeDbRecord'
              example:
                - orgId: org123
                  projectId: proj456
                  environmentId: env1
                  releaseId: rel789
                  componentId: comp101
                  status: true
        "400":
          description: Bad Request – missing or invalid parameters.
        "401":
          description: Unauthorized.
        "500":
          description: Internal Server Error.
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Create or update auto optimize status
      description: |
        Updates the auto optimize status for one or more environments for the specified organization and project.
      operationId: postAutoOptimizeStatus
      parameters:
        - name: orgId
          in: query
          description: Organization identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: org123
        - name: projectId
          in: query
          description: Project identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: proj456
        - name: releaseId
          in: query
          description: Optional release identifier.
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: rel789
        - name: componentId
          in: query
          description: Optional component identifier.
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: comp101
      requestBody:
        description: Payload containing the auto optimize status and the list of environment IDs.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutoOptimizePayload'
            example:
              status: true
              environmentId:
                - env1
                - env2
        required: true
      responses:
        "201":
          description: Auto optimize status updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutoOptimizeStatus'
              example:
                status: true
                environmentId:
                  - env1
                  - env2
        "400":
          description: Bad Request – invalid payload.
        "401":
          description: Unauthorized.
        "500":
          description: Internal Server Error.
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /recommendation-stats:
    get:
      summary: Retrieve recommendation statistics
      description: |
        Returns recommendation statistics for the given organization. If a project identifier is provided, project-level stats (with component details) are returned; otherwise, organization-level stats are returned.
      operationId: getRecommendationStats
      parameters:
        - name: orgId
          in: query
          description: Organization identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: org123
        - name: projectId
          in: query
          description: Optional project identifier for project-level stats.
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: proj456
      responses:
        "200":
          description: Recommendation statistics retrieved successfully.
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/RecommendationStatResponse'
                  - $ref: '#/components/schemas/RecommendationStatResponseWithComponent'
              examples:
                OrgLevel:
                  summary: Org-level stats (no projectId provided)
                  value:
                    envStats:
                      - environmentId: env1
                        projectStats:
                          - projectId: proj456
                            recommendationStats:
                              overProvisionedCount: 5
                              underProvisionedCount: 2
                              idleCount: 1
                            deploymentStats:
                              autoOptimizedCount: 3
                              requiredAttentionCount: 1
                ProjectLevel:
                  summary: Project-level stats (projectId provided)
                  value:
                    envStats:
                      - environmentId: env1
                        projectStats:
                          - projectId: proj456
                            componentStats:
                              - componentId: comp101
                                recommendationStats:
                                  overProvisionedCount: 2
                                  underProvisionedCount: 0
                                  idleCount: 0
                                deploymentStats:
                                  autoOptimizedCount: 1
                                  requiredAttentionCount: 0
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /recommendation-apply-status:
    put:
      summary: Update recommendation apply status
      description: |
        Marks a specific recommendation type as applied for the given organization, project, and release.
      operationId: updateRecommendationApplyStatus
      parameters:
        - name: orgId
          in: query
          description: Organization identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: org123
        - name: projectId
          in: query
          description: Project identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: proj456
        - name: releaseId
          in: query
          description: Release identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: rel789
      requestBody:
        description: Payload indicating which recommendation type is being applied.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecommendationApplyUpdatePayload'
            example:
              recommendationType: resource_right_size_savings
        required: true
      responses:
        "201":
          description: Recommendation apply status updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecommendationApplyUpdateStatus'
              example:
                status: true
        "400":
          description: Bad Request.
        "401":
          description: Unauthorized.
        "500":
          description: Internal Server Error.
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /recommendations:
    get:
      summary: Retrieve recommendations for a release
      description: |
        Returns recommendations for resource right-sizing, enabling S2Z, and stopping deployment for the specified organization and release.
      operationId: getRecommendations
      parameters:
        - name: orgId
          in: query
          description: Organization identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: org123
        - name: releaseId
          in: query
          description: Release identifier.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: rel789
      responses:
        "200":
          description: Recommendations retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecommendationsResponse'
              example:
                resource_right_size_savings:
                  apply: false
                  save_upto: 50.0
                  cpu:
                    recommended_limit: 2.5
                    recommended_request: 1.5
                    savings: 30.0
                  memory:
                    recommended_limit: 1024
                    recommended_request: 512
                    savings: 20.0
                enable_s2z:
                  apply: true
                  save_upto: 10.0
                stop_deployment:
                  apply: false
                  save_upto: 0
        "400":
          description: Bad Request.
        "401":
          description: Unauthorized.
        "500":
          description: Internal Server Error.
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /health:
    get:
      summary: Get service health status
      description: Returns a simple health check status.
      operationId: getHealth
      responses:
        "200":
          description: Service is healthy.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
              example:
                status: healthy
        "500":
          description: Internal Server Error.
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    InstanceRatesResponse:
      type: object
      description: Detailed CPU and Memory rates for a cloud instance.
      properties:
        instanceType:
          type: string
          description: The type of cloud instance (e.g., 'm6i.large').
          example: m6i.large
        vCPUs:
          type: integer
          description: Number of virtual CPUs.
          example: 2
        memory:
          type: number
          format: float
          description: Amount of memory in GiB.
          example: 8.0
        region:
          type: string
          description: The cloud region (e.g., 'us-west-2').
          example: us-west-2
        pricePerUnitUSD:
          type: string
          description: On-demand price per hour in USD (formatted string).
          example: "0.1265"
        hourlyCPURate:
          type: string
          description: Hourly CPU rate per core (formatted string).
          example: $0.0409 (per core/ hour)
        hourlyMemoryRate:
          type: string
          description: Hourly Memory rate per byte (formatted string).
          example: $0.000000000005215406 (per byte/hour)
        cloudProvider:
          type: string
          description: The Cloud Service Provider.
          example: aws
      required:
        - cloudProvider
        - hourlyCPURate
        - hourlyMemoryRate
        - instanceType
        - memory
        - pricePerUnitUSD
        - region
        - vCPUs
    AutoOptimizePayload:
      type: object
      properties:
        status:
          type: boolean
          example: true
        environmentId:
          type: array
          example:
            - env1
            - env2
          items:
            type: string
      required:
        - environmentId
        - status
    AutoOptimizeStatus:
      type: object
      properties:
        status:
          type: boolean
          example: true
        environmentId:
          type: array
          example:
            - env1
            - env2
          items:
            type: string
      required:
        - environmentId
        - status
    AutoOptimizeDbRecord:
      type: object
      properties:
        orgId:
          type: string
          example: org123
        projectId:
          type: string
          example: proj456
        environmentId:
          type: string
          example: env1
        releaseId:
          type: string
          example: rel789
        componentId:
          type: string
          example: comp101
        status:
          type: boolean
          example: true
      required:
        - environmentId
        - orgId
        - projectId
        - status
    RecommendationApplyUpdatePayload:
      type: object
      properties:
        recommendationType:
          type: string
          description: |
            Type of recommendation to update. Allowed values: resource_right_size_savings, enable_s2z, stop_deployment.
          enum:
            - resource_right_size_savings
            - enable_s2z
            - stop_deployment
          example: resource_right_size_savings
      required:
        - recommendationType
    RecommendationApplyUpdateStatus:
      type: object
      properties:
        status:
          type: boolean
          example: true
      required:
        - status
    RecommendationsResponse:
      type: object
      properties:
        resource_right_size_savings:
          $ref: '#/components/schemas/Recommendation'
        enable_s2z:
          $ref: '#/components/schemas/DeployRecommendation'
        stop_deployment:
          $ref: '#/components/schemas/DeployRecommendation'
      required:
        - enable_s2z
        - resource_right_size_savings
        - stop_deployment
    Recommendation:
      type: object
      properties:
        apply:
          type: boolean
          example: false
        save_upto:
          type: number
          format: float
          example: 50.0
        cpu:
          $ref: '#/components/schemas/ResourceRecommendations'
        memory:
          $ref: '#/components/schemas/ResourceRecommendations'
      required:
        - apply
        - cpu
        - memory
        - save_upto
    ResourceRecommendations:
      type: object
      properties:
        recommended_limit:
          type: number
          format: float
          example: 2.5
        recommended_request:
          type: number
          format: float
          example: 1.5
        savings:
          type: number
          format: float
          example: 30.0
      required:
        - recommended_limit
        - recommended_request
        - savings
    DeployRecommendation:
      type: object
      properties:
        apply:
          type: boolean
          example: true
        save_upto:
          type: number
          format: float
          example: 10.0
      required:
        - apply
        - save_upto
    RecommendationStatResponse:
      type: object
      properties:
        envStats:
          type: array
          items:
            $ref: '#/components/schemas/ProjectEnvStats'
      required:
        - envStats
    RecommendationStatResponseWithComponent:
      type: object
      properties:
        envStats:
          type: array
          items:
            $ref: '#/components/schemas/ProjectEnvStatsWithComponent'
      required:
        - envStats
    ProjectEnvStats:
      type: object
      properties:
        environmentId:
          type: string
          example: env1
        projectStats:
          type: array
          items:
            $ref: '#/components/schemas/ProjectRecommendationStat'
      required:
        - environmentId
        - projectStats
    ProjectEnvStatsWithComponent:
      type: object
      properties:
        environmentId:
          type: string
          example: env1
        projectStats:
          type: array
          items:
            $ref: '#/components/schemas/ProjectRecommendationStatWitComponent'
      required:
        - environmentId
        - projectStats
    ProjectRecommendationStat:
      type: object
      properties:
        projectId:
          type: string
          example: proj456
        recommendationStats:
          $ref: '#/components/schemas/RecommendationStats'
        deploymentStats:
          $ref: '#/components/schemas/DeploymentStats'
      required:
        - deploymentStats
        - projectId
        - recommendationStats
    ProjectRecommendationStatWitComponent:
      type: object
      properties:
        projectId:
          type: string
          example: proj456
        componentStats:
          type: array
          items:
            $ref: '#/components/schemas/ComponentRecommendationStat'
      required:
        - componentStats
        - projectId
    ComponentRecommendationStat:
      type: object
      properties:
        componentId:
          type: string
          example: comp101
        recommendationStats:
          $ref: '#/components/schemas/RecommendationStats'
        deploymentStats:
          $ref: '#/components/schemas/DeploymentStats'
      required:
        - componentId
        - deploymentStats
        - recommendationStats
    RecommendationStats:
      type: object
      properties:
        overProvisionedCount:
          type: integer
          format: int64
          example: 5
        underProvisionedCount:
          type: integer
          format: int64
          example: 2
        idleCount:
          type: integer
          format: int64
          example: 1
      required:
        - idleCount
        - overProvisionedCount
        - underProvisionedCount
    DeploymentStats:
      type: object
      properties:
        autoOptimizedCount:
          type: integer
          format: int64
          example: 3
        requiredAttentionCount:
          type: integer
          format: int64
          example: 1
      required:
        - autoOptimizedCount
        - requiredAttentionCount
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://cost-optimizer-cp.choreo-ai:8080/
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://cost-optimizer-cp.choreo-ai:8080/
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/cost-optimizer-cp/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
