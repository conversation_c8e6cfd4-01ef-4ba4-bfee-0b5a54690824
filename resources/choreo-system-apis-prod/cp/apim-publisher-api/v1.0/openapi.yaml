openapi: 3.0.1
info:
  title: APIM Publisher API
  description: |
    This document specifies a **RESTful API** for WSO2 **API Manager** - **Publisher**.

    # Authentication
    The Publisher REST API is protected using OAuth2 and access control is achieved through scopes. Before you start invoking
    the the API you need to obtain an access token with the required scopes. This guide will walk you through the steps
    that you will need to follow to obtain an access token.
    First you need to obtain the consumer key/secret key pair by calling the dynamic client registration (DCR) endpoint. You can add your preferred grant types
    in the payload. A Sample payload is shown below.
    ```
      {
      "callbackUrl":"www.google.lk",
      "clientName":"rest_api_publisher",
      "owner":"admin",
      "grantType":"client_credentials password refresh_token",
      "saasApp":true
      }
    ```
    Create a file (payload.json) with the above sample payload, and use the cURL shown bellow to invoke the DCR endpoint. Authorization header of this should contain the
    base64 encoded admin username and password.
    **Format of the request**
    ```
      curl -X POST -H "Authorization: Basic Base64(admin_username:admin_password)" -H "Content-Type: application/json"
      \ -d @payload.json https://<host>:<servlet_port>/client-registration/v0.17/register
    ```
    **Sample request**
    ```
      curl -X POST -H "Authorization: Basic YWRtaW46YWRtaW4=" -H "Content-Type: application/json"
      \ -d @payload.json https://localhost:9443/client-registration/v0.17/register
    ```
    Following is a sample response after invoking the above curl.
    ```
    {
    "clientId": "fOCi4vNJ59PpHucC2CAYfYuADdMa",
    "clientName": "rest_api_publisher",
    "callBackURL": "www.google.lk",
    "clientSecret": "a4FwHlq0iCIKVs2MPIIDnepZnYMa",
    "isSaasApplication": true,
    "appOwner": "admin",
    "jsonString": "{\"grant_types\":\"client_credentials password refresh_token\",\"redirect_uris\":\"www.google.lk\",\"client_name\":\"rest_api123\"}",
    "jsonAppAttribute": "{}",
    "tokenType": null
    }
    ```
    Next you must use the above client id and secret to obtain the access token.
    We will be using the password grant type for this, you can use any grant type you desire.
    You also need to add the proper **scope** when getting the access token. All possible scopes for publisher REST API can be viewed in **OAuth2 Security** section
    of this document and scope for each resource is given in **authorization** section of resource documentation.
    Following is the format of the request if you are using the password grant type.
    ```
    curl -k -d "grant_type=password&username=<admin_username>&password=<admin_passowrd&scope=<scopes seperated by space>"
    \ -H "Authorization: Basic base64(cliet_id:client_secret)"
    \ https://<host>:<servlet_port>/oauth2/token
    ```
    **Sample request**
    ```
    curl https://localhost:9443/oauth2/token -k \
    -H "Authorization: Basic Zk9DaTR2Tko1OVBwSHVjQzJDQVlmWXVBRGRNYTphNEZ3SGxxMGlDSUtWczJNUElJRG5lcFpuWU1h" \
    -d "grant_type=password&username=admin&password=admin&scope=apim:api_view apim:api_create"
    ```
    Shown below is a sample response to the above request.
    ```
    {
    "access_token": "e79bda48-3406-3178-acce-f6e4dbdcbb12",
    "refresh_token": "a757795d-e69f-38b8-bd85-9aded677a97c",
    "scope": "apim:api_create apim:api_view",
    "token_type": "Bearer",
    "expires_in": 3600
    }
    ```
    Now you have a valid access token, which you can use to invoke an API.
    Navigate through the API descriptions to find the required API, obtain an access token as described above and invoke the API with the authentication header.
    If you use a different authentication mechanism, this process may change.

    # Try out in Postman
    If you want to try-out the embedded postman collection with "Run in Postman" option, please follow the guidelines listed below.
    * All of the OAuth2 secured endpoints have been configured with an Authorization Bearer header with a parameterized access token. Before invoking any REST API resource make sure you run the `Register DCR Application` and `Generate Access Token` requests to fetch an access token with all required scopes.
    * Make sure you have an API Manager instance up and running.
    * Update the `basepath` parameter to match the hostname and port of the APIM instance.

    [![Run in Postman](https://run.pstmn.io/button.svg)](https://god.gw.postman.com/run-collection/17491134-edb4ef7a-1ac7-420b-8651-2d9fd5585f6c)
  contact: {}
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  version: v1.0
servers:
  - url: https://apis.wso2.com/93tu/am/publisher/v1.0
security:
  - default: []
paths:
  /apis:
    get:
      tags:
        - APIs
      summary: |
        Retrieve/Search APIs
      description: |
        This operation provides you a list of available APIs qualifying under a given search condition.

        Each retrieved API is represented with a minimal amount of attributes. If you want to get complete details of an API, you need to use **Get details of an API** operation.
      operationId: getAllAPIs
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/sortBy'
        - $ref: '#/components/parameters/sortOrder'
        - $ref: '#/components/parameters/requestedTenant'
        - name: query
          in: query
          description: |
            **Search condition**.

            You can search in attributes by using an **"<attribute>:"** modifier.

            Eg.
            "provider:wso2" will match an API if the provider of the API contains "wso2".
            "provider:"wso2"" will match an API if the provider of the API is exactly "wso2".
            "status:PUBLISHED" will match an API if the API is in PUBLISHED state.
            "display-name:PizzaShackAPI" will match an API if the display name of the API is "PizzaShackAPI".

            Also you can use combined modifiers
            Eg.
            name:pizzashack version:v1 will match an API if the name of the API is pizzashack and version is v1.

            Supported attribute modifiers are [**version, context, name, display-name, status,
            description, provider, api-category, tags, doc, contexttemplate,
            lcstate, content, type, label, enablestore, thirdparty**]

            If no advanced attribute modifier has been specified,  the API names containing
            the search term will be returned as a result.

            Please note that you need to use encoded URL (URL encoding) if you are using a client which does not support URL encoding (such as curl)
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/If-None-Match'
        - $ref: '#/components/parameters/Accept'
      responses:
        "200":
          description: |
            OK.
            List of qualifying APIs is returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:api_import_export
            - apim:api_list_view
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:api_import_export
            - apim:api_list_view
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - APIs
      summary: Create a New API
      description: |
        This operation can be used to create a new API specifying the details of the API in the payload. The new API will be in `CREATED` state.

        There is a special capability for a user who has `APIM Admin` permission such that he can create APIs on behalf of other users. For that he can to specify `"provider" : "some_other_user"` in the payload so that the API's creator will be shown as `some_other_user` in the UI.
      operationId: createAPI
      parameters:
        - name: openAPIVersion
          in: query
          description: Open api version
          required: false
          style: form
          explode: true
          schema:
            type: string
            enum:
              - v2
              - v3
            default: v3
        - $ref: '#/components/parameters/governanceCheckEnabled'
      requestBody:
        description: API object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/API'
        required: true
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created object as entity in the body.
            Location header contains URL of newly created entity.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}:
    get:
      tags:
        - APIs
      summary: Get Details of an API
      description: |
        Using this operation, you can retrieve complete details of a single API. You need to provide the Id of the API to retrive it.
      operationId: getAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/requestedTenant'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested API is returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:api_import_export
            - apim:api_product_import_export
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:api_import_export
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - APIs
      summary: Update an API
      description: |
        This operation can be used to update an existing API.
        But the properties `name`, `version`, `context`, `provider`, `state` will not be changed by this operation.
      operationId: updateAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
        - $ref: '#/components/parameters/governanceCheckEnabled'
      requestBody:
        description: API object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/API'
        required: true
      responses:
        "200":
          description: |
            OK.
            Successful response with updated API object
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - APIs
      summary: Delete an API
      description: |
        This operation can be used to delete an existing API proving the Id of the API.
      operationId: deleteAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_delete
            - apim:api_manage
            - apim:api_import_export
        - default:
            - apim:api_delete
            - apim:api_manage
            - apim:api_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/component-mapping:
    put:
      tags:
        - APIs
      summary: Update Choreo Component Info mapping of an API
      description: This operation can be used to update Choreo Component Info mapping of an existing API.
      operationId: updateComponentMapping
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        description: Choreo Component Info mapping object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChoreoComponentInfo'
        required: true
      responses:
        "200":
          description: |
            OK.
            Successful response with updated Choreo Component Info mapping object
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modified the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
      security:
        - OAuth2Security:
            - apim:admin
        - default:
            - apim:admin
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f/component-mapping"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/topics:
    put:
      tags:
        - APIs
      summary: Update Topics of an Async API
      description: This operation can be used to update topics of an existing async API.
      operationId: updateTopics
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        description: API object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TopicList'
        required: true
      responses:
        "200":
          description: |
            OK.
            Successful response with updated API object
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_import_export
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f/topics"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/reimport-service:
    put:
      tags:
        - APIs
      summary: Update the Service that is used to create the API
      description: This operation can be used to re-import the Service used to create the API
      operationId: reimportServiceFromCatalog
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            Successful response with updated API object
          headers:
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/ecb5b300-422d-4ee8-88d2-364a0a122238/reimport-service"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/swagger:
    get:
      tags:
        - APIs
      summary: Get Swagger Definition
      description: |
        This operation can be used to retrieve the swagger definition of an API.
      operationId: getAPISwagger
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested swagger document of the API is returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                type: string
                example: ""
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:api_definition_view
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:api_definition_view
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f/swagger"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - APIs
      summary: Update Swagger Definition
      description: |
        This operation can be used to update the swagger definition of an existing API. Swagger definition to be updated is passed as a form data parameter `apiDefinition`.
      operationId: updateAPISwagger
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
        - $ref: '#/components/parameters/governanceCheckEnabled'
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                apiDefinition:
                  type: string
                  description: Swagger definition of the API
                url:
                  type: string
                  description: Swagger definition URL of the API
                file:
                  type: string
                  description: Swagger definitio as a file
                  format: binary
      responses:
        "200":
          description: |
            OK.
            Successful response with updated Swagger definition
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                type: string
                example: ""
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F apiDefinition=@swagger.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/swagger"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/generate-mock-scripts:
    post:
      tags:
        - APIs
      summary: Generate Mock Response Payloads
      description: |
        This operation can be used to generate mock responses from examples of swagger definition of an API.
      operationId: generateMockScripts
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested swagger document of the API is returned with example responses
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                type: string
                example: ""
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f/generate-mock-scripts"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/generated-mock-scripts:
    get:
      tags:
        - APIs
      summary: Get Generated Mock Response Payloads
      description: |
        This operation can be used to get generated mock responses from examples of swagger definition of an API.
      operationId: getGeneratedMockScriptsOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested swagger document of the API is returned with example responses
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MockResponsePayloadList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f/generated-mock-scripts"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/resource-policies:
    get:
      tags:
        - API Resource Policies
      summary: Get the Resource Policy(inflow/outflow) Definitions
      description: |
        This operation can be used to retrieve conversion policy resource definitions of an API.
      operationId: getAPIResourcePolicies
      parameters:
        - $ref: '#/components/parameters/apiId'
        - name: resourcePath
          in: query
          description: Resource path of the resource policy definition
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: verb
          in: query
          description: HTTP verb of the resource path of the resource policy definition
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: sequenceType
          in: query
          description: sequence type of the resource policy resource definition
          required: true
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            List of resource policy definitions of the API is returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourcePolicyList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/2fd14eb8-b828-4013-b448-0739d2e76bf7/resource-policies?resourcePath=checkPhoneNumber&verb=post&sequenceType=in"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/resource-policies/{resourcePolicyId}:
    get:
      tags:
        - API Resource Policies
      summary: Get the Resource Policy(inflow/outflow) Definition for a Given Resource Identifier.
      description: |
        This operation can be used to retrieve conversion policy resource definitions of an API given the resource identifier.
      operationId: getAPIResourcePoliciesByPolicyId
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/resourcePolicyId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested resource policy definition of the API is returned for the given resource identifier.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourcePolicyInfo'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/2fd14eb8-b828-4013-b448-0739d2e76bf7/resource-policies/8efc32a4-c7f1-4bee-b860-b7566e2bc0d5"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - API Resource Policies
      summary: Update the Resource Policy(inflow/outflow) Definition for the Given Resource Identifier
      description: |
        This operation can be used to update the resource policy(inflow/outflow) definition for the given resource identifier of an existing API. resource policy definition to be updated is passed as a body parameter `content`.
      operationId: updateAPIResourcePoliciesByPolicyId
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/resourcePolicyId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        description: Content of the resource policy definition that needs to be updated
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourcePolicyInfo'
        required: true
      responses:
        "200":
          description: |
            OK.
            Successful response with updated the resource policy definition
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourcePolicyInfo'
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/2fd14eb8-b828-4013-b448-0739d2e76bf7/resource-policies/8efc32a4-c7f1-4bee-b860-b7566e2bc0d5"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/thumbnail:
    get:
      tags:
        - APIs
      summary: Get Thumbnail Image
      description: |
        This operation can be used to download a thumbnail image of an API.
      operationId: getAPIThumbnail
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Thumbnail image returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/2fd14eb8-b828-4013-b448-0739d2e76bf7/thumbnail"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - APIs
      summary: Upload a Thumbnail Image
      description: |
        This operation can be used to upload a thumbnail image of an API. The thumbnail to be uploaded should be given as a form data parameter `file`.
      operationId: updateAPIThumbnail
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - file
              properties:
                file:
                  type: string
                  description: Image to upload
                  format: binary
        required: true
      responses:
        "200":
          description: |
            OK.
            Image updated
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the uploaded thumbnail image of the API.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileInfo'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@image.jpeg "https://127.0.0.1:9443/api/am/publisher/v3/apis/2fd14eb8-b828-4013-b448-0739d2e76bf7/thumbnail"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/subscription-policies:
    get:
      tags:
        - APIs
      summary: |
        Get Details of the Subscription Throttling Policies of an API
      description: |
        This operation can be used to retrieve details of the subscription throttling policy of an API by specifying the API Id.

        `X-WSO2-Tenant` header can be used to retrive API subscription throttling policies that belongs to a different tenant domain. If not specified super tenant will be used. If Authorization header is present in the request, the user's tenant associated with the access token will be used.
      operationId: getAPISubscriptionPolicies
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/requestedTenant'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Throttling Policy returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests.
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThrottlingPolicy'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/2fd14eb8-b828-4013-b448-0739d2e76bf7/subscription-policies"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/copy-api:
    post:
      tags:
        - APIs
      summary: Create a New API Version
      description: |
        This operation can be used to create a new version of an existing API. The new version is specified as `newVersion` query parameter. New API will be in `CREATED` state.
      operationId: createNewAPIVersion
      parameters:
        - name: newVersion
          in: query
          description: Version of the new API.
          required: true
          style: form
          explode: true
          schema:
            maxLength: 30
            type: string
        - name: defaultVersion
          in: query
          description: Specifies whether new API should be added as default version.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
        - name: serviceVersion
          in: query
          description: Version of the Service that will used in creating new version
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/apiId-Q'
        - $ref: '#/components/parameters/choreoVersionId-Q-Opt'
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created API as entity in the body. Location header contains URL of newly created API.
          headers:
            Location:
              description: |
                The URL of the newly created API.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/copy-api?newVersion=2.0&defaultVersion=false&apiId=2fd14eb8-b828-4013-b448-0739d2e76bf7"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/change-lifecycle:
    post:
      tags:
        - API Lifecycle
      summary: Change API Status
      description: |
        This operation is used to change the lifecycle of an API. Eg: Publish an API which is in `CREATED` state. In order to change the lifecycle, we need to provide the lifecycle `action` as a query parameter.

        For example, to Publish an API, `action` should be `Publish`. Note that the `Re-publish` action is available only after calling `Block`.

        Some actions supports providing additional paramters which should be provided as `lifecycleChecklist` parameter. Please see parameters table for more information.
      operationId: changeAPILifecycle
      parameters:
        - name: action
          in: query
          description: |
            The action to demote or promote the state of the API.

            Supported actions are [ **Publish**, **Deploy as a Prototype**, **Demote to Created**, **Block**, **Deprecate**, **Re-Publish**, **Retire** ]
          required: true
          style: form
          explode: true
          schema:
            type: string
            enum:
              - Publish
              - Deploy as a Prototype
              - Demote to Created
              - Block
              - Deprecate
              - Re-Publish
              - Retire
        - name: lifecycleChecklist
          in: query
          description: |2
            Supported checklist items are as follows.
            1. **Deprecate old versions after publishing the API**: Setting this to true will deprecate older versions of a particular API when it is promoted to Published state from Created state.
            2. **Requires re-subscription when publishing the API**: If you set this to true, users need to re subscribe to the API although they may have subscribed to an older version.
            You can specify additional checklist items by using an **"attribute:"** modifier.
            Eg: "Deprecate old versions after publishing the API:true" will deprecate older versions of a particular API when it is promoted to Published state from Created state. Multiple checklist items can be given in "attribute1:true, attribute2:false" format.
            **Sample CURL :**  curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -X POST "https://localhost:9443/api/am/publisher/v3/apis/change-lifecycle?apiId=890a4f4d-09eb-4877-a323-57f6ce2ed79b&action=Publish&lifecycleChecklist=Deprecate%20old%20versions%20after%20publishing%20the%20API%3Atrue,Requires%20re-subscription%20when%20publishing%20the%20API%3Afalse"
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/apiId-Q'
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Lifecycle changed successfully.
          headers:
            ETag:
              description: |
                Entity Tag of the changed API. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the API lifecycle has been modified the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowResponse'
        "202":
          description: |
            Accepted.
            The request has been accepted.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
            - apim:api_import_export
        - default:
            - apim:api_publish
            - apim:api_manage
            - apim:api_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/change-lifecycle?apiId=890a4f4d-09eb-4877-a323-57f6ce2ed79b&action=Publish"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/lifecycle-history:
    get:
      tags:
        - API Lifecycle
      summary: Get Lifecycle State Change History of the API.
      description: |
        This operation can be used to retrieve Lifecycle state change history of the API.
      operationId: getAPILifecycleHistory
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Lifecycle state change history returned successfully.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LifecycleHistory'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/lifecycle-history"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/lifecycle-state:
    get:
      tags:
        - API Lifecycle
      summary: Get Lifecycle State Data of the API.
      description: |
        This operation can be used to retrieve Lifecycle state data of the API.
      operationId: getAPILifecycleState
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Lifecycle state data returned successfully.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LifecycleState'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/lifecycle-state"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/lifecycle-state/pending-tasks:
    delete:
      tags:
        - API Lifecycle
      summary: Delete Pending Lifecycle State Change Tasks
      description: |
        This operation can be used to remove pending lifecycle state change requests that are in pending state
      operationId: deleteAPILifecycleStatePendingTasks
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            Lifecycle state change pending task removed successfully.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/lifecycle-state/pending-tasks"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/revisions:
    get:
      tags:
        - API Revisions
      summary: List Revisions
      description: |
        List available revisions of an API
      operationId: getAPIRevisions
      parameters:
        - $ref: '#/components/parameters/apiId'
        - name: query
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            List of API revisions are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevisionList'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
            - apim:api_import_export
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
            - apim:api_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/revisions?query=deployed:true"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - API Revisions
      summary: Create API Revision
      description: |
        Create a new API revision
      operationId: createAPIRevision
      parameters:
        - $ref: '#/components/parameters/apiId'
      requestBody:
        description: API object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIRevision'
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created APIRevision object as the entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevision'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
            - apim:api_import_export
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
            - apim:api_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/revisions"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/revisions/{revisionId}:
    get:
      tags:
        - API Revisions
      summary: Retrieve Revision
      description: |
        Retrieve a revision of an API (This resource is not supported in the current release)
      operationId: getAPIRevision
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/revisionId'
      responses:
        "200":
          description: |
            OK.
            An API revision is returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevision'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/revisions/e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - API Revisions
      summary: Delete Revision
      description: |
        Delete a revision of an API
      operationId: deleteAPIRevision
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/revisionId'
      responses:
        "200":
          description: |
            OK.
            List of remaining API revisions are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevisionList'
        "204":
          description: |
            No Content.
            Successfully deleted the revision
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
            - apim:api_import_export
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
            - apim:api_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/revisions/e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/deployments:
    get:
      tags:
        - API Revisions
      summary: List Deployments
      description: |
        List available deployed revision deployment details of an API
      operationId: getAPIRevisionDeployments
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            List of deployed revision deployment details are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevisionDeploymentList'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/deployments"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/deployments/{deploymentId}:
    put:
      tags:
        - API Revisions
      summary: Update Deployment
      description: |
        Update deployment devportal visibility
      operationId: updateAPIDeployment
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/deploymentId'
      requestBody:
        description: Deployment object that needs to be updated
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIRevisionDeployment'
      responses:
        "200":
          description: |
            Created.
            Successful response with the newly updated APIRevisionDeployment List object as the entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevisionDeployment'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/deployments/UHJvZHVjdGlvbiBhbmQgU2FuZGJveA"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/undeploy:
    post:
      tags:
        - API Revisions
      summary: Undeploy API from gateway
      description: |
        Un-Deploy the deployment of API from the specified gateway environments
      operationId: unDeployAPIFromGateway
      parameters:
        - $ref: '#/components/parameters/apiId'
      requestBody:
        description: Deployment object that needs to be added
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/APIUndeployRedeploy'
      responses:
        "200":
          description: |
            Created.
            Successful response with the newly deployed APIRevisionDeployment List object as the entity in the body.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/APIRevisionDeployment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/redeploy"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/redeploy:
    post:
      tags:
        - API Revisions
      summary: Redeploy API into gateway
      description: |
        Deploy a revision
      operationId: reDeployAPIIntoGateway
      parameters:
        - $ref: '#/components/parameters/apiId'
      requestBody:
        description: Deployment object that needs to be added
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/APIUndeployRedeploy'
      responses:
        "200":
          description: |
            Created.
            Successful response with the newly deployed APIRevisionDeployment List object as the entity in the body.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/APIRevisionDeployment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/redeploy"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/deploy-revision:
    post:
      tags:
        - API Revisions
      summary: Deploy Revision
      description: |
        Deploy a revision
      operationId: deployAPIRevision
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/revisionId-Q'
      requestBody:
        description: Deployment object that needs to be added
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/APIRevisionDeployment'
      responses:
        "200":
          description: |
            Created.
            Successful response with the newly deployed APIRevisionDeployment List object as the entity in the body.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/APIRevisionDeployment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/deploy-revision?revisionId=e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/undeploy-revision:
    post:
      tags:
        - API Revisions
      summary: UnDeploy Revision
      description: |
        UnDeploy a revision
      operationId: undeployAPIRevision
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/revisionId-Q'
        - $ref: '#/components/parameters/revisionNum-Q'
        - name: allEnvironments
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      requestBody:
        description: Deployment object that needs to be added
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/APIRevisionDeployment'
      responses:
        "200":
          description: |
            OK.
        "201":
          description: |
            Created.
            Successful response with the newly undeployed APIRevisionDeploymentList object as the entity in the body.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/APIRevisionDeployment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
            - apim:api_import_export
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
            - apim:api_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/undeploy-revision?revisionId=e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/restore-revision:
    post:
      tags:
        - API Revisions
      summary: Restore API Revision
      description: |
        Restore a revision to the current API of the API
      operationId: restoreAPIRevision
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/revisionId-Q'
      responses:
        "201":
          description: |
            Restored.
            Successful response with the newly restored API object as the entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/890a4f4d-09eb-4877-a323-57f6ce2ed79b/restore-revision?revisionId=e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/import-service:
    post:
      tags:
        - APIs
      summary: Import a Service from Service Catalog
      description: This operation can be used to create an API from a Service from Service Catalog
      operationId: importServiceFromCatalog
      parameters:
        - name: serviceKey
          in: query
          description: ID of service that should be imported from Service Catalog
          required: true
          style: form
          explode: true
          schema:
            type: string
          example: Pizzashack-1.0.0
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/API'
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created object as entity in the body.
            Location header contains the URL of the newly created entity.
          headers:
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/import-service?serviceKey=Pizzashack-1.0.0"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/comments:
    get:
      tags:
        - Comments
      summary: Retrieve API Comments
      description: |
        Get a list of Comments that are already added to APIs
      operationId: getAllCommentsOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/requestedTenant'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/includeCommenterInfo'
      responses:
        "200":
          description: |
            OK.
            Comments list is returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommentList'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:comment_view
            - apim:comment_manage
        - default:
            - apim:api_view
            - apim:comment_view
            - apim:comment_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://localhost:9443/api/am/publisher/v3/apis/e93fb282-b456-48fc-8981-003fb89086ae/comments"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - Comments
      summary: Add an API Comment
      operationId: addCommentToAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/parentCommentID'
      requestBody:
        description: |
          Comment object that should to be added
        content:
          application/json:
            schema:
              title: Post request body
              required:
                - content
              type: object
              properties:
                content:
                  maxLength: 512
                  type: string
                  description: |
                    Content of the comment
                  example: This is a comment
                category:
                  type: string
                  description: |
                    Category of the comment
                  example: general
        required: true
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created object as entity in the body.
            Location header contains URL of newly created entity.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional request.
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                Location to the newly created Comment.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Comment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "404":
          $ref: '#/components/responses/NotFound'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:comment_write
            - apim:comment_manage
        - default:
            - apim:comment_write
            - apim:comment_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization:Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://localhost:9443/api/am/publisher/v3/apis/e93fb282-b456-48fc-8981-003fb89086ae/comments"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/comments/{commentId}:
    get:
      tags:
        - Comments
      summary: Get Details of an API Comment
      description: |
        Get the individual comment given by a username for a certain API.
      operationId: getCommentOfAPI
      parameters:
        - $ref: '#/components/parameters/commentId'
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/requestedTenant'
        - $ref: '#/components/parameters/If-None-Match'
        - $ref: '#/components/parameters/includeCommenterInfo'
        - $ref: '#/components/parameters/replyLimit'
        - $ref: '#/components/parameters/replyOffset'
      responses:
        "200":
          description: |
            OK.
            Comment returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests.
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Comment'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:comment_view
            - apim:comment_manage
        - default:
            - apim:api_view
            - apim:comment_view
            - apim:comment_manage
      x-code-samples:
        - lang: Curl
          source: curl -k -H "Authorization:Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://localhost:9443/api/am/publisher/v3/apis/e93fb282-b456-48fc-8981-003fb89086ae/comments/d4cf1704-5d09-491c-bc48-4d19ce6ea9b4"
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - Comments
      summary: Delete an API Comment
      description: |
        Remove a Comment
      operationId: deleteComment
      parameters:
        - $ref: '#/components/parameters/commentId'
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "405":
          description: |
            MethodNotAllowed.
            Request method is known by the server but is not supported by the target resource.
          content: {}
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:comment_write
            - apim:comment_manage
            - apim:admin
        - default:
            - apim:comment_write
            - apim:comment_manage
            - apim:admin
      x-code-samples:
        - lang: Curl
          source: curl -k -X DELETE -H "Authorization:Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://localhost:9443/api/am/publisher/v3/apis/e93fb282-b456-48fc-8981-003fb89086ae/comments/d4cf1704-5d09-491c-bc48-4d19ce6ea9b4"
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    patch:
      tags:
        - Comments
      summary: Edit a comment
      description: |
        Edit the individual comment
      operationId: editCommentOfAPI
      parameters:
        - $ref: '#/components/parameters/commentId'
        - $ref: '#/components/parameters/apiId'
      requestBody:
        description: |
          Comment object that should to be updated
        content:
          application/json:
            schema:
              title: Patch request body
              type: object
              properties:
                content:
                  maxLength: 512
                  type: string
                  description: |
                    Content of the comment
                  example: This is a comment
                category:
                  type: string
                  description: |
                    Category of the comment
                  example: general
        required: true
      responses:
        "200":
          description: |
            OK.
            Comment updated.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional request.
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                Location to the newly created Comment.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Comment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:comment_write
            - apim:comment_manage
        - default:
            - apim:comment_write
            - apim:comment_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PATCH -H "Authorization:Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://localhost:9443/api/am/publisher/v3/apis/e93fb282-b456-48fc-8981-003fb89086ae/comments/d4cf1704-5d09-491c-bc48-4d19ce6ea9b4"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/comments/{commentId}/replies:
    get:
      tags:
        - Comments
      summary: Get replies of a comment
      description: |
        Get replies of a comment
      operationId: getRepliesOfComment
      parameters:
        - $ref: '#/components/parameters/commentId'
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/requestedTenant'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/If-None-Match'
        - $ref: '#/components/parameters/includeCommenterInfo'
      responses:
        "200":
          description: |
            OK.
            Comment returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests.
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommentList'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:comment_view
            - apim:comment_manage
        - default:
            - apim:api_view
            - apim:comment_view
            - apim:comment_manage
      x-code-samples:
        - lang: Curl
          source: curl -k -H "Authorization:Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://localhost:9443/api/am/publisher/v3/apis/e93fb282-b456-48fc-8981-003fb89086ae/comments/d4cf1704-5d09-491c-bc48-4d19ce6ea9b4/replies"
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/import-openapi:
    post:
      tags:
        - APIs
      summary: Import an OpenAPI Definition
      description: |
        This operation can be used to create an API from an OpenAPI definition. Provide either `url` or `file`
        to specify the definition.

        Specify additionalProperties with **at least** API's name, version, context and endpointConfig.
      operationId: importOpenAPIDefinition
      parameters:
        - name: prefixScopesWith
          in: query
          description: Specify a prefix for the scopes.
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/governanceCheckEnabled'
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  description: Definition to upload as a file
                  format: binary
                url:
                  type: string
                  description: Definition url
                additionalProperties:
                  type: string
                  description: Additional attributes specified as a stringified JSON with API's schema
                inlineAPIDefinition:
                  type: string
                  description: Inline content of the OpenAPI definition
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created object as entity in the body.
            Location header contains URL of newly created entity.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@openapi.json -F additionalProperties=@data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/import-openapi"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/import-wsdl:
    post:
      tags:
        - APIs
      summary: Import a WSDL Definition
      description: |
        This operation can be used to create an API using a WSDL definition. Provide either `url` or `file`
        to specify the definition.

        WSDL can be speficied as a single file or a ZIP archive with WSDLs and reference XSDs etc.
        Specify additionalProperties with **at least** API's name, version, context and endpointConfig.
      operationId: importWSDLDefinition
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  description: |
                    WSDL definition as a file or archive

                    **Sample cURL to Upload WSDL File**

                    curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@api.wsdl -F additionalProperties=@data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/import-wsdl"

                    **Sample cURL to Upload WSDL Archive**

                    curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file="@wsdl.zip;type=application/zip" -F additionalProperties=@data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/import-wsdl"
                  format: binary
                url:
                  type: string
                  description: WSDL Definition url
                additionalProperties:
                  type: string
                  description: Additional attributes specified as a stringified JSON with API's schema
                implementationType:
                  type: string
                  description: |
                    If 'SOAP' is specified, the API will be created with only one resource 'POST /*' which is to be used for SOAP
                    operations.

                    If 'HTTP_BINDING' is specified, the API will be created with resources using HTTP binding operations
                    which are extracted from the WSDL.
                  enum:
                    - SOAPTOREST
                    - SOAP
                  default: SOAP
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created object as entity in the body.
            Location header contains URL of newly created entity.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@api.wsdl -F additionalProperties=@data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/import-wsdl"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/import-graphql-schema:
    post:
      tags:
        - APIs
      summary: Import a GraphQL SDL
      description: |
        This operation can be used to create api from api definition.APIMgtDAOTest

        API definition is GraphQL Schema
      operationId: importGraphQLSchema
      parameters:
        - name: If-Match
          in: header
          description: |
            Validator for conditional requests; based on ETag.
          required: false
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                type:
                  type: string
                  description: Definition type to upload
                file:
                  type: string
                  description: Definition to uploads a file
                  format: binary
                additionalProperties:
                  type: string
                  description: Additional attributes specified as a stringified JSON with API's schema
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created object as entity in the body.
            Location header contains URL of newly created entity.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@schema.graphql -F additionalProperties=@data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/import-graphql-schema"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/validate-openapi:
    post:
      tags:
        - Validation
      summary: Validate an OpenAPI Definition
      description: |
        This operation can be used to validate an OpenAPI definition and retrieve a summary. Provide either `url`
        or `file` to specify the definition.
      operationId: validateOpenAPIDefinition
      parameters:
        - name: returnContent
          in: query
          description: |
            Specify whether to return the full content of the OpenAPI definition in the response. This is only
            applicable when using url based validation
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                url:
                  type: string
                  description: OpenAPI definition url
                file:
                  type: string
                  description: OpenAPI definition as a file
                  format: binary
                inlineAPIDefinition:
                  type: string
                  description: Inline content of the OpenAPI definition
      responses:
        "200":
          description: |
            OK.
            API definition validation information is returned
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OpenAPIDefinitionValidationResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@openapi.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/validate-openapi"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/validate-endpoint:
    post:
      tags:
        - Validation
      summary: Check Whether Given Endpoint URL is Valid
      description: |
        Using this operation, it is possible check whether the given API endpoint url is a valid url
      operationId: validateEndpoint
      parameters:
        - name: endpointUrl
          in: query
          description: API endpoint url
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: apiId
          in: query
          description: API ID consisting of the UUID of the API
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            API definition validation information is returned
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiEndpointValidationResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/validate-endpoint?apiId=e0824883-3e86-403a-aec1-22bbc454eb7c&endpointUrl=https%3A%2F%2Flocalhost%3A9443%2Fam%2Fsample%2Fpizzashack%2Fv1%2Fapi%2F"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/validate:
    post:
      tags:
        - Validation
      summary: Check Given API Context Name already Exists
      description: |
        Using this operation, you can check a given API context is already used. You need to provide the context name you want to check.
      operationId: validateAPI
      parameters:
        - name: query
          in: query
          description: |
            **Search condition**.

            You can search in attributes by using an **"<attribute>:"** modifier.

            Eg.
            "name:wso2" will match an API if the provider of the API is exactly "wso2".

            Supported attribute modifiers are [** version, context, name **]

            If no advanced attribute modifier has been specified, search will match the
            given query string against API Name.
          required: true
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            API definition validation information is returned
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      deprecated: true
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/validate?query=name%3Awso2"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/validate-api:
    post:
      tags:
        - Validation
      summary: Check whether an API with the given details already exists
      description: |
        Using this operation, you can check whether an API with the given details already exists.
      operationId: checkApiExistence
      requestBody:
        description: Object that needs to be validated
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIValidationRequest'
        required: true
      responses:
        "200":
          description: |
            OK.
            API validation information is returned
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiValidationResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/validate-api"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/validate-scope:
    post:
      tags:
        - Validation
      summary: Check Whether Given Scope Already Exists
      description: |
        Using this operation, you can check a given scope is already used. You need to provide the scope name you want to check.
      operationId: validateScopeExistence
      parameters:
        - name: scope
          in: query
          description: |
            **Search condition**.
            You can search in attributes by using an **"<attribute>:"** modifier.
            Eg.
            "scope:read" will match to a scope if the scope name is exactly "read".
            If no advanced attribute modifier has been specified, search will match the
            given query string against scope.
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: apiName
          in: query
          description: Name of the API.
          required: true
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Scope validation information is returned
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/validate-scope?query=scope%3Aread"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/validate-wsdl:
    post:
      tags:
        - Validation
      summary: Validate a WSDL Definition
      description: |
        This operation can be used to validate a WSDL definition and retrieve a summary. Provide either `url`
        or `file` to specify the definition.
      operationId: validateWSDLDefinition
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                url:
                  type: string
                  description: Definition url
                file:
                  type: string
                  description: Definition to upload as a file
                  format: binary
      responses:
        "200":
          description: |
            OK.
            API definition validation information is returned
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WSDLValidationResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@api.wsdl "https://127.0.0.1:9443/api/am/publisher/v3/apis/validate-wsdl"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/validate-graphql-schema:
    post:
      tags:
        - Validation
      summary: Validate a GraphQL SDL
      description: |
        This operation can be used to validate a graphQL definition and retrieve a summary.
      operationId: validateGraphQLSchema
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - file
              properties:
                file:
                  type: string
                  description: Definition to upload as a file
                  format: binary
        required: true
      responses:
        "200":
          description: |
            OK.
            API definition validation information is returned
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GraphQLValidationResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@schema.graphql "https://127.0.0.1:9443/api/am/publisher/v3/apis/validate-graphql-schema"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/graphql-schema:
    get:
      tags:
        - GraphQL Schema (Individual)
      summary: Get the Schema of a GraphQL API
      description: |
        This operation can be used to retrieve the Schema definition of a GraphQL API.
      operationId: getAPIGraphQLSchema
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested GraphQL Schema DTO object belongs to the API
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GraphQLSchema'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/e0824883-3e86-403a-aec1-22bbc454eb7c/graphql-schema"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - GraphQL Schema
      summary: Add a Schema to a GraphQL API
      description: |
        This operation can be used to add a GraphQL Schema definition to an existing GraphQL API.
      operationId: updateAPIGraphQLSchema
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - schemaDefinition
              properties:
                schemaDefinition:
                  type: string
                  description: schema definition of the GraphQL API
        required: true
      responses:
        "200":
          description: |
            OK.
            Successful response with updated schema definition
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by cache, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F schemaDefinition=@schema.graphql "https://127.0.0.1:9443/api/am/publisher/v3/apis/e0824883-3e86-403a-aec1-22bbc454eb7c/graphql-schema"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/amznResourceNames:
    get:
      tags:
        - AWS Lambda (Individual)
      summary: Retrieve the ARNs of AWS Lambda Functions
      description: |
        This operation can be use to retrieve ARNs of AWS Lambda function for a given AWS credentials.
      operationId: getAmazonResourceNamesOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            Requested ARN List of the API is returned
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                type: string
                example: |-
                  {
                     "count": "2",
                     "list": [
                        "arn:aws:lambda:us-west-2:123456789012:function:my-function1",
                        "arn:aws:lambda:us-west-2:123456789012:function:my-function2"
                     ]
                  }
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/e0824883-3e86-403a-aec1-22bbc454eb7c/amznResourceNames"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/monetize:
    post:
      tags:
        - API Monetization
      summary: Configure Monetization for a Given API
      description: |
        This operation can be used to configure monetization for a given API.
      operationId: addAPIMonetization
      parameters:
        - $ref: '#/components/parameters/apiId'
      requestBody:
        description: Monetization data object
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIMonetizationInfo'
        required: true
      responses:
        "201":
          description: |
            OK.
            Monetization status changed successfully.
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/monetize'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/monetization:
    get:
      tags:
        - API Monetization
      summary: Get Monetization Status for each Tier in a Given API
      description: |
        This operation can be used to get monetization status for each tier in a given API
      operationId: getAPIMonetization
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            Monetization status for each tier returned successfully.
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/monetization"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/revenue:
    get:
      tags:
        - API Monetization
      summary: Get Total Revenue Details of a Given Monetized API with Meterd Billing
      description: |
        This operation can be used to get details of total revenue details of a given monetized API with meterd billing.
      operationId: getAPIRevenue
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            Details of a total revenue returned.
          headers:
            ETag:
              description: Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: Date and time the resource has been modified the last time. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevenue'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/revenue"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/documents:
    get:
      tags:
        - API Documents
      summary: Get a List of Documents of an API
      description: |
        This operation can be used to retrieve a list of documents belonging to an API by providing the id of the API.
      operationId: getAPIDocuments
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Document list is returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:document_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:document_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/e0824883-3e86-403a-aec1-22bbc454eb7c/documents"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - API Documents
      summary: Add a New Document to an API
      description: |
        This operation can be used to add a new documentation to an API. This operation only adds the metadata of a document. To add the actual content we need to use **Upload the content of an API document ** API once we obtain a document Id by this operation.
      operationId: addAPIDocument
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        description: Document object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
        required: true
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created Document object as entity in the body.
            Location header contains URL of newly added document.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                Location to the newly created Document.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:document_create
            - apim:document_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:document_create
            - apim:document_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/documents"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/documents/{documentId}:
    get:
      tags:
        - API Documents
      summary: Get a Document of an API
      description: |
        This operation can be used to retrieve a particular document's metadata associated with an API.
      operationId: getAPIDocumentByDocumentId
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Document returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:document_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:document_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/documents/0bcb7f05-599d-4e1a-adce-5cb89bfe58d5"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - API Documents
      summary: Update a Document of an API
      description: |
        This operation can be used to update metadata of an API's document.
      operationId: updateAPIDocument
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        description: Document object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
        required: true
      responses:
        "200":
          description: |
            OK.
            Document updated
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the updated document.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:document_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:document_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @doc.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/documents/0bcb7f05-599d-4e1a-adce-5cb89bfe58d5"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - API Documents
      summary: Delete a Document of an API
      description: |
        This operation can be used to delete a document associated with an API.
      operationId: deleteAPIDocument
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:document_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:document_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/documents/0bcb7f05-599d-4e1a-adce-5cb89bfe58d5"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/documents/{documentId}/content:
    get:
      tags:
        - API Documents
      summary: Get the Content of an API Document
      description: |
        This operation can be used to retrive the content of an API's document.

        The document can be of 3 types. In each cases responses are different.

        1. **Inline type**:
           The content of the document will be retrieved in `text/plain` content type

           _Sample cURL_ : `curl -k -H "Authorization:Bearer 579f0af4-37be-35c7-81a4-f1f1e9ee7c51" -F inlineContent=@"docs.txt" -X POST "https://localhost:9443/api/am/publisher/v3/apis/995a4972-3178-4b17-a374-756e0e19127c/documents/43c2bcce-60e7-405f-bc36-e39c0c5e189e/content`
        2. **FILE type**:
           The file will be downloaded with the related content type (eg. `application/pdf`)
        3. **URL type**:
            The client will recieve the URL of the document as the Location header with the response with - `303 See Other`
      operationId: getAPIDocumentContentByDocumentId
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            File or inline content returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/octet-stream:
              schema:
                type: string
        "303":
          description: |
            See Other.
            Source can be retrived from the URL specified at the Location header.
          headers:
            Location:
              description: |
                The Source URL of the document.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:document_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:document_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/documents/0bcb7f05-599d-4e1a-adce-5cb89bfe58d5/content"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - API Documents
      summary: Upload the Content of an API Document
      description: |
        Thid operation can be used to upload a file or add inline content to an API document.

        **IMPORTANT:**
        * Either **file** or **inlineContent** form data parameters should be specified at one time.
        * Document's source type should be **FILE** in order to upload a file to the document using **file** parameter.
        * Document's source type should be **INLINE** in order to add inline content to the document using **inlineContent** parameter.
      operationId: addAPIDocumentContent
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  description: Document to upload
                  format: binary
                inlineContent:
                  type: string
                  description: Inline content of the document
      responses:
        "200":
          description: |
            OK.
            Document updated
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the updated content of the document.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:document_create
            - apim:document_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:document_create
            - apim:document_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@sample.pdf "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/documents/0bcb7f05-599d-4e1a-adce-5cb89bfe58d5/content"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/documents/validate:
    post:
      tags:
        - API Documents
      summary: Check Whether a Document with the Provided Name Exist
      description: |
        This operation can be used to verify the document name exists or not.
      operationId: validateDocument
      parameters:
        - $ref: '#/components/parameters/apiId'
        - name: name
          in: query
          description: |
            The name of the document which needs to be checked for the existance.
          required: true
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Successful response if the document name exists.
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          description: |
            Not Found.
            The specified resource does not exist.
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:document_manage
            - apim:document_create
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:document_manage
            - apim:document_create
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/documents/validate?name=CalculatorDoc"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/wsdl-info:
    get:
      tags:
        - APIs
      summary: Get WSDL Meta Information
      description: |
        This operation can be used to retrieve the WSDL meta information of an API. It states whether the API is a SOAP
        API. If the API is a SOAP API, it states whether it has a single WSDL or a WSDL archive.
      operationId: getWSDLInfoOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            Requested WSDL meta information of the API is returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WSDLInfo'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/wsdl-info"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/wsdl:
    get:
      tags:
        - APIs
      summary: Get WSDL definition
      description: |
        This operation can be used to retrieve the WSDL definition of an API. It can be either a single WSDL file or a WSDL archive.

        The type of the WSDL of the API is indicated at the "wsdlInfo" element of the API payload definition.
      operationId: getWSDLOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested WSDL document of the API is returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/wsdl"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - APIs
      summary: Update WSDL Definition
      description: |
        This operation can be used to update the WSDL definition of an existing API. WSDL to be updated can be passed as either "url" or "file".
        Only one of "url" or "file" can be used at the same time. "file" can be specified as a single WSDL file or as a zip file which has a WSDL
        and its dependencies (eg: XSDs)
      operationId: updateWSDLOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  description: WSDL file or archive to upload
                  format: binary
                url:
                  type: string
                  description: WSDL Definition url
      responses:
        "200":
          description: |
            OK.
            Successful response with updated WSDL definition
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (This is not supported by WSO2 API Manager as of yet).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (This is not supported by WSO2 API Manager as of yet).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@api.wsdl "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/wsdl"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/graphql-policies/complexity:
    get:
      tags:
        - GraphQL Policies
      summary: Get the Complexity-Related Details of an API
      description: |
        This operation can be used to retrieve complexity-related details belonging to an API by providing the API ID.
      operationId: getGraphQLPolicyComplexityOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            Requested complexity details returned.
          headers:
            Content-Type:
              description: |
                The content of the body.
              style: simple
              explode: false
              schema:
                type: string
                default: application/json
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GraphQLQueryComplexityInfo'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/graphql-policies/complexity"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - GraphQL Policies
      summary: Update Complexity-Related Details of an API
      description: |
        This operation can be used to update complexity-related details belonging to an API by providing the API ID.
      operationId: updateGraphQLPolicyComplexityOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
      requestBody:
        description: Role-depth mapping that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GraphQLQueryComplexityInfo'
      responses:
        "200":
          description: |
            Created.
            Complexity details created successfully.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/graphql-policies/complexity"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/graphql-policies/complexity/types:
    get:
      tags:
        - GraphQL Policies
      summary: Retrieve Types and Fields of a GraphQL Schema
      description: |
        This operation can be used to retrieve all types and fields of the GraphQL Schema by providing the API ID.
      operationId: getGraphQLPolicyComplexityTypesOfAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            Types and fields returned successfully.
          headers:
            Content-Type:
              description: |
                The content of the body.
              style: simple
              explode: false
              schema:
                type: string
                default: application/json
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GraphQLSchemaTypeList'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/graphql-policies/complexity/types"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/resource-paths:
    get:
      tags:
        - APIs
      summary: Get Resource Paths of an API
      description: |
        This operation can be used to retrieve resource paths defined for a specific API.
      operationId: getAPIResourcePaths
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            ResourcePaths returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modified the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourcePathList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/resource-paths"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/auditapi:
    get:
      tags:
        - API Audit
      summary: Retrieve the Security Audit Report of the Audit API
      description: |
        Retrieve the Security Audit Report of the Audit API
      operationId: getAuditReportOfAPI
      parameters:
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            The Security Audit Report has been returned.
          headers:
            Content-Type:
              description: |
                The content of the body.
              style: simple
              explode: false
              schema:
                type: string
                default: application/json
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditReport'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/auditapi"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/external-stores:
    get:
      tags:
        - External Stores
      summary: Get the List of External Stores to which an API is Published
      description: |
        This operation can be used to retrieve a list of external stores which an API is published to by providing the ID of the API.
      operationId: getAllPublishedExternalStoresByAPI
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            External Store list is returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIExternalStoreList'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/external-stores"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/publish-to-external-stores:
    post:
      tags:
        - External Stores
      summary: Publish an API to External Stores
      description: |
        This operation can be used to publish an API to a list of external stores.
      operationId: publishAPIToExternalStores
      parameters:
        - $ref: '#/components/parameters/apiId'
        - name: externalStoreIds
          in: query
          description: External Store Ids of stores which the API needs to be published or updated.
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            API was successfully published to all the selected external stores.
          headers:
            ETag:
              description: |
                Entity Tag of the blocked subscription.
                Used by caches, or in conditional requests (This is not supported by WSO2 API Manager as of yet).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the subscription which was blocked.
                Used by caches, or in conditional requests (This is not supported by WSO2 API Manager as of yet).
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIExternalStoreList'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/publish-to-external-stores?externalStoreId=Store123#"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/generate-key:
    post:
      tags:
        - APIs
      summary: Generate internal API Key to invoke APIS.
      description: |
        This operation can be used to generate internal API key which used to invoke API.
      operationId: generateInternalAPIKey
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/keyType'
      responses:
        "200":
          description: |
            OK.
            apikey generated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIKey'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_generate_key
            - apim:api_manage
        - default:
            - apim:api_generate_key
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f/generate-key?keyType=production"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/revoke-key:
    post:
      tags:
        - API Keys
      summary: Revokes an API Test Token
      description: |
        This operation can be used to revoke an internal API key which was used to invoke an API.
      operationId: revokeInternalAPIKey
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        description: |
          API Key Revoke Request Object
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIKeyRevokeRequest'
        required: false
      responses:
        "200":
          description: |
            OK.
            apiKey revoked successfully.
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
      security:
        - OAuth2Security:
            - apim:api_generate_key
            - apim:api_manage
        - default:
            - apim:api_generate_key
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "-H Content-Type: application/json" -X POST @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/7a2298c4-c905-403f-8fac-38c73301631f/revoke-keys"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/export:
    get:
      tags:
        - Import Export
      summary: Export an API
      description: |
        This operation can be used to export the details of a particular API as a zip file.
      operationId: exportAPI
      parameters:
        - name: apiId
          in: query
          description: UUID of the API
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: name
          in: query
          description: |
            API Name
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: version
          in: query
          description: |
            Version of the API
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: revisionNumber
          in: query
          description: |
            Revision number of the API artifact
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: providerName
          in: query
          description: |
            Provider name of the API
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: format
          in: query
          description: |
            Format of output documents. Can be YAML or JSON.
          required: false
          style: form
          explode: true
          schema:
            type: string
            enum:
              - JSON
              - YAML
        - name: preserveStatus
          in: query
          description: |
            Preserve API Status during export
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: latestRevision
          in: query
          description: |
            Export the latest revision of the API
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      responses:
        "200":
          description: |
            OK.
            Export Successful.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/zip:
              schema:
                type: string
                format: binary
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_create
            - apim:api_import_export
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_create
            - apim:api_import_export
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/export?apiId=96077508-fd01-4fae-bc64-5de0e2baf43c&name=PizzaShackAPI&version=1.0&provider=admin&format=YAML" > exportAPI.zip'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/import:
    post:
      tags:
        - Import Export
      summary: Import an API
      description: |
        This operation can be used to import an API.
      operationId: importAPI
      parameters:
        - name: preserveProvider
          in: query
          description: |
            Preserve Original Provider of the API. This is the user choice to keep or replace the API provider
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: rotateRevision
          in: query
          description: |
            Once the revision max limit reached, undeploy and delete the earliest revision and create a new revision
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: overwrite
          in: query
          description: |
            Whether to update the API or not. This is used when updating already existing APIs
          required: false
          style: form
          explode: true
          schema:
            type: boolean
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - file
              properties:
                file:
                  type: string
                  description: Zip archive consisting on exported API configuration
                  format: binary
      responses:
        "200":
          description: |
            Created.
            API Imported Successfully.
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_import_export
            - apim:admin
        - default:
            - apim:api_import_export
            - apim:admin
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@admin-PizzaShackAPI-1.0.0.zip "https://127.0.0.1:9443/api/am/publisher/v3/apis/import?preserveProvider=false&overwrite=false"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /subscriptions:
    get:
      tags:
        - Subscriptions
      summary: Get all Subscriptions
      description: |
        This operation can be used to retrieve a list of subscriptions of the user associated with the provided access token. This operation is capable of

        1. Retrieving all subscriptions for the user's APIs.
        `GET https://127.0.0.1:9443/api/am/publisher/v3/subscriptions`

        2. Retrieving subscriptions for a specific API.
        `GET https://127.0.0.1:9443/api/am/publisher/v3/subscriptions?apiId=c43a325c-260b-4302-81cb-768eafaa3aed`
      operationId: getSubscriptions
      parameters:
        - $ref: '#/components/parameters/apiId-Q-Opt'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/includeFromVersionRange'
        - $ref: '#/components/parameters/If-None-Match'
        - name: query
          in: query
          description: |
            Keywords to filter subscriptions
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            Subscription list returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:subscription_view
            - apim:subscription_manage
        - default:
            - apim:api_view
            - apim:subscription_view
            - apim:subscription_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/subscriptions?apiId=96077508-fd01-4fae-bc64-5de0e2baf43c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /subscriptions/{subscriptionId}/usage:
    get:
      tags:
        - API Monetization
      summary: Get Details of a Pending Invoice for a Monetized Subscription with Metered Billing.
      description: |
        This operation can be used to get details of a pending invoice for a monetized subscription with meterd billing.
      operationId: getSubscriptionUsage
      parameters:
        - $ref: '#/components/parameters/subscriptionId'
      responses:
        "200":
          description: |
            OK.
            Details of a pending invoice returned.
          headers:
            ETag:
              description: Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: Date and time the resource has been modified the last time. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIMonetizationUsage'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          description: |
            Not Found.
            Requested Subscription does not exist.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:subscription_view
            - apim:subscription_manage
        - default:
            - apim:api_view
            - apim:subscription_view
            - apim:subscription_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/subscriptions/64eca60b-2e55-4c38-8603-e9e6bad7d809/usage"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /subscriptions/{subscriptionId}/subscriber-info:
    get:
      tags:
        - Subscriber
      summary: Get Details of a Subscriber
      description: |
        This operation can be used to get details of a user who subscribed to the API.
      operationId: getSubscriberInfoBySubscriptionId
      parameters:
        - $ref: '#/components/parameters/subscriptionId'
      responses:
        "200":
          description: |
            OK.
             Details of the subscriber are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriberInfo'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:subscription_view
            - apim:subscription_manage
        - default:
            - apim:api_view
            - apim:subscription_view
            - apim:subscription_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/subscriptions/64eca60b-2e55-4c38-8603-e9e6bad7d809/subscriber-info"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /subscriptions/block-subscription:
    post:
      tags:
        - Subscriptions
      summary: Block a Subscription
      description: |
        This operation can be used to block a subscription. Along with the request, `blockState` must be specified as a query parameter.

        1. `BLOCKED` : Subscription is completely blocked for both Production and Sandbox environments.
        2. `PROD_ONLY_BLOCKED` : Subscription is blocked for Production environment only.
      operationId: blockSubscription
      parameters:
        - $ref: '#/components/parameters/subscriptionId-Q'
        - name: blockState
          in: query
          description: |
            Subscription block state.
          required: true
          style: form
          explode: true
          schema:
            type: string
            enum:
              - BLOCKED
              - PROD_ONLY_BLOCKED
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Subscription was blocked successfully.
          headers:
            ETag:
              description: |
                Entity Tag of the blocked subscription.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the subscription has been blocked.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:subscription_block
            - apim:subscription_manage
        - default:
            - apim:subscription_block
            - apim:subscription_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/subscriptions/block-subscription?subscriptionId=64eca60b-2e55-4c38-8603-e9e6bad7d809&blockState=PROD_ONLY_BLOCKED"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /subscriptions/unblock-subscription:
    post:
      tags:
        - Subscriptions
      summary: Unblock a Subscription
      description: |
        This operation can be used to unblock a subscription specifying the subscription Id. The subscription will be fully unblocked after performing this operation.
      operationId: unBlockSubscription
      parameters:
        - $ref: '#/components/parameters/subscriptionId-Q'
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Subscription was unblocked successfully.
          headers:
            ETag:
              description: |
                Entity Tag of the unblocked subscription.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the subscription has been unblocked.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:subscription_block
            - apim:subscription_manage
        - default:
            - apim:subscription_block
            - apim:subscription_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/subscriptions/unblock-subscription?subscriptionId=64eca60b-2e55-4c38-8603-e9e6bad7d809"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /throttling-policies/{policyLevel}:
    get:
      tags:
        - Throttling Policies
      summary: Get All Throttling Policies for the Given Type
      description: |
        This operation can be used to list the available policies for a given policy level. Tier level should be specified as a path parameter and should be one of `subscription` and `api`.
        `subscription` is for Subscription Level policies and `api` is for Resource Level policies
      operationId: getAllThrottlingPolicies
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/policyLevel'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            List of policies returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThrottlingPolicyList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:tier_view
            - apim:tier_manage
        - default:
            - apim:api_view
            - apim:tier_view
            - apim:tier_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/throttling-policies/api"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /throttling-policies/streaming/subscription:
    get:
      tags:
        - Throttling Policies
      summary: Get streaming throttling policies
      description: |
        This operation can be used to list the available streaming subscription policies
      operationId: getSubscriptionThrottlingPolicies
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            List of subscription policies returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionPolicyList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:tier_view
            - apim:tier_manage
        - default:
            - apim:api_view
            - apim:tier_view
            - apim:tier_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/throttling-policies/streaming/subscription?limit=10&offset=0"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /throttling-policies/{policyLevel}/{policyName}:
    get:
      tags:
        - Throttling Policies
      summary: Get Details of a Policy
      description: |
        This operation can be used to retrieve details of a single policy by specifying the policy level and policy name.
      operationId: getThrottlingPolicyByName
      parameters:
        - $ref: '#/components/parameters/policyName'
        - $ref: '#/components/parameters/policyLevel'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Tier returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThrottlingPolicy'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:tier_view
            - apim:tier_manage
        - default:
            - apim:api_view
            - apim:tier_view
            - apim:tier_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/throttling-policies/api/Platinum"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/client-certificates:
    get:
      tags:
        - Client Certificates
      summary: Retrieve/ Search Uploaded Client Certificates
      description: |
        This operation can be used to retrieve and search the uploaded client certificates.
      operationId: getAPIClientCertificates
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - name: alias
          in: query
          description: Alias for the client certificate
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK. Successful response with the list of matching certificate information in the body.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientCertificates'
        "400":
          $ref: '#/components/responses/BadRequest'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:client_certificates_view
            - apim:client_certificates_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:client_certificates_view
            - apim:client_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/d48a3412-1b85-49be-99f4-b81a3722ae73/client-certificates?alias=wso2carbon"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - Client Certificates
      summary: Upload a New Certificate
      description: |
        This operation can be used to upload a new certificate for an endpoint.
      operationId: addAPIClientCertificate
      parameters:
        - $ref: '#/components/parameters/apiId'
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - alias
                - certificate
                - tier
              properties:
                certificate:
                  type: string
                  description: The certificate that needs to be uploaded.
                  format: binary
                alias:
                  maxLength: 30
                  minLength: 1
                  type: string
                  description: Alias for the certificate
                tier:
                  type: string
                  description: API tier to which the certificate should be applied.
        required: true
      responses:
        "200":
          description: |
            OK.
            The Certificate added successfully.
          headers:
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientCertMetadata'
        "400":
          $ref: '#/components/responses/BadRequest'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:client_certificates_add
            - apim:client_certificates_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:client_certificates_add
            - apim:client_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: multipart/form-data" -F certificate=@test.crt -F alias=wso2carbon -F tier=Gold "https://127.0.0.1:9443/api/am/publisher/v3/apis/d48a3412-1b85-49be-99f4-b81a3722ae73/client-certificates"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/client-certificates/{alias}:
    get:
      tags:
        - Client Certificates
      summary: Get the Certificate Information
      description: |
        This operation can be used to get the information about a certificate.
      operationId: getAPIClientCertificateByAlias
      parameters:
        - name: alias
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertificateInfo'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:client_certificates_view
            - apim:client_certificates_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:client_certificates_view
            - apim:client_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/d48a3412-1b85-49be-99f4-b81a3722ae73/client-certificates/wso2carbon"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - Client Certificates
      summary: Update a Certificate
      description: |
        This operation can be used to update an uploaded certificate.
      operationId: updateAPIClientCertificateByAlias
      parameters:
        - name: alias
          in: path
          description: Alias for the certificate
          required: true
          style: simple
          explode: false
          schema:
            maxLength: 30
            minLength: 1
            type: string
        - $ref: '#/components/parameters/apiId'
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                certificate:
                  type: string
                  description: The certificate that needs to be uploaded.
                  format: binary
                tier:
                  type: string
                  description: The tier of the certificate
      responses:
        "200":
          description: |
            OK.
            The Certificate updated successfully.
          headers:
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientCertMetadata'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:client_certificates_update
            - apim:client_certificates_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:client_certificates_update
            - apim:client_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: multipart/form-data" -F certificate=@test.crt -F alias=wso2carbon -F apiId=fea749dd-d548-4a8b-b308-34903b39a34b -F tier=Gold "https://127.0.0.1:9443/api/am/publisher/v3/apis/d48a3412-1b85-49be-99f4-b81a3722ae73/client-certificates/wso2carbon"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - Client Certificates
      summary: Delete a Certificate
      description: |
        This operation can be used to delete an uploaded certificate.
      operationId: deleteAPIClientCertificateByAlias
      parameters:
        - name: alias
          in: path
          description: |
            The alias of the certificate that should be deleted.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - $ref: '#/components/parameters/apiId'
      responses:
        "200":
          description: |
            OK.
            The Certificate deleted successfully.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:client_certificates_update
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:client_certificates_update
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/d48a3412-1b85-49be-99f4-b81a3722ae73/client-certificates/wso2carbon"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/client-certificates/{alias}/content:
    get:
      tags:
        - Client Certificates
      summary: Download a Certificate
      description: |
        This operation can be used to download a certificate which matches the given alias.
      operationId: getAPIClientCertificateContentByAlias
      parameters:
        - $ref: '#/components/parameters/apiId'
        - name: alias
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:client_certificates_view
            - apim:client_certificates_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:client_certificates_view
            - apim:client_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/d48a3412-1b85-49be-99f4-b81a3722ae73/client-certificates/wso2carbon/content" > test.crt'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /endpoint-certificates:
    get:
      tags:
        - Endpoint Certificates
      summary: Retrieve/Search Uploaded Certificates
      description: |
        This operation can be used to retrieve and search the uploaded certificates.
      operationId: getEndpointCertificates
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - name: alias
          in: query
          description: Alias for the certificate
          required: false
          style: form
          explode: true
          schema:
            maxLength: 30
            type: string
        - name: endpoint
          in: query
          description: Endpoint of which the certificate is uploaded
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: |
            OK. Successful response with the list of matching certificate information in the body.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Certificates'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:ep_certificates_view
            - apim:ep_certificates_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:ep_certificates_view
            - apim:ep_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/endpoint-certificates?alias=wso2carbon&endpoint=www.abc.com"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - Endpoint Certificates
      summary: Upload a new Certificate.
      description: |
        This operation can be used to upload a new certificate for an endpoint.
      operationId: addEndpointCertificate
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - alias
                - certificate
                - endpoint
              properties:
                certificate:
                  type: string
                  description: The certificate that needs to be uploaded.
                  format: binary
                alias:
                  maxLength: 30
                  minLength: 1
                  type: string
                  description: Alias for the certificate
                endpoint:
                  type: string
                  description: Endpoint to which the certificate should be applied.
        required: true
      responses:
        "200":
          description: |
            OK.
            The Certificate added successfully.
          headers:
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertMetadata'
        "400":
          $ref: '#/components/responses/BadRequest'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:ep_certificates_add
            - apim:ep_certificates_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:ep_certificates_add
            - apim:ep_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: multipart/form-data" -F certificate=@test.crt -F alias=alias -F endpoint=https://www.abc.com "https://127.0.0.1:9443/api/am/publisher/v3/endpoint-certificates"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /endpoint-certificates/{alias}:
    get:
      tags:
        - Endpoint Certificates
      summary: Get the Certificate Information
      description: |
        This operation can be used to get the information about a certificate.
      operationId: getEndpointCertificateByAlias
      parameters:
        - name: alias
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertificateInfo'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:ep_certificates_view
            - apim:ep_certificates_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:ep_certificates_view
            - apim:ep_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/endpoint-certificates/wso2carbon"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - Endpoint Certificates
      summary: Update a certificate.
      description: |
        This operation can be used to update an uploaded certificate.
      operationId: updateEndpointCertificateByAlias
      parameters:
        - name: alias
          in: path
          description: Alias for the certificate
          required: true
          style: simple
          explode: false
          schema:
            maxLength: 30
            minLength: 1
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - certificate
              properties:
                certificate:
                  type: string
                  description: The certificate that needs to be uploaded.
                  format: binary
        required: true
      responses:
        "200":
          description: |
            OK.
            The Certificate updated successfully.
          headers:
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertMetadata'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:ep_certificates_update
            - apim:ep_certificates_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:ep_certificates_update
            - apim:ep_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: multipart/form-data" -F certificate=@test.crt "https://127.0.0.1:9443/api/am/publisher/v3/endpoint-certificates/wso2carbon"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - Endpoint Certificates
      summary: Delete a certificate.
      description: |
        This operation can be used to delete an uploaded certificate.
      operationId: deleteEndpointCertificateByAlias
      parameters:
        - name: alias
          in: path
          description: |
            The alias of the certificate that should be deleted.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            The Certificate deleted successfully.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
            - apim:ep_certificates_update
            - apim:ep_certificates_manage
        - default:
            - apim:api_create
            - apim:api_manage
            - apim:ep_certificates_update
            - apim:ep_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/endpoint-certificates/wso2carbon"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /endpoint-certificates/{alias}/content:
    get:
      tags:
        - Endpoint Certificates
      summary: Download a Certificate
      description: |
        This operation can be used to download a certificate which matches the given alias.
      operationId: getEndpointCertificateContentByAlias
      parameters:
        - name: alias
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:ep_certificates_view
            - apim:ep_certificates_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:ep_certificates_view
            - apim:ep_certificates_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/endpoint-certificates/wso2carbon/content" > test.crt'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /search:
    get:
      tags:
        - Unified Search
      summary: |
        Retrieve/Search APIs and API Documents by Content
      description: |
        This operation provides you a list of available APIs and API Documents qualifying the given keyword match.
      operationId: search
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - name: query
          in: query
          description: |
            **Search**.

            You can search by proving a keyword.

            You can search by the display name of the API or the API Document.
            "display-name:PizzaShackAPI" will match an API or the API Document if the display name is "PizzaShackAPI".
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            List of qualifying APIs and API documents is returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResultList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:api_import_export
            - apim:api_product_import_export
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:api_import_export
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/search?query=pizza"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products:
    get:
      tags:
        - API Products
      summary: |
        Retrieve/Search API Products
      description: |
        This operation provides you a list of available API Products qualifying under a given search condition.

        Each retrieved API Product is represented with a minimal amount of attributes. If you want to get complete details of an API Product, you need to use **Get details of an API Product** operation.
      operationId: getAllAPIProducts
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - name: query
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            List of qualifying API Products is returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIProductList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products?query=PizzaAPIProduct"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - API Products
      summary: Create a New API Product
      description: |
        This operation can be used to create a new API Product specifying the details of the API Product in the payload.
      operationId: createAPIProduct
      requestBody:
        description: API object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIProduct'
        required: true
      responses:
        "201":
          description: |
            'Created.
            Successful response with the newly created object as entity in the body.
            Location header contains URL of newly created entity.'
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIProduct'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/api-products"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}:
    get:
      tags:
        - API Products
      summary: Get Details of an API Product
      description: |
        Using this operation, you can retrieve complete details of a single API Product. You need to provide the Id of the API to retrive it.
      operationId: getAPIProduct
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested API Product is returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIProduct'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - API Products
      summary: Update an API Product
      description: |
        This operation can be used to update an existing API product.
        But the properties `name`, `provider` and `version` cannot be changed.
      operationId: updateAPIProduct
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        description: API object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIProduct'
        required: true
      responses:
        "200":
          description: |
            OK.
            Successful response with updated API product object
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIProduct'
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - API Products
      summary: Delete an API Product
      description: |
        This operation can be used to delete an existing API Product proving the Id of the API Product.
      operationId: deleteAPIProduct
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
        - default:
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/thumbnail:
    get:
      tags:
        - API Products
      summary: Get Thumbnail Image
      description: |
        This operation can be used to download a thumbnail image of an API product.
      operationId: getAPIProductThumbnail
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Thumbnail image returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/thumbnail" > image.jpeg'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - API Products
      summary: Upload a Thumbnail Image
      description: |
        This operation can be used to upload a thumbnail image of an API Product. The thumbnail to be uploaded should be given as a form data parameter `file`.
      operationId: updateAPIProductThumbnail
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - file
              properties:
                file:
                  type: string
                  description: Image to upload
                  format: binary
        required: true
      responses:
        "200":
          description: |
            OK.
            Image updated
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the uploaded thumbnail image of the API Product.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileInfo'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: multipart/form-data" -F file=@image.jpeg "https://127.0.0.1:9443/api/am/publisher/v3/api-products/d48a3412-1b85-49be-99f4-b81a3722ae73/thumbnail"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/swagger:
    get:
      tags:
        - API Products
      summary: Get Swagger Definition
      description: |
        This operation can be used to retrieve the swagger definition of an API.
      operationId: getAPIProductSwagger
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested swagger document of the API is returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/swagger"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/is-outdated:
    get:
      tags:
        - API Products
      summary: Check Whether API Product is Outdated
      description: |
        This operation can be used to retrieve the status indicating if an API Product is outdated due to updating of dependent APIs (This resource is not supported at the moment)
      operationId: getIsAPIProductOutdated
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested swagger document of the API is returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIProductOutdatedStatus'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/documents:
    get:
      tags:
        - API Product Documents
      summary: Get a List of Documents of an API Product
      description: |
        This operation can be used to retrive a list of documents belonging to an API Product by providing the ID of the API Product.
      operationId: getAPIProductDocuments
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Document list is returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentList'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/documents"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - API Product Documents
      summary: Add a New Document to an API Product
      description: |
        This operation can be used to add a new documentation to an API Product. This operation only adds the metadata of a document. To add the actual content we need to use **Upload the content of an API Product document ** API once we obtain a document Id by this operation.
      operationId: addAPIProductDocument
      parameters:
        - $ref: '#/components/parameters/apiProductId'
      requestBody:
        description: Document object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
        required: true
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created Document object as entity in the body.
            Location header contains URL of newly added document.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                Location to the newly created Document.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/documents"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/documents/{documentId}:
    get:
      tags:
        - API Product Documents
      summary: Get a Document of an API Product
      description: |
        This operation can be used to retrieve a particular document's metadata associated with an API.
      operationId: getAPIProductDocument
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Document returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/documents/83312daf-0d8a-427b-8f72-12755b7901d3"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - API Product Documents
      summary: Update a Document of an API Product
      description: |
        This operation can be used to update metadata of an API's document.
      operationId: updateAPIProductDocument
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        description: Document object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
        required: true
      responses:
        "200":
          description: |
            OK.
            Document updated
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the updated document.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/documents/83312daf-0d8a-427b-8f72-12755b7901d3"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - API Product Documents
      summary: Delete a Document of an API Product
      description: |
        This operation can be used to delete a document associated with an API Product.
      operationId: deleteAPIProductDocument
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/documents/83312daf-0d8a-427b-8f72-12755b7901d3"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/documents/{documentId}/content:
    get:
      tags:
        - API Product Documents
      summary: Get the Content of an API Product Document
      description: |
        This operation can be used to retrive the content of an API's document.

        The document can be of 3 types. In each cases responses are different.

        1. **Inline type**:
           The content of the document will be retrieved in `text/plain` content type

           _Sample cURL_ : `curl -k -H "Authorization:Bearer 579f0af4-37be-35c7-81a4-f1f1e9ee7c51" -F inlineContent=@"docs.txt" -X POST "https://localhost:9443/api/am/publisher/v3/apis/995a4972-3178-4b17-a374-756e0e19127c/documents/43c2bcce-60e7-405f-bc36-e39c0c5e189e/content`
        2. **FILE type**:
           The file will be downloaded with the related content type (eg. `application/pdf`)
        3. **URL type**:
            The client will recieve the URL of the document as the Location header with the response with - `303 See Other`
      operationId: getAPIProductDocumentContent
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/Accept'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            File or inline content returned.
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "303":
          description: |
            See Other.
            Source can be retrived from the URL specified at the Location header.
          headers:
            Location:
              description: |
                The Source URL of the document.
              style: simple
              explode: false
              schema:
                type: string
          content: {}
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/documents/83312daf-0d8a-427b-8f72-12755b7901d3/content"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - API Product Documents
      summary: Upload the Content of an API Product Document
      description: |
        Thid operation can be used to upload a file or add inline content to an API Product document.

        **IMPORTANT:**
        * Either **file** or **inlineContent** form data parameters should be specified at one time.
        * Document's source type should be **FILE** in order to upload a file to the document using **file** parameter.
        * Document's source type should be **INLINE** in order to add inline content to the document using **inlineContent** parameter.
      operationId: addAPIProductDocumentContent
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  description: Document to upload
                  format: binary
                inlineContent:
                  type: string
                  description: Inline content of the document
      responses:
        "200":
          description: |
            OK.
            Document updated
          headers:
            ETag:
              description: |
                Entity Tag of the response resource.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has been modifed the last time.
                Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the updated content of the document.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: multipart/form-data" -F file=@sample.pdf "https://127.0.0.1:9443/api/am/publisher/v3/api-products/5bca47e1-8233-46a5-9295-525dca337f33/documents/83312daf-0d8a-427b-8f72-12755b7901d3/content"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/revisions:
    get:
      tags:
        - API Product Revisions
      summary: List Revisions
      description: |
        List available revisions of an API Product
      operationId: getAPIProductRevisions
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - name: query
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            List of API Product revisions are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevisionList'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/revisions?query=deployed:true"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - API Product Revisions
      summary: Create a new API Product revision
      description: |
        Create a new API Product revision
      operationId: createAPIProductRevision
      parameters:
        - $ref: '#/components/parameters/apiProductId'
      requestBody:
        description: API Product object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIRevision'
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created APIRevision object as the entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevision'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/revisions"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/revisions/{revisionId}:
    get:
      tags:
        - API Product Revisions
      summary: Retrieve Revision
      description: |
        Retrieve a revision of an API Product (This resource is not supported at the moment)
      operationId: getAPIProductRevision
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/revisionId'
      responses:
        "200":
          description: |
            OK.
            An API revision is returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevision'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/revisions/e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - API Product Revisions
      summary: Delete Revision
      description: |
        Delete a revision of an API Product
      operationId: deleteAPIProductRevision
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/revisionId'
      responses:
        "200":
          description: |
            OK.
            List of remaining API revisions are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevisionList'
        "204":
          description: |
            No Content.
            Successfully deleted the revision
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/revisions/e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/deployments:
    get:
      tags:
        - API Product Revisions
      summary: List Deployments
      description: |
        List available deployed revision deployment details of an API Product
      operationId: getAPIProductRevisionDeployments
      parameters:
        - $ref: '#/components/parameters/apiProductId'
      responses:
        "200":
          description: |
            OK.
            List of deployed revision deployment details are returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevisionDeploymentList'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/deployments"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/deployments/{deploymentId}:
    put:
      tags:
        - API Product Revisions
      summary: Update Deployment
      description: |
        Update deployment devportal visibility
      operationId: updateAPIProductDeployment
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/deploymentId'
      requestBody:
        description: Deployment object that needs to be updated
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIRevisionDeployment'
      responses:
        "200":
          description: |
            Created.
            Successful response with the newly updated APIRevisionDeployment List object as the entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIRevisionDeployment'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/deployments/UHJvZHVjdGlvbiBhbmQgU2FuZGJveA"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/deploy-revision:
    post:
      tags:
        - API Product Revisions
      summary: Deploy Revision
      description: |
        Deploy an API Product Revision
      operationId: deployAPIProductRevision
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/revisionId-Q'
      requestBody:
        description: Deployment object that needs to be added
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/APIRevisionDeployment'
      responses:
        "200":
          description: |
            OK.
        "201":
          description: |
            Created.
            Successful response with the newly deployed APIRevisionDeployment List object as the entity in the body.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/APIRevisionDeployment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/deploy-revision?revisionId=e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/undeploy-revision:
    post:
      tags:
        - API Product Revisions
      summary: UnDeploy Revision
      description: |
        UnDeploy an API Product Revision
      operationId: undeployAPIProductRevision
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/revisionId-Q'
        - $ref: '#/components/parameters/revisionNum-Q'
        - name: allEnvironments
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      requestBody:
        description: Deployment object that needs to be added
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/APIRevisionDeployment'
      responses:
        "200":
          description: |
            OK.
        "201":
          description: |
            Created.
            Successful response with the newly undeployed APIRevisionDeploymentList object as the entity in the body.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/APIRevisionDeployment'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
        - default:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/undeploy-revision?revisionId=e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/restore-revision:
    post:
      tags:
        - API Product Revisions
      summary: Restore Revision
      description: |
        Restore a revision to the Current API of the API Product
      operationId: restoreAPIProductRevision
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/revisionId-Q'
      responses:
        "201":
          description: |
            Restored.
            Successful response with the newly restored API Product object as the entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIProduct'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/restore-revision?revisionId=e0824883-3e86-403a-aec1-22bbc454eb7c"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/export:
    get:
      tags:
        - Import Export
      summary: Export an API Product
      description: |
        This operation can be used to export the details of a particular API Product as a zip file.
      operationId: exportAPIProduct
      parameters:
        - name: name
          in: query
          description: |
            API Product Name
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: version
          in: query
          description: |
            Version of the API Product
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: providerName
          in: query
          description: |
            Provider name of the API Product
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: revisionNumber
          in: query
          description: |
            Revision number of the API Product
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: format
          in: query
          description: |
            Format of output documents. Can be YAML or JSON.
          required: false
          style: form
          explode: true
          schema:
            type: string
            enum:
              - JSON
              - YAML
        - name: preserveStatus
          in: query
          description: |
            Preserve API Product Status on export
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: latestRevision
          in: query
          description: |
            Export the latest revision of the API Product
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      responses:
        "200":
          description: |
            OK.
            Export Successful.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/zip:
              schema:
                type: string
                format: binary
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_product_import_export
        - default:
            - apim:api_view
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/export?name=LeasingAPIProduct&version=1.0.0&revisionNumber=2&provider=admin&format=YAML" > exportAPIProduct.zip'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/import:
    post:
      tags:
        - Import Export
      summary: Import an API Product
      description: |
        This operation can be used to import an API Product.
      operationId: importAPIProduct
      parameters:
        - name: preserveProvider
          in: query
          description: |
            Preserve Original Provider of the API Product. This is the user choice to keep or replace the API Product provider
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: rotateRevision
          in: query
          description: |
            Once the revision max limit reached, undeploy and delete the earliest revision and create a new revision
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: importAPIs
          in: query
          description: |
            Whether to import the dependent APIs or not.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: overwriteAPIProduct
          in: query
          description: |
            Whether to update the API Product or not. This is used when updating already existing API Products.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: overwriteAPIs
          in: query
          description: |
            Whether to update the dependent APIs or not. This is used when updating already existing dependent APIs of an API Product.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - file
              properties:
                file:
                  type: string
                  description: |
                    Zip archive consisting on exported API Product configuration
                  format: binary
      responses:
        "200":
          description: |
            Created.
            API Product Imported Successfully.
          content: {}
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_product_import_export
        - default:
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@admin-PizzaShackAPIProduct.zip "https://127.0.0.1:9443/api/am/publisher/v3/api-products/import?preserveProvider=false&overwriteAPIProduct=false&overwriteAPIs=false&importAPIs=false"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /roles/{roleId}:
    head:
      tags:
        - Roles
      summary: Check Whether Given Role Name already Exist
      description: |
        Using this operation, user can check a given role name exists or not.
      operationId: validateSystemRole
      parameters:
        - $ref: '#/components/parameters/roleId'
      responses:
        "200":
          description: OK. Requested role name exists.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -I -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/roles/SW50ZXJuYWwvcHVibGlzaGVyCQ"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /me/roles/{roleId}:
    head:
      tags:
        - Roles
      summary: Validate Whether the Logged-in User has the Given Role
      description: |
        Using this operation, logged-in user can check whether he has given role.
      operationId: validateUserRole
      parameters:
        - $ref: '#/components/parameters/roleId'
      responses:
        "200":
          description: OK. Requested user has the role.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -I -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/me/roles/SW50ZXJuYWwvcHVibGlzaGVyCQ"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /external-stores:
    get:
      tags:
        - External Stores
      summary: Retrieve External Stores List to Publish an API
      description: |
        Retrieve external stores list configured to publish an API
      operationId: getAllExternalStores
      responses:
        "200":
          description: |
            OK.
            External Stores list returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExternalStore'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/external-stores"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /settings:
    get:
      tags:
        - Settings
      summary: Retreive Publisher Settings
      description: |
        Retreive publisher settings
      operationId: getSettings
      responses:
        "200":
          description: |
            OK.
            Settings returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Settings'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:publisher_settings
        - default:
            - apim:api_view
            - apim:publisher_settings
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/settings"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /tenants:
    get:
      tags:
        - Tenants
      summary: |
        Get Tenants by State
      description: |
        This operation is to get tenants by state
      operationId: getTenantsByState
      parameters:
        - name: state
          in: query
          description: |
            The state represents the current state of the tenant

            Supported states are [active, inactive]
          required: false
          style: form
          explode: true
          schema:
            type: string
            enum:
              - active
              - inactive
            default: active
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        "200":
          description: |
            OK.
            Tenant names returned.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantList'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/tenants?state=active"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /tenants/{tenantDomain}:
    head:
      tags:
        - Tenants
      summary: Check Whether the Given Tenant already Exists
      description: |
        Using this operation, user can check whether a given tenant exists or not.
      operationId: getTenantExistence
      parameters:
        - $ref: '#/components/parameters/tenantDomain'
      responses:
        "200":
          description: OK. Requested tenant exists.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -I -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/tenants/wso2.com"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-categories:
    get:
      tags:
        - API Category (Collection)
      summary: Get all API categories
      description: |
        Get all API categories
      operationId: getAllAPICategories
      responses:
        "200":
          description: |
            OK.
            Categories returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APICategoryList'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-categories"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /scopes:
    get:
      tags:
        - Scopes
      summary: Get All Available Shared Scopes
      description: |
        This operation can be used to get all the available Shared Scopes.
      operationId: getSharedScopes
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        "200":
          description: |
            OK.
            Shared Scope list is returned.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScopeList'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:shared_scope_manage
        - default:
            - apim:api_view
            - apim:shared_scope_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/scopes"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - Scopes
      summary: Add a New Shared Scope
      description: |
        This operation can be used to add a new Shared Scope.
      operationId: addSharedScope
      requestBody:
        description: Scope object that needs to be added
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Scope'
        required: true
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created Scope object as an entity in the body.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Scope'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:shared_scope_manage
        - default:
            - apim:shared_scope_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/scopes"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /scopes/{scopeId}:
    get:
      tags:
        - Scopes
      summary: Get a Shared Scope by Scope Id
      description: |
        This operation can be used to retrieve details of a Shared Scope by a given scope Id.
      operationId: getSharedScope
      parameters:
        - $ref: '#/components/parameters/scopeId'
      responses:
        "200":
          description: |
            OK.
            Requested Shared Scope is returned.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Scope'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:shared_scope_manage
        - default:
            - apim:api_view
            - apim:shared_scope_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/scopes/01234567-0123-0123-0123-012345678901"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - Scopes
      summary: Update a Shared Scope
      description: |
        This operation can be used to update a Shared Scope by a given scope Id.
      operationId: updateSharedScope
      parameters:
        - $ref: '#/components/parameters/scopeId'
      requestBody:
        description: Scope object that needs to be updated
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Scope'
        required: true
      responses:
        "200":
          description: |
            OK.
            Successful response with updated Scope object
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Scope'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:shared_scope_manage
        - default:
            - apim:shared_scope_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/scopes/01234567-0123-0123-0123-012345678901"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - Scopes
      summary: Delete a Shared Scope
      description: |
        This operation can be used to delete a Shared Scope proving the Id of the scope.
      operationId: deleteSharedScope
      parameters:
        - $ref: '#/components/parameters/scopeId'
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:shared_scope_manage
        - default:
            - apim:shared_scope_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/scopes/01234567-0123-0123-0123-012345678901"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /scopes/{scopeId}/usage:
    get:
      tags:
        - Scopes
      summary: Get usages of a Shared Scope by Scope Id
      description: |
        This operation can be used to retrieve usages of a Shared Scope by a given scope Id.
      operationId: getSharedScopeUsages
      parameters:
        - $ref: '#/components/parameters/scopeId'
      responses:
        "200":
          description: |
            OK.
            Usages of the shared scope is returned.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SharedScopeUsage'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:shared_scope_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:shared_scope_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/scopes/01234567-0123-0123-0123-012345678901/usage"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /key-managers:
    get:
      tags:
        - Key Managers (Collection)
      summary: Get All Key Managers
      description: |
        Get all Key managers
      operationId: getAllKeyManagers
      parameters:
        - $ref: '#/components/parameters/EnvironmentId'
      responses:
        "200":
          description: |
            OK.
            Categories returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeyManagerList'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_view
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/key-managers"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/validate-asyncapi:
    post:
      tags:
        - Validation
      summary: Validate an AsyncAPI Specification
      description: This operation can be used to validate and AsyncAPI Specification and retrieve a summary. Provide either 'url' or 'file' to specify the definition.
      operationId: validateAsyncAPISpecification
      parameters:
        - name: returnContent
          in: query
          description: Specify whether to return the full content of the AsyncAPI specification in the response. This is only applicable when using url based validation
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                url:
                  type: string
                  description: AsyncAPI definition url
                file:
                  type: string
                  description: AsyncAPI definition as a file
                  format: binary
                inlineAPIDefinition:
                  type: string
                  description: Inline API definition
      responses:
        "200":
          description: OK. API definition validation information is returned
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AsyncAPISpecificationValidationResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@streetlights.yml "https://127.0.0.1:9443/api/am/publisher/v3/apis/validate-asyncapi"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/import-asyncapi:
    post:
      tags:
        - APIs
      summary: Import an AsyncAPI Specification
      description: |-
        This operation can be used to create and API from the AsyncAPI Specification. Provide either 'url' or 'file' to specify the definition.
        Specify additionalProperties with **at least** API's name, version, context and endpointConfig.
      operationId: importAsyncAPISpecification
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                file:
                  type: string
                  description: Definition to upload as a file
                  format: binary
                url:
                  type: string
                  description: Definition url
                additionalProperties:
                  type: string
                  description: Additional attributes specified as a stringified JSON with API's schema
                inlineAPIDefinition:
                  type: string
                  description: Inline API definition
      responses:
        "201":
          description: Created. Successful response with the newly created object as entity in the body. Location header contains URL of newly created entity.
          headers:
            Etag:
              description: Entity Tag of the respons resource. Used by caches, or in conditional requests (Will be supported in the future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/API'
        "400":
          $ref: '#/components/responses/BadRequest'
        "415":
          $ref: '#/components/responses/UnsupportedMediaType'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@streetlights.yml -F additionalProperties=@import-asyncapi.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/import-asyncapi"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/asyncapi:
    get:
      tags:
        - APIs
      summary: Get AsyncAPI definition
      description: |
        This operation can be used to retrieve the AsyncAPI definition of an API.
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Requested AsyncAPI definition of the API is returned
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Willl= be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has beed modified the last time.
                Used by caches, or in conditional request (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                type: string
                example: ""
        "304":
          description: |
            Not Modified.
            Empty body because the client has already the latest version of the requested resource (Will be supported in future).
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:api_definition_view
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:api_definition_view
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/01234567-0123-0123-0123-012345678901/asyncapi"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - APIs
      summary: Update AsyncAPI definition
      description: |
        This operation can be used to update the AsyncAPI definition of an existing API. AsyncAPI definition to be updated is passed as a form data parameter 'apiDefinition'.
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/If-Match'
      requestBody:
        content:
          multipart/form-data:
            schema:
              properties:
                apiDefinition:
                  type: string
                  description: AsyncAPI definition of the API
                url:
                  type: string
                  description: AsyncAPI definition URL of the API
                file:
                  type: string
                  description: AsyncAPI definition as a file
                  format: binary
      responses:
        "200":
          description: |
            OK.
            Successful response with updated AsyncAPI definition
          headers:
            ETag:
              description: |
                Entity Tag of the response resource. Used by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Last-Modified:
              description: |
                Date and time the resource has beed modified the last time.
                Use =d by caches, or in conditional requests (Will be supported in future).
              style: simple
              explode: false
              schema:
                type: string
            Location:
              description: |
                The URL of the newly created resource.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                type: string
                example: ""
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "412":
          $ref: '#/components/responses/PreconditionFailed'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -F file=@streetlights.yml "https://127.0.0.1:9443/api/am/publisher/v3/apis/01234567-0123-0123-0123-012345678901/asyncapi"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/environments/{envId}/keys:
    get:
      tags:
        - APIs
      summary: Get environment specific API properties
      description: |
        This operation can be used to retrieve environment specific API properties from an existing API.
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/envId'
      responses:
        "200":
          description: |
            OK.
            Successful response with environment specific API properties
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentProperties'
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/01234567-0123-0123-0123-012345678901/environments/9b37ac57-78b9-4fac-b0d4-dc8f4b15c023/keys"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      tags:
        - APIs
      summary: Update environment specific API properties
      description: |
        This operation can be used to update the environment specific API properties of an existing API.
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/envId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnvironmentProperties'
        required: true
      responses:
        "200":
          description: |
            OK.
            Successful response with environment specific API properties
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentProperties'
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X PUT -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: application/json" -d @data.json "https://127.0.0.1:9443/api/am/publisher/v3/apis/01234567-0123-0123-0123-012345678901/environments/9b37ac57-78b9-4fac-b0d4-dc8f4b15c023/keys"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/operation-policies:
    get:
      tags:
        - API Operation Policies
      summary: |
        Get all API specific operation policies for an API
      description: |
        This operation provides you a list of all applicabale operation policies for an API
      operationId: getAllAPISpecificOperationPolicies
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - name: query
          in: query
          description: -Not supported yet-
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            List of qualifying policies is returned.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationPolicyDataList'
        "406":
          $ref: '#/components/responses/NotAcceptable'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:mediation_policy_view
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:mediation_policy_view
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/operation-policies"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - API Operation Policies
      summary: Add an API specific operation policy
      description: |
        This operation can be used to add an API specifc operation policy. This policy cannot be used in other APIs.
      operationId: addAPISpecificOperationPolicy
      parameters:
        - $ref: '#/components/parameters/apiId'
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - type
              properties:
                policySpecFile:
                  type: string
                  description: Policy specification to upload
                  format: binary
                synapsePolicyDefinitionFile:
                  type: string
                  description: Operation policy definition of synapse gateway to upload
                  format: binary
                ccPolicyDefinitionFile:
                  type: string
                  description: Operation policy definition of choreo connect to upload
                  format: binary
        required: true
      responses:
        "201":
          description: |
            OK.
            Operation policy uploaded
          headers:
            Location:
              description: |
                The URL of the uploaded operation policy of the API.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationPolicyData'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_manage
            - apim:mediation_policy_create
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
        - default:
            - apim:api_manage
            - apim:mediation_policy_create
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: multipart/form-data" -F policySpecFile=@setHeader.yaml -F policyDefinitionFile=@setHeader.j2 "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/operation-policies"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/operation-policies/{operationPolicyId}:
    get:
      tags:
        - API Operation Policies
      summary: Get policy details of an API specific policy
      description: |
        This operation can be used to retrieve a particular API specific operation policy.
      operationId: getOperationPolicyForAPIByPolicyId
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/operationPolicyId'
      responses:
        "200":
          description: |
            OK.
            Operation policy returned.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationPolicyData'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:mediation_policy_view
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:mediation_policy_view
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/operation-policies/f56eb8b4-128c-45aa-ad35-9c87a546261a"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - API Operation Policies
      summary: Delete an API Specific Operation Policy
      description: |
        This operation can be used to delete an existing API specific opreation policy by providing the Id of the API and the Id of the policy.
      operationId: deleteAPISpecificOperationPolicyByPolicyId
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/operationPolicyId'
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_manage
            - apim:mediation_policy_create
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
        - default:
            - apim:api_manage
            - apim:mediation_policy_create
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/operation-policies/f56eb8b4-128c-45aa-ad35-9c87a546261a"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /apis/{apiId}/operation-policies/{operationPolicyId}/content:
    get:
      tags:
        - API Operation Policies
      summary: Download an API Specific Operation Policy
      description: |
        This operation can be used to download a particular API specific operation policy.
      operationId: getAPISpecificOperationPolicyContentByPolicyId
      parameters:
        - $ref: '#/components/parameters/apiId'
        - $ref: '#/components/parameters/operationPolicyId'
      responses:
        "200":
          description: |
            OK.
            Operation policy returned.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/zip:
              schema:
                type: string
                format: binary
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_view
            - apim:api_manage
            - apim:mediation_policy_view
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
        - default:
            - apim:api_view
            - apim:api_manage
            - apim:mediation_policy_view
            - apim:mediation_policy_manage
            - apim:api_mediation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/apis/96077508-fd01-4fae-bc64-5de0e2baf43c/operation-policies/f56eb8b4-128c-45aa-ad35-9c87a546261a/content"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /operation-policies:
    get:
      tags:
        - Operation Policies
      summary: |
        Get all common operation policies to all the APIs
      description: |
        This operation provides you a list of all common operation policies that can be used by any API
      operationId: getAllCommonOperationPolicies
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - name: query
          in: query
          description: -Not supported yet-
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            List of qualifying policies is returned.
          headers:
            Content-Type:
              description: The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationPolicyDataList'
        "406":
          $ref: '#/components/responses/NotAcceptable'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_manage
            - apim:common_operation_policy_view
            - apim:common_operation_policy_manage
        - default:
            - apim:api_manage
            - apim:common_operation_policy_view
            - apim:common_operation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/operation-policies"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      tags:
        - Operation Policies
      summary: Add a new common operation policy
      description: |
        This operation can be used to add a new common operation policy.
      operationId: addCommonOperationPolicy
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - type
              properties:
                policySpecFile:
                  type: string
                  description: Operation policy specification to upload
                  format: binary
                synapsePolicyDefinitionFile:
                  type: string
                  description: Operation policy definition of synapse gateway to upload
                  format: binary
                ccPolicyDefinitionFile:
                  type: string
                  description: Operation policy definition of choreo connect to upload
                  format: binary
        required: true
      responses:
        "201":
          description: |
            OK.
            Shared operation policy uploaded
          headers:
            Location:
              description: |
                The URL of the uploaded common operation policy of the API.
              style: simple
              explode: false
              schema:
                type: string
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationPolicyData'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:common_operation_policy_manage
        - default:
            - apim:common_operation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -H "Content-Type: multipart/form-data" -F policySpecFile=@setHeader.yaml -F policyDefinitionFile=@setHeader.j2 "https://127.0.0.1:9443/api/am/publisher/v3/operation-policies"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /operation-policies/{operationPolicyId}:
    get:
      tags:
        - Operation Policies
      summary: Get the details of a common operation policy by providing policy ID
      description: |
        This operation can be used to retrieve a particular common operation policy.
      operationId: getCommonOperationPolicyByPolicyId
      parameters:
        - $ref: '#/components/parameters/operationPolicyId'
      responses:
        "200":
          description: |
            OK.
            Operation policy returned.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationPolicyData'
        "404":
          $ref: '#/components/responses/NotFound'
        "406":
          $ref: '#/components/responses/NotAcceptable'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_manage
            - apim:common_operation_policy_view
            - apim:common_operation_policy_manage
        - default:
            - apim:api_manage
            - apim:common_operation_policy_view
            - apim:common_operation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/operation-policies/f56eb8b4-128c-45aa-ad35-9c87a546261a"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      tags:
        - Operation Policies
      summary: Delete a common operation policy
      description: |
        This operation can be used to delete an existing common opreation policy by providing the Id of the policy.
      operationId: deleteCommonOperationPolicyByPolicyId
      parameters:
        - $ref: '#/components/parameters/operationPolicyId'
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:common_operation_policy_manage
        - default:
            - apim:common_operation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/operation-policies/f56eb8b4-128c-45aa-ad35-9c87a546261a"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /operation-policies/{operationPolicyId}/content:
    get:
      tags:
        - Operation Policies
      summary: Download a common operation policy
      description: |
        This operation can be used to download a selected common operation policy.
      operationId: getCommonOperationPolicyContentByPolicyId
      parameters:
        - $ref: '#/components/parameters/operationPolicyId'
      responses:
        "200":
          description: |
            OK.
            Operation policy returned.
          headers:
            Content-Type:
              description: |
                The content type of the body.
              style: simple
              explode: false
              schema:
                type: string
          content:
            application/zip:
              schema:
                type: string
                format: binary
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_manage
            - apim:common_operation_policy_view
            - apim:common_operation_policy_manage
        - default:
            - apim:api_manage
            - apim:common_operation_policy_view
            - apim:common_operation_policy_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/operation-policies/f56eb8b4-128c-45aa-ad35-9c87a546261a/content"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/change-lifecycle:
    post:
      tags:
        - API Product Lifecycle
      summary: Change API Product LC Status
      description: |
        This operation is used to change the lifecycle of an API Product.
        Eg: Publish an API Product which is in `CREATED` state. In order to change the lifecycle, we need to provide the
        lifecycle `action` as a query parameter.

        For example, to Publish an API Product, `action` should be `Publish`. Note that the `Re-publish` action is
        available only after calling `Block`.

        Some actions supports providing additional paramters which should be provided as `lifecycleChecklist` parameter.
        Please see parameters table for more information.
      operationId: changeAPIProductLifecycle
      parameters:
        - name: action
          in: query
          description: |
            The action to demote or promote the state of the API Product.

            Supported actions are [ **Publish**, **Deploy as a Prototype**, **Demote to Created**, **Block**, **Deprecate**, **Re-Publish**, **Retire** ]
          required: true
          style: form
          explode: true
          schema:
            type: string
            enum:
              - Publish
              - Deploy as a Prototype
              - Demote to Created
              - Block
              - Deprecate
              - Re-Publish
              - Retire
        - name: lifecycleChecklist
          in: query
          description: |
            Supported checklist items are as follows.
            1. **Deprecate old versions after publishing the API**: Setting this to true will deprecate older versions of a particular API Product when it is promoted to Published state from Created state.
            2. **Requires re-subscription when publishing the API**: If you set this to true, users need to re subscribe to the API Products although they may have subscribed to an older version.
            You can specify additional checklist items by using an **"attribute:"** modifier.
            Eg: "Deprecate old versions after publishing the API:true" will deprecate older versions of a particular API Product when it is promoted to Published state from Created state. Multiple checklist items can be given in "attribute1:true, attribute2:false" format.
            **Sample CURL :**  curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" -X POST "https://localhost:9443/api/am/publisher/v3/api-products/change-lifecycle?apiId=890a4f4d-09eb-4877-a323-57f6ce2ed79b&action=Publish&lifecycleChecklist=Deprecate%20old%20versions%20after%20publishing%20the%20API%3Atrue,Requires%20re-subscription%20when%20publishing%20the%20API%3Afalse"
          required: false
          style: form
          explode: true
          schema:
            type: string
        - $ref: '#/components/parameters/apiProductId-Q'
        - $ref: '#/components/parameters/If-Match'
      responses:
        "200":
          description: |
            OK.
            Lifecycle changed successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
        - default:
            - apim:api_publish
            - apim:api_manage
            - apim:api_product_import_export
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X POST -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/change-lifecycle?apiId=890a4f4d-09eb-4877-a323-57f6ce2ed79b&action=Publish"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/lifecycle-history:
    get:
      tags:
        - API Product Lifecycle
      summary: Get Lifecycle State Change History of the API Products.
      description: |
        This operation can be used to retrieve Lifecycle state change history of the API Products.
      operationId: getAPIProductLifecycleHistory
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Lifecycle state change history returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LifecycleHistory'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/lifecycle-history"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/lifecycle-state:
    get:
      tags:
        - API Product Lifecycle
      summary: Get Lifecycle State Data of the API Product.
      description: |
        This operation can be used to retrieve Lifecycle state data of the API Product.
      operationId: getAPIProductLifecycleState
      parameters:
        - $ref: '#/components/parameters/apiProductId'
        - $ref: '#/components/parameters/If-None-Match'
      responses:
        "200":
          description: |
            OK.
            Lifecycle state data returned successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LifecycleState'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_create
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_create
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/lifecycle-state"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /api-products/{apiProductId}/lifecycle-state/pending-tasks:
    delete:
      tags:
        - API Product Lifecycle
      summary: Delete Pending Lifecycle State Change Tasks
      description: |
        This operation can be used to remove pending lifecycle state change requests that are in pending state
      operationId: deleteAPIProductLifecycleStatePendingTasks
      parameters:
        - $ref: '#/components/parameters/apiProductId'
      responses:
        "200":
          description: |
            OK.
            Lifecycle state change pending task removed successfully.
          content: {}
        "401":
          $ref: '#/components/responses/Unauthorized'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - apim:api_publish
            - apim:api_manage
        - default:
            - apim:api_publish
            - apim:api_manage
      x-code-samples:
        - lang: Curl
          source: 'curl -k -X DELETE -H "Authorization: Bearer ae4eae22-3f65-387b-a171-d37eaa366fa8" "https://127.0.0.1:9443/api/am/publisher/v3/api-products/890a4f4d-09eb-4877-a323-57f6ce2ed79b/lifecycle-state/pending-tasks"'
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    Comment:
      title: Comment
      required:
        - content
      type: object
      properties:
        id:
          type: string
          readOnly: true
          example: 943d3002-000c-42d3-a1b9-d6559f8a4d49
        content:
          maxLength: 512
          type: string
          example: This is a comment
        createdTime:
          type: string
          readOnly: true
          example: 2021-02-11-09:57:25
        createdBy:
          type: string
          readOnly: true
          example: admin
        updatedTime:
          type: string
          readOnly: true
          example: 2021-02-12-19:57:25
        category:
          type: string
          readOnly: true
          example: general
          default: general
        parentCommentId:
          type: string
          readOnly: true
          example: 6f38aea2-f41e-4ac9-b3f2-a9493d00ba97
        entryPoint:
          type: string
          readOnly: true
          enum:
            - devPortal
            - publisher
        commenterInfo:
          $ref: '#/components/schemas/CommenterInfo'
        replies:
          $ref: '#/components/schemas/CommentList'
    CommentList:
      title: Comments List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Comments returned.
          readOnly: true
          example: 1
        list:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/Comment'
        pagination:
          $ref: '#/components/schemas/Pagination'
    CommenterInfo:
      type: object
      properties:
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: David
        fullName:
          type: string
          example: John David
    APIList:
      title: API List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of APIs returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/APIInfo'
        pagination:
          $ref: '#/components/schemas/Pagination'
    APIInfo:
      title: API Info object with basic API details.
      type: object
      properties:
        id:
          type: string
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          example: CalculatorAPI
        displayName:
          type: string
          example: CalculatorAPI
        description:
          type: string
          example: A calculator API that supports basic operations
        context:
          type: string
          example: CalculatorAPI
        additionalProperties:
          type: array
          description: Map of custom properties of API
          items:
            type: object
            properties:
              name:
                type: string
              value:
                type: string
              display:
                type: boolean
        additionalPropertiesMap:
          type: object
          additionalProperties:
            type: object
            properties:
              name:
                type: string
              value:
                type: string
              display:
                type: boolean
                default: false
        version:
          type: string
          example: 1.0.0
        provider:
          type: string
          description: |
            If the provider value is not given, the user invoking the API will be used as the provider.
          example: admin
        type:
          type: string
          example: HTTP
        audience:
          type: string
          description: The audience of the API. Accepted values are PUBLIC, SINGLE
          example: PUBLIC
          enum:
            - PUBLIC
            - SINGLE
        lifeCycleStatus:
          type: string
          example: CREATED
        workflowStatus:
          type: string
          example: APPROVED
        hasThumbnail:
          type: boolean
          example: true
        securityScheme:
          type: array
          items:
            type: string
        createdTime:
          type: string
          example: "2021-02-11 09:57:25"
        updatedTime:
          type: string
          example: "2021-02-11 09:57:25"
        gatewayVendor:
          type: string
          example: wso2
        advertiseOnly:
          type: boolean
          example: true
        choreoComponentInfo:
          $ref: '#/components/schemas/ChoreoComponentInfo'
    ChoreoComponentInfo:
      title: Choreo Component Info object
      type: object
      properties:
        organizationId:
          type: string
          description: Organization ID
          readOnly: true
          example: 90bce838-056d-46df-8a0d-0fbaac6d3499
        projectId:
          pattern: ^[a-f\d]{8}\-[a-f\d]{4}\-[a-f\d]{4}\-[a-f\d]{4}\-[a-f\d]{12}$
          type: string
          description: Project ID
          example: c0fb925e-a2ef-4e8a-9b92-2f0e433b054d
        componentId:
          pattern: ^[a-f\d]{8}\-[a-f\d]{4}\-[a-f\d]{4}\-[a-f\d]{4}\-[a-f\d]{12}$
          type: string
          description: Component ID
          example: e84b40a8-bda9-4649-bd14-b53227f45157
        versionId:
          pattern: ^([a-f\d]{8}\-[a-f\d]{4}\-[a-f\d]{4}\-[a-f\d]{4}\-[a-f\d]{12}|[a-f\d]{24})$
          type: string
          description: Version ID
          example: 65029d779fd7f828ac543080
    Topic:
      title: Topic object
      required:
        - description
        - mode
        - name
      type: object
      properties:
        id:
          type: string
          description: id
          readOnly: true
          example: "1222344"
        name:
          maxLength: 50
          minLength: 1
          pattern: (^[^~!@#;:%^*()+={}|\\<>"',&$\s+\[\]\/]*$)
          type: string
          example: PizzaShackAPI
        mode:
          maxLength: 20
          type: string
          example: Pizza
        description:
          maxLength: 32766
          type: string
          example: This is a simple API for Pizza Shack online pizza delivery store.
    TopicList:
      title: Topic List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Topics returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/Topic'
        pagination:
          $ref: '#/components/schemas/Pagination'
    API:
      title: API object
      required:
        - context
        - name
        - version
      type: object
      properties:
        id:
          type: string
          description: |
            UUID of the api registry artifact
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        name:
          maxLength: 150
          minLength: 1
          pattern: (^[^~!@#;:%^*()+={}|\\<>"',&$\[\]\/]*$)
          type: string
          example: PizzaShackAPI
        displayName:
          maxLength: 150
          minLength: 1
          pattern: (^[^~!@#;:%^*()+={}|\\<>"',&$\[\]\/]*$)
          type: string
          example: PizzaShackAPI
        description:
          maxLength: 32766
          type: string
          example: This is a simple API for Pizza Shack online pizza delivery store.
        context:
          maxLength: 232
          minLength: 1
          type: string
          example: pizza
        version:
          maxLength: 30
          minLength: 1
          pattern: ^[^~!@#;:%^*()+={}|\\<>"',&/$\[\]\s+\/]+$
          type: string
          example: 1.0.0
        provider:
          maxLength: 50
          type: string
          description: |
            If the provider value is not given user invoking the api will be used as the provider.
          example: admin
        lifeCycleStatus:
          type: string
          example: CREATED
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        wsdlInfo:
          $ref: '#/components/schemas/WSDLInfo'
        wsdlUrl:
          type: string
          readOnly: true
          example: /apimgt/applicationdata/wsdls/admin--soap1.wsdl
        responseCachingEnabled:
          type: boolean
          example: true
        cacheTimeout:
          type: integer
          example: 300
        hasThumbnail:
          type: boolean
          example: false
        isDefaultVersion:
          type: boolean
          example: false
        isRevision:
          type: boolean
          example: false
        enableBackendJWT:
          type: boolean
          example: true
        backendJWTConfiguration:
          type: object
          properties:
            audiences:
              type: array
              description: |
                The list of audiences to which the JWT should be issued.
              example:
                - sampleOrg1.com
                - sampleOrg2.com
              items:
                type: string
        revisionedApiId:
          type: string
          description: |
            UUID of the api registry artifact
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        revisionId:
          type: integer
          example: 1
        enableSchemaValidation:
          type: boolean
          example: false
        type:
          type: string
          description: The api creation type to be used. Accepted values are HTTP, WS, SOAPTOREST, GRAPHQL, WEBSUB, SSE, WEBHOOK, ASYNC
          example: HTTP
          enum:
            - HTTP
            - WS
            - SOAPTOREST
            - SOAP
            - GRAPHQL
            - WEBSUB
            - SSE
            - WEBHOOK
            - ASYNC
          default: HTTP
        audience:
          type: string
          description: The audience of the API. Accepted values are PUBLIC, SINGLE
          example: PUBLIC
          enum:
            - PUBLIC
            - SINGLE
        transport:
          type: array
          description: |
            Supported transports for the API (http and/or https).
          example:
            - http
            - https
          items:
            type: string
        tags:
          type: array
          example:
            - pizza
            - food
          items:
            type: string
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        policies:
          type: array
          example:
            - Unlimited
          items:
            type: string
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        apiThrottlingPolicy:
          type: string
          description: The API level throttling policy selected for the particular API
          example: Unlimited
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        throttlingLimit:
          $ref: '#/components/schemas/ThrottlingLimit'
        authorizationHeader:
          pattern: (^[^~!@#;:%^*()+={}|\\<>"',&$\s+]*$)
          type: string
          description: |
            Name of the Authorization header used for invoking the API. If it is not set, Authorization header name specified
            in tenant or system level will be used.
          example: Authorization
        securityScheme:
          type: array
          description: |
            Types of API security, the current API secured with. It can be either OAuth2 or mutual SSL or both. If
            it is not set OAuth2 will be set as the security for the current API.
          example:
            - oauth2
          items:
            type: string
          x-default-null: true
        maxTps:
          $ref: '#/components/schemas/APIMaxTps'
        visibility:
          type: string
          description: The visibility level of the API. Accepts one of the following. PUBLIC, PRIVATE, RESTRICTED.
          example: PUBLIC
          enum:
            - PUBLIC
            - PRIVATE
            - RESTRICTED
          default: PUBLIC
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        visibleRoles:
          type: array
          description: The user roles that are able to access the API in Developer Portal
          example: []
          items:
            type: string
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        visibleTenants:
          type: array
          example: []
          items:
            type: string
        mediationPolicies:
          type: array
          example:
            - name: json_to_xml_in_message
              type: in
            - name: xml_to_json_out_message
              type: out
            - name: json_fault
              type: fault
          items:
            $ref: '#/components/schemas/MediationPolicy'
        subscriptionAvailability:
          type: string
          description: The subscription availability. Accepts one of the following. CURRENT_TENANT, ALL_TENANTS or SPECIFIC_TENANTS.
          example: CURRENT_TENANT
          enum:
            - CURRENT_TENANT
            - ALL_TENANTS
            - SPECIFIC_TENANTS
          default: CURRENT_TENANT
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        subscriptionAvailableTenants:
          type: array
          example: []
          items:
            type: string
        additionalProperties:
          type: array
          description: Map of custom properties of API
          items:
            type: object
            properties:
              name:
                type: string
              value:
                type: string
              display:
                type: boolean
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        additionalPropertiesMap:
          type: object
          additionalProperties:
            type: object
            properties:
              name:
                type: string
              value:
                type: string
              display:
                type: boolean
                default: false
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        monetization:
          $ref: '#/components/schemas/APIMonetizationInfo'
        accessControl:
          type: string
          description: |
            Is the API is restricted to certain set of publishers or creators or is it visible to all the
            publishers and creators. If the accessControl restriction is none, this API can be modified by all the
            publishers and creators, if not it can only be viewable/modifiable by certain set of publishers and creators,
             based on the restriction.
          enum:
            - NONE
            - RESTRICTED
          default: NONE
        accessControlRoles:
          type: array
          description: The user roles that are able to view/modify as API publisher or creator.
          example: []
          items:
            type: string
        businessInformation:
          allOf:
            - $ref: '#/components/schemas/APIBusinessInformation'
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        corsConfiguration:
          $ref: '#/components/schemas/APICorsConfiguration'
        websubSubscriptionConfiguration:
          $ref: '#/components/schemas/WebsubSubscriptionConfiguration'
        workflowStatus:
          type: string
          example: APPROVED
        createdTime:
          type: string
        lastUpdatedTime:
          type: string
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        endpointConfig:
          type: object
          properties: {}
          description: |
            Endpoint configuration of the API. This can be used to provide different types of endpoints including Simple REST Endpoints, Loadbalanced and Failover.

            `Simple REST Endpoint`
              {
                "endpoint_type": "http",
                "sandbox_endpoints":       {
                   "url": "https://localhost:9443/am/sample/pizzashack/v3/api/"
                },
                "production_endpoints":       {
                   "url": "https://localhost:9443/am/sample/pizzashack/v3/api/"
                }
              }

            `Loadbalanced Endpoint`

              {
                "endpoint_type": "load_balance",
                "algoCombo": "org.apache.synapse.endpoints.algorithms.RoundRobin",
                "sessionManagement": "",
                "sandbox_endpoints":       [
                            {
                      "url": "https://localhost:9443/am/sample/pizzashack/v3/api/1"
                   },
                            {
                      "endpoint_type": "http",
                      "template_not_supported": false,
                      "url": "https://localhost:9443/am/sample/pizzashack/v3/api/2"
                   }
                ],
                "production_endpoints":       [
                            {
                      "url": "https://localhost:9443/am/sample/pizzashack/v3/api/3"
                   },
                            {
                      "endpoint_type": "http",
                      "template_not_supported": false,
                      "url": "https://localhost:9443/am/sample/pizzashack/v3/api/4"
                   }
                ],
                "sessionTimeOut": "",
                "algoClassName": "org.apache.synapse.endpoints.algorithms.RoundRobin"
              }

            `Failover Endpoint`

              {
                "production_failovers":[
                   {
                      "endpoint_type":"http",
                      "template_not_supported":false,
                      "url":"https://localhost:9443/am/sample/pizzashack/v3/api/1"
                   }
                ],
                "endpoint_type":"failover",
                "sandbox_endpoints":{
                   "url":"https://localhost:9443/am/sample/pizzashack/v3/api/2"
                },
                "production_endpoints":{
                   "url":"https://localhost:9443/am/sample/pizzashack/v3/api/3"
                },
                "sandbox_failovers":[
                   {
                      "endpoint_type":"http",
                      "template_not_supported":false,
                      "url":"https://localhost:9443/am/sample/pizzashack/v3/api/4"
                   }
                ]
              }

            `Default Endpoint`

              {
                "endpoint_type":"default",
                "sandbox_endpoints":{
                   "url":"default"
                },
                "production_endpoints":{
                   "url":"default"
                }
              }

            `Endpoint from Endpoint Registry`
              {
                "endpoint_type": "Registry",
                "endpoint_id": "{registry-name:entry-name:version}",
              }
          example:
            endpoint_type: http
            sandbox_endpoints:
              url: https://localhost:9443/am/sample/pizzashack/v3/api/
            production_endpoints:
              url: https://localhost:9443/am/sample/pizzashack/v3/api/
        endpointImplementationType:
          type: string
          example: INLINE
          enum:
            - INLINE
            - ENDPOINT
            - MOCKED_OAS
          default: ENDPOINT
        scopes:
          type: array
          items:
            $ref: '#/components/schemas/APIScope'
        scopePrefix:
          type: string
          description: Unique scope identifier of an API
          example: urn:APIID
        operations:
          type: array
          example:
            - target: /order/{orderId}
              verb: POST
              authType: Application & Application User
              throttlingPolicy: Unlimited
            - target: /menu
              verb: GET
              authType: Application & Application User
              throttlingPolicy: Unlimited
          items:
            $ref: '#/components/schemas/APIOperations'
        threatProtectionPolicies:
          type: object
          properties:
            list:
              type: array
              items:
                type: object
                properties:
                  policyId:
                    type: string
                  priority:
                    type: integer
        categories:
          type: array
          description: |
            API categories
          items:
            type: string
            example: ""
          x-otherScopes:
            - apim:api_publish
        keyManagers:
          type: object
          properties: {}
          description: |
            API Key Managers
          readOnly: true
        serviceInfo:
          type: object
          properties:
            key:
              type: string
              example: PetStore-1.0.0
            name:
              type: string
              example: PetStore
            version:
              type: string
              example: 1.0.0
            outdated:
              type: boolean
              example: false
        advertiseInfo:
          $ref: '#/components/schemas/AdvertiseInfo'
        gatewayVendor:
          title: field to identify gateway vendor
          type: string
          example: wso2
        gatewayType:
          title: Field to identify gateway type.
          type: string
          description: The gateway type selected for the API policies. Accepts one of the following. wso2/synapse, wso2/choreo-connect.
          example: wso2/synapse
        asyncTransportProtocols:
          type: array
          description: |
            Supported transports for the async API (http and/or https).
          example:
            - http
            - https
          items:
            type: string
        choreoComponentInfo:
          $ref: '#/components/schemas/ChoreoComponentInfo'
      x-scopes:
        - apim:api_create
        - apim:api_import_export
        - apim:api_manage
    APIValidationRequest:
      title: Validation Request
      type: object
      properties:
        name:
          maxLength: 60
          minLength: 1
          pattern: (^[^~!@#;:%^*()+={}|\\<>"',&$\[\]\/]*$)
          type: string
          description: Name of the API
          example: PizzaShackAPI
        displayName:
          maxLength: 60
          minLength: 1
          pattern: (^[^~!@#;:%^*()+={}|\\<>"',&$\[\]\/]*$)
          type: string
          description: Display name of the API
          example: Pizza Shack API
        context:
          maxLength: 232
          minLength: 1
          type: string
          description: Context of the API
          example: pizzashack
      description: |
        API Validation request object
    APIRevision:
      title: API Info object with basic API details
      properties:
        displayName:
          type: string
          readOnly: true
          example: REVISION 1
        id:
          type: string
          readOnly: true
          example: c26b2b9b-4632-4ca4-b6f3-521c8863990c
        description:
          maxLength: 255
          minLength: 0
          type: string
          example: removed a post resource
        createdTime:
          type: string
          format: date-time
          readOnly: true
        apiInfo:
          $ref: '#/components/schemas/APIRevisionAPIInfo'
        deploymentInfo:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/APIRevisionDeployment'
    APIRevisionAPIInfo:
      title: API Info object with basic Revisioned API details
      properties:
        id:
          type: string
          example: 01234567-0123-0123-0123-012345678901
      readOnly: true
    APIRevisionList:
      title: API Revisions List
      properties:
        count:
          type: integer
          description: |
            Number of API revisions returned
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/APIRevision'
    APIRevisionDeploymentList:
      title: API Revision to Deployment mapped object with basic API deployment details
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/APIRevisionDeployment'
    APIRevisionDeployment:
      title: APIRevisionDeployment Info object with basic API deployment details
      properties:
        revisionUuid:
          maxLength: 255
          minLength: 0
          type: string
          example: c26b2b9b-4632-4ca4-b6f3-521c8863990c
        name:
          maxLength: 255
          minLength: 1
          type: string
          example: Default
        vhost:
          maxLength: 255
          minLength: 1
          pattern: ^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9])$
          type: string
          example: mg.wso2.com
        displayOnDevportal:
          type: boolean
          example: true
        deployedTime:
          type: string
          format: date-time
          readOnly: true
        successDeployedTime:
          type: string
          format: date-time
          readOnly: true
    APIUndeployRedeploy:
      title: APIUndeployRedeploy Info object with basic API deployment details
      properties:
        name:
          maxLength: 255
          minLength: 1
          type: string
          example: Default
    AuditReport:
      title: Resource for Audit Report
      type: object
      properties:
        report:
          type: string
          description: |
            The API Security Audit Report
        grade:
          type: string
          description: |
            The overall grade of the Security Audit
          example: "27.95"
        numErrors:
          type: integer
          description: |
            The number of errors in the API Definition
          example: 20
        externalApiId:
          type: string
          description: |
            The External API ID
          example: fd21f9f7-3674-49cf-8a83-dca401f635de
    APIProductList:
      title: API Product List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of API Products returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/APIProductInfo'
        pagination:
          $ref: '#/components/schemas/Pagination'
    APIProductInfo:
      title: API Info object with basic API details.
      type: object
      properties:
        id:
          type: string
          description: |
            UUID of the api product
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          description: Name of the API Product
          example: PizzaShackAPIProduct
        context:
          type: string
          example: pizzaproduct
        description:
          type: string
          description: A brief description about the API
          example: This is a simple API for Pizza Shack online pizza delivery store
        provider:
          type: string
          description: |
            If the provider value is not given, the user invoking the API will be used as the provider.
          example: admin
        hasThumbnail:
          type: boolean
          example: true
        state:
          type: string
          description: |
            State of the API product. Only published API products are visible on the Developer Portal
          enum:
            - CREATED
            - PUBLISHED
            - DEPRECATED
            - RETIRED
            - BLOCKED
            - PROTOTYPED
        securityScheme:
          type: array
          description: |
            Types of API security, the current API secured with. It can be either OAuth2 or mutual SSL or both. If
            it is not set OAuth2 will be set as the security for the current API.
          example:
            - oauth2
          items:
            type: string
        gatewayVendor:
          type: string
          example: wso2
    APIProduct:
      title: API Product object
      required:
        - name
      type: object
      properties:
        id:
          type: string
          description: |
            UUID of the api product
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        name:
          maxLength: 50
          minLength: 1
          type: string
          description: Name of the API Product
          example: PizzaShackAPIProduct
        context:
          maxLength: 60
          minLength: 1
          type: string
          example: pizzaproduct
        description:
          type: string
          description: A brief description about the API
          example: This is a simple API for Pizza Shack online pizza delivery store
        provider:
          maxLength: 50
          type: string
          description: |
            If the provider value is not given, the user invoking the API will be used as the provider.
          example: admin
        hasThumbnail:
          type: boolean
          example: false
        state:
          type: string
          description: |
            State of the API product. Only published API products are visible on the Developer Portal
          enum:
            - CREATED
            - PUBLISHED
            - DEPRECATED
            - RETIRED
            - BLOCKED
            - PROTOTYPED
          default: CREATED
        enableSchemaValidation:
          type: boolean
          example: false
        isRevision:
          type: boolean
          example: false
        revisionedApiProductId:
          type: string
          description: |
            UUID of the api product registry artifact
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        revisionId:
          type: integer
          example: 1
        responseCachingEnabled:
          type: boolean
          example: true
        cacheTimeout:
          type: integer
          example: 300
        visibility:
          type: string
          description: The visibility level of the API. Accepts one of the following. PUBLIC, PRIVATE, RESTRICTED.
          example: PUBLIC
          enum:
            - PUBLIC
            - PRIVATE
            - RESTRICTED
          default: PUBLIC
        visibleRoles:
          type: array
          description: The user roles that are able to access the API
          example: []
          items:
            type: string
        visibleTenants:
          type: array
          example: []
          items:
            type: string
        accessControl:
          type: string
          description: |
            Defines whether the API Product is restricted to certain set of publishers or creators or is it visible to all the
            publishers and creators. If the accessControl restriction is none, this API Product can be modified by all the
            publishers and creators, if not it can only be viewable/modifiable by certain set of publishers and creators,
            based on the restriction.
          enum:
            - NONE
            - RESTRICTED
          default: NONE
        accessControlRoles:
          type: array
          description: The user roles that are able to view/modify as API Product publisher or creator.
          example: []
          items:
            type: string
        apiType:
          type: string
          description: The API type to be used. Accepted values are API, APIPRODUCT
          example: APIPRODUCT
          enum:
            - API
            - APIPRODUCT
        transport:
          type: array
          description: |
            Supported transports for the API (http and/or https).
          example:
            - http
            - https
          items:
            type: string
        tags:
          type: array
          example:
            - pizza
            - food
          items:
            type: string
        policies:
          type: array
          example:
            - Unlimited
          items:
            type: string
        apiThrottlingPolicy:
          type: string
          description: The API level throttling policy selected for the particular API Product
          example: Unlimited
        authorizationHeader:
          type: string
          description: |
            Name of the Authorization header used for invoking the API. If it is not set, Authorization header name specified
            in tenant or system level will be used.
          example: Authorization
        securityScheme:
          type: array
          description: |
            Types of API security, the current API secured with. It can be either OAuth2 or mutual SSL or both. If
            it is not set OAuth2 will be set as the security for the current API.
          example:
            - oauth2
          items:
            type: string
        subscriptionAvailability:
          type: string
          description: The subscription availability. Accepts one of the following. CURRENT_TENANT, ALL_TENANTS or SPECIFIC_TENANTS.
          example: CURRENT_TENANT
          enum:
            - CURRENT_TENANT
            - ALL_TENANTS
            - SPECIFIC_TENANTS
          default: ALL_TENANTS
        subscriptionAvailableTenants:
          type: array
          example: []
          items:
            type: string
          x-otherScopes:
            - apim:api_publish
            - apim:api_manage
        additionalProperties:
          type: array
          description: Map of custom properties of API
          items:
            type: object
            properties:
              name:
                type: string
              value:
                type: string
              display:
                type: boolean
        additionalPropertiesMap:
          type: object
          additionalProperties:
            type: object
            properties:
              name:
                type: string
              value:
                type: string
              display:
                type: boolean
                default: false
        monetization:
          $ref: '#/components/schemas/APIMonetizationInfo'
        businessInformation:
          $ref: '#/components/schemas/APIProductBusinessInformation'
        corsConfiguration:
          $ref: '#/components/schemas/APICorsConfiguration'
        createdTime:
          type: string
        lastUpdatedTime:
          type: string
        gatewayVendor:
          title: field to identify gateway vendor
          type: string
          example: wso2
        apis:
          type: array
          description: |
            APIs and resources in the API Product.
          example:
            - name: PizzaShackAPI
              apiId: 01234567-0123-0123-0123-012345678901
              version: "1.0"
              operations:
                - target: /order/{orderId}
                  verb: POST
                  authType: Application & Application User
                  throttlingPolicy: Unlimited
                - target: /menu
                  verb: GET
                  authType: Application & Application User
                  throttlingPolicy: Unlimited
          items:
            $ref: '#/components/schemas/ProductAPI'
        scopes:
          type: array
          example: []
          items:
            $ref: '#/components/schemas/APIScope'
        categories:
          type: array
          description: |
            API categories
          example: []
          items:
            type: string
        workflowStatus:
          type: string
          example: APPROVED
    ProductAPI:
      title: ProductAPI
      required:
        - apiId
      type: object
      properties:
        name:
          type: string
          example: PizzaShackAPI
        apiId:
          type: string
          example: 01234567-0123-0123-0123-012345678901
        version:
          type: string
          example: "1.0"
        operations:
          type: array
          items:
            $ref: '#/components/schemas/APIOperations'
    ResourcePath:
      title: ResourcePath
      required:
        - id
      type: object
      properties:
        id:
          type: integer
          example: 1
        resourcePath:
          type: string
          example: /menu
        httpVerb:
          type: string
          example: GET
    ResourcePathList:
      title: ResourcePath List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of API Resource Paths returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/ResourcePath'
        pagination:
          $ref: '#/components/schemas/Pagination'
    APIProductOutdatedStatus:
      title: APIProduct is outdated status
      type: object
      properties:
        isOutdated:
          type: boolean
          description: |
            Indicates if an API Product is outdated
          example: true
    APIProductBusinessInformation:
      type: object
      properties:
        businessOwner:
          maxLength: 120
          type: string
          example: businessowner
        businessOwnerEmail:
          type: string
          example: <EMAIL>
        technicalOwner:
          maxLength: 120
          type: string
          example: technicalowner
        technicalOwnerEmail:
          type: string
          example: <EMAIL>
    Claim:
      title: Claim
      type: object
      properties:
        name:
          type: string
          example: email
        URI:
          type: string
          example: http://wso2.org/claims/emailaddress
        value:
          type: string
          example: <EMAIL>
    SubscriberInfo:
      title: SubscriberInfo
      type: object
      properties:
        name:
          type: string
          example: admin
        claims:
          type: array
          items:
            $ref: '#/components/schemas/Claim'
    Application:
      title: Application
      required:
        - name
        - throttlingTier
      type: object
      properties:
        applicationId:
          type: string
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          example: CalculatorApp
        subscriber:
          type: string
          example: admin
        throttlingTier:
          type: string
          example: Unlimited
        description:
          type: string
          example: Sample calculator application
        groupId:
          type: string
          example: ""
    ApplicationInfo:
      title: Application info object with basic application details
      type: object
      properties:
        applicationId:
          type: string
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          example: CalculatorApp
        subscriber:
          type: string
          example: admin
        subscriberEmail:
          type: string
          example: <EMAIL>
        description:
          type: string
          example: Sample calculator application
        subscriptionCount:
          type: integer
    DocumentList:
      title: Document List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Documents returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/Document'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Document:
      title: Document
      required:
        - name
        - sourceType
        - type
        - visibility
      type: object
      properties:
        documentId:
          type: string
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        name:
          maxLength: 60
          minLength: 1
          type: string
          example: PizzaShackDoc
        type:
          type: string
          example: HOWTO
          enum:
            - HOWTO
            - SAMPLES
            - PUBLIC_FORUM
            - SUPPORT_FORUM
            - API_MESSAGE_FORMAT
            - SWAGGER_DOC
            - OTHER
        summary:
          maxLength: 32766
          minLength: 1
          type: string
          example: Summary of PizzaShackAPI Documentation
        sourceType:
          type: string
          example: INLINE
          enum:
            - INLINE
            - MARKDOWN
            - URL
            - FILE
        sourceUrl:
          type: string
          readOnly: true
          example: ""
        fileName:
          type: string
          readOnly: true
          example: ""
        inlineContent:
          type: string
          example: This is doc content. This can have many lines.
        otherTypeName:
          type: string
          readOnly: true
          example: ""
        visibility:
          type: string
          example: API_LEVEL
          enum:
            - OWNER_ONLY
            - PRIVATE
            - API_LEVEL
        createdTime:
          type: string
          readOnly: true
        createdBy:
          type: string
          example: admin
        lastUpdatedTime:
          type: string
          readOnly: true
        lastUpdatedBy:
          type: string
          readOnly: true
          example: admin
    GraphQLSchema:
      title: GraphQL Schema
      required:
        - name
      type: object
      properties:
        name:
          type: string
          example: admin--HackerNewsAPI.graphql
        schemaDefinition:
          type: string
    GraphQLQueryComplexityInfo:
      title: GraphQL Query Complexity Info
      type: object
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/GraphQLCustomComplexityInfo'
    GraphQLCustomComplexityInfo:
      title: GraphQL Custom Complexity Info
      required:
        - complexityValue
        - field
        - type
      type: object
      properties:
        type:
          type: string
          description: |
            The type found within the schema of the API
          example: Country
        field:
          type: string
          description: |
            The field which is found under the type within the schema of the API
          example: name
        complexityValue:
          type: integer
          description: |
            The complexity value allocated for the associated field under the specified type
          example: 1
    GraphQLSchemaTypeList:
      title: List of types and corresponding fields of the GraphQL Schema
      type: object
      properties:
        typeList:
          type: array
          items:
            $ref: '#/components/schemas/GraphQLSchemaType'
    GraphQLSchemaType:
      title: Single type and corresponding fields found within the GraphQL Schema
      type: object
      properties:
        type:
          type: string
          description: |
            Type found within the GraphQL Schema
          example: Country
        fieldList:
          type: array
          description: |
            Array of fields under current type
          example:
            - code
            - name
          items:
            type: string
    ThrottlingPolicyList:
      title: Throttling policy list
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Tiers returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/ThrottlingPolicy'
        pagination:
          $ref: '#/components/schemas/Pagination'
    ThrottlingPolicy:
      title: Tier
      required:
        - name
        - requestCount
        - stopOnQuotaReach
        - tierPlan
        - unitTime
      type: object
      properties:
        name:
          type: string
          example: Platinum
        description:
          type: string
          example: Allows 50 request(s) per minute.
        policyLevel:
          type: string
          example: api
          enum:
            - subscription
            - api
        displayName:
          type: string
          example: Platinum
        attributes:
          type: object
          additionalProperties:
            type: string
          description: |
            Custom attributes added to the policy policy
          example: {}
        requestCount:
          type: integer
          description: |
            Maximum number of requests which can be sent within a provided unit time
          format: int64
          example: 50
        dataUnit:
          type: string
          description: |
            Unit of data allowed to be transfered. Allowed values are "KB", "MB" and "GB"
          example: KB
        unitTime:
          type: integer
          format: int64
          example: 60000
        timeUnit:
          type: string
          example: min
        rateLimitCount:
          type: integer
          description: Burst control request count
          example: 10
          default: 0
        rateLimitTimeUnit:
          type: string
          description: Burst control time unit
          example: min
        quotaPolicyType:
          type: string
          description: Default quota limit type
          example: REQUESTCOUNT
          enum:
            - REQUESTCOUNT
            - BANDWIDTHVOLUME
        tierPlan:
          type: string
          description: |
            This attribute declares whether this policy is available under commercial or free
          example: FREE
          enum:
            - FREE
            - COMMERCIAL
        stopOnQuotaReach:
          type: boolean
          description: |
            By making this attribute to false, you are capabale of sending requests
            even if the request count exceeded within a unit time
          example: true
        monetizationProperties:
          type: object
          additionalProperties:
            type: string
          description: Properties of a tier plan which are related to monetization
          example: {}
    SubscriptionPolicyList:
      title: Subscription policy list
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Tiers returned.
          example: 1
        list:
          type: array
          description: |
            Array of SubscriptionPolicies
          items:
            $ref: '#/components/schemas/SubscriptionPolicy'
        pagination:
          $ref: '#/components/schemas/Pagination'
    SubscriptionList:
      title: Subscription List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Subscriptions returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/Subscription'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Subscription:
      title: Subscription
      required:
        - applicationInfo
        - subscriptionId
        - subscriptionStatus
        - throttlingPolicy
      type: object
      properties:
        subscriptionId:
          type: string
          example: 01234567-0123-0123-0123-012345678901
        applicationInfo:
          $ref: '#/components/schemas/ApplicationInfo'
        throttlingPolicy:
          type: string
          example: Unlimited
        versionRange:
          type: string
          example: v1
        subscriptionStatus:
          type: string
          example: BLOCKED
          enum:
            - BLOCKED
            - PROD_ONLY_BLOCKED
            - UNBLOCKED
            - ON_HOLD
            - REJECTED
            - TIER_UPDATE_PENDING
            - DELETE_PENDING
    ThrottlePolicy:
      title: Generic Throttling Policy
      required:
        - policyName
      type: object
      properties:
        policyId:
          type: string
          description: Id of policy
          readOnly: true
          example: 0c6439fd-9b16-3c2e-be6e-1086e0b9aa93
        policyName:
          maxLength: 60
          minLength: 1
          type: string
          description: Name of policy
          example: 30PerMin
        displayName:
          maxLength: 512
          type: string
          description: Display name of the policy
          example: 30PerMin
        description:
          maxLength: 1024
          type: string
          description: Description of the policy
          example: Allows 30 request per minute
        isDeployed:
          type: boolean
          description: Indicates whether the policy is deployed successfully or not.
          default: false
        type:
          type: string
          description: Indicates the type of throttle policy
      discriminator:
        propertyName: type
    GraphQLQuery:
      title: GraphQL Query
      type: object
      properties:
        graphQLMaxComplexity:
          type: integer
          description: Maximum Complexity of the GraphQL query
          example: 400
        graphQLMaxDepth:
          type: integer
          description: Maximum Depth of the GraphQL query
          example: 10
    ThrottleLimitBase:
      title: Throttle Limit Base
      required:
        - timeUnit
        - unitTime
      type: object
      properties:
        timeUnit:
          type: string
          description: Unit of the time. Allowed values are "sec", "min", "hour", "day"
          example: min
        unitTime:
          type: integer
          description: Time limit that the throttling limit applies.
          example: 10
    ThrottleLimit:
      title: Throttle Limit
      required:
        - type
      type: object
      properties:
        type:
          type: string
          description: |
            Type of the throttling limit. Allowed values are "REQUESTCOUNTLIMIT" and "BANDWIDTHLIMIT".
            Please see schemas of "RequestCountLimit" and "BandwidthLimit" throttling limit types in
            Definitions section.
          example: REQUESTCOUNTLIMIT
          enum:
            - REQUESTCOUNTLIMIT
            - BANDWIDTHLIMIT
            - EVENTCOUNTLIMIT
        requestCount:
          $ref: '#/components/schemas/RequestCountLimit'
        bandwidth:
          $ref: '#/components/schemas/BandwidthLimit'
        eventCount:
          $ref: '#/components/schemas/EventCountLimit'
    MonetizationInfo:
      title: API monetization details object
      required:
        - monetizationPlan
        - properties
      type: object
      properties:
        monetizationPlan:
          type: string
          description: Flag to indicate the monetization plan
          example: FixedRate
          enum:
            - FIXEDRATE
            - DYNAMICRATE
        properties:
          type: object
          additionalProperties:
            type: string
          description: Map of custom properties related to each monetization plan
    BandwidthLimit:
      title: Bandwidth Limit object
      allOf:
        - $ref: '#/components/schemas/ThrottleLimitBase'
        - required:
            - dataAmount
            - dataUnit
          type: object
          properties:
            dataAmount:
              type: integer
              description: Amount of data allowed to be transfered
              format: int64
              example: 1000
            dataUnit:
              type: string
              description: Unit of data allowed to be transfered. Allowed values are "KB", "MB" and "GB"
              example: KB
    RequestCountLimit:
      title: Request Count Limit object
      allOf:
        - $ref: '#/components/schemas/ThrottleLimitBase'
        - required:
            - requestCount
          type: object
          properties:
            requestCount:
              type: integer
              description: Maximum number of requests allowed
              format: int64
              example: 30
    EventCountLimit:
      title: Event Count Limit object
      allOf:
        - $ref: '#/components/schemas/ThrottleLimitBase'
        - required:
            - eventCount
          type: object
          properties:
            eventCount:
              type: integer
              description: Maximum number of events allowed
              format: int64
              example: 3000
    SubscriptionPolicy:
      title: Subscription Throttling Policy
      allOf:
        - required:
            - defaultLimit
          type: object
          properties:
            policyId:
              type: integer
              description: Id of policy
              example: 1
            uuid:
              type: string
              description: policy uuid
              example: 0c6439fd-9b16-3c2e-be6e-1086e0b9aa93
            policyName:
              maxLength: 60
              minLength: 1
              type: string
              description: Name of policy
              example: 30PerMin
            displayName:
              maxLength: 512
              type: string
              description: Display name of the policy
              example: 30PerMin
            description:
              maxLength: 1024
              type: string
              description: Description of the policy
              example: Allows 30 request per minute
            isDeployed:
              type: boolean
              description: Indicates whether the policy is deployed successfully or not.
              default: false
            tenantId:
              type: integer
              description: Throttling policy tenant domain id
              example: -1234
            tenantDomain:
              type: string
              description: Throttling policy tenant domain
              example: carbon.super
            defaultLimit:
              $ref: '#/components/schemas/ThrottleLimit'
            rateLimitCount:
              type: integer
              description: Burst control request count
              example: 10
            rateLimitTimeUnit:
              type: string
              description: Burst control time unit
              example: min
            subscriberCount:
              type: integer
              description: Number of subscriptions allowed
              example: 10
            customAttributes:
              type: array
              description: |
                Custom attributes added to the Subscription Throttling Policy
              example: []
              items:
                $ref: '#/components/schemas/CustomAttribute'
            stopOnQuotaReach:
              type: boolean
              description: |
                This indicates the action to be taken when a user goes beyond the allocated quota. If checked, the user's requests will be dropped. If unchecked, the requests will be allowed to pass through.
              default: false
            billingPlan:
              type: string
              description: |
                define whether this is Paid or a Free plan. Allowed values are FREE or COMMERCIAL.
              example: FREE
            permissions:
              $ref: '#/components/schemas/SubscriptionThrottlePolicyPermission'
    CustomAttribute:
      title: Name-Value pair
      required:
        - name
        - value
      type: object
      properties:
        name:
          type: string
          description: Name of the custom attribute
          example: customAttr1
        value:
          type: string
          description: Value of the custom attribute
          example: value1
    SubscriptionThrottlePolicyPermission:
      title: SubscriptionThrottlePolicyPermission
      required:
        - permissionType
        - roles
      type: object
      properties:
        permissionType:
          type: string
          example: deny
          enum:
            - ALLOW
            - DENY
        roles:
          type: array
          example:
            - Internal/everyone
          items:
            type: string
    APIMonetizationUsage:
      title: API monetization usage object
      type: object
      properties:
        properties:
          type: object
          additionalProperties:
            type: string
          description: Map of custom properties related to monetization usage
    APIRevenue:
      title: API revenue data object
      type: object
      properties:
        properties:
          type: object
          additionalProperties:
            type: string
          description: Map of custom properties related to API revenue
    MediationPolicy:
      title: Mediation Policy
      required:
        - name
      type: object
      properties:
        id:
          type: string
          example: 69ea3fa6-55c6-472e-896d-e449dd34a824
        name:
          type: string
          example: log_in_message
        type:
          type: string
          example: in
        shared:
          type: boolean
          example: true
    Error:
      title: Error object returned with 4XX HTTP Status
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: integer
          format: int64
        message:
          type: string
          description: Error message.
        description:
          type: string
          description: |
            A detail description about the error message.
        moreInfo:
          type: string
          description: |
            Preferably an url with more details about the error.
        error:
          type: array
          description: |
            If there are more than one error list them out.
            For example, list out validation errors by each field.
          items:
            $ref: '#/components/schemas/ErrorListItem'
    ErrorListItem:
      title: Description of individual errors that may have occurred during a request.
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: string
        message:
          type: string
          description: |
            Description about individual errors occurred
        description:
          type: string
          description: |
            A detail description about the error message.
    Environment:
      title: Environment
      required:
        - id
        - name
        - serverUrl
        - showInApiConsole
        - type
      type: object
      properties:
        id:
          type: string
        name:
          type: string
          example: default
        displayName:
          type: string
          example: Default
        type:
          type: string
          example: hybrid
        serverUrl:
          type: string
          example: https://localhost:9443/services/
        provider:
          type: string
          example: wso2
        showInApiConsole:
          type: boolean
          example: true
        vhosts:
          type: array
          items:
            $ref: '#/components/schemas/VHost'
        endpointURIs:
          type: array
          items:
            $ref: '#/components/schemas/GatewayEnvironmentProtocolURI'
        additionalProperties:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalProperty'
    EnvironmentList:
      title: Environment List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Environments returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/Environment'
    AdditionalProperty:
      title: Additional Gateway Properties
      type: object
      properties:
        key:
          type: string
          example: Organization
        value:
          type: string
          example: wso2
    VHost:
      title: Virtual Host
      type: object
      properties:
        host:
          type: string
          example: mg.wso2.com
        httpContext:
          type: string
          example: pets
        httpPort:
          type: integer
          example: 80
        httpsPort:
          type: integer
          example: 443
        wsPort:
          type: integer
          example: 9099
        wssPort:
          type: integer
          example: 8099
        websubHttpPort:
          type: integer
          example: 9021
        websubHttpsPort:
          type: integer
          example: 8021
    FileInfo:
      title: File Information including meta data
      type: object
      properties:
        relativePath:
          type: string
          description: relative location of the file (excluding the base context and host of the Publisher API)
          example: apis/01234567-0123-0123-0123-012345678901/thumbnail
        mediaType:
          type: string
          description: media-type of the file
          example: image/jpeg
    APIMaxTps:
      type: object
      properties:
        production:
          type: integer
          format: int64
          example: 1000
        sandbox:
          type: integer
          format: int64
          example: 1000
    APIBusinessInformation:
      type: object
      properties:
        businessOwner:
          maxLength: 120
          type: string
          example: businessowner
        businessOwnerEmail:
          type: string
          example: <EMAIL>
        technicalOwner:
          maxLength: 120
          type: string
          example: technicalowner
        technicalOwnerEmail:
          type: string
          example: <EMAIL>
    WebsubSubscriptionConfiguration:
      type: object
      properties:
        enable:
          type: boolean
          description: Toggle enable WebSub subscription configuration
          default: false
        secret:
          type: string
          description: Secret key to be used for subscription
        signingAlgorithm:
          type: string
          description: The algorithm used for signing
        signatureHeader:
          type: string
          description: The header uses to send the signature
    APICorsConfiguration:
      type: object
      properties:
        corsConfigurationEnabled:
          type: boolean
          default: false
        accessControlAllowOrigins:
          type: array
          items:
            type: string
        accessControlAllowCredentials:
          type: boolean
          default: false
        accessControlAllowHeaders:
          type: array
          items:
            type: string
        accessControlAllowMethods:
          type: array
          items:
            type: string
      description: |
        CORS configuration for the API
    Endpoint:
      title: Endpoints
      type: object
      properties:
        id:
          type: string
          description: |
            UUID of the Endpoint entry
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          description: |
            name of the Endpoint entry
          example: Endpoint 1
        endpointConfig:
          type: object
          properties:
            endpointType:
              type: string
              example: FAIL_OVER
              enum:
                - SINGLE
                - LOAD_BALANCED
                - FAIL_OVER
            list:
              type: array
              items:
                $ref: '#/components/schemas/EndpointConfig'
        endpointSecurity:
          type: object
          properties:
            enabled:
              type: boolean
              example: false
            type:
              type: string
              example: basic
            username:
              type: string
              example: basic
            password:
              type: string
              example: basic
        maxTps:
          type: integer
          description: Endpoint max tps
          format: int64
          example: 1000
        type:
          type: string
          example: http
    EndpointConfig:
      title: Endpoint Configuration
      type: object
      properties:
        url:
          type: string
          description: |
            Service url of the endpoint
          example: http://localhost:8280
        timeout:
          type: string
          description: |
            Time out of the endpoint
          example: "1000"
        attributes:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: Suspension time
              value:
                type: string
                example: 2s
    EndpointList:
      title: Endpoint List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Endpoints returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/Endpoint'
    Scope:
      title: Scope
      required:
        - name
      type: object
      properties:
        id:
          type: string
          description: |
            UUID of the Scope. Valid only for shared scopes.
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        name:
          maxLength: 255
          minLength: 1
          type: string
          description: |
            name of Scope
          example: apim:api_view
        displayName:
          maxLength: 255
          type: string
          description: |
            display name of Scope
          example: api_view
        description:
          maxLength: 512
          type: string
          description: |
            description of Scope
          example: This Scope can used to view Apis
        bindings:
          type: array
          description: |
            role bindings list of the Scope
          example:
            - admin
            - Internal/creator
            - Internal/publisher
          items:
            type: string
        usageCount:
          type: integer
          description: |
            usage count of Scope
          readOnly: true
          example: 3
    SharedScopeUsage:
      title: SharedScopeUsage
      required:
        - id
        - name
      type: object
      properties:
        id:
          type: string
          description: |
            UUID of the Scope. Valid only for shared scopes.
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          description: |
            name of Scope
          example: apim:api_view
        usedApiList:
          type: array
          description: |
            API list which have used the shared scope
          items:
            $ref: '#/components/schemas/SharedScopeUsedAPIInfo'
    SharedScopeUsedAPIInfo:
      title: API object using shared scope
      required:
        - context
        - name
        - version
      type: object
      properties:
        name:
          type: string
          example: CalculatorAPI
        context:
          type: string
          example: CalculatorAPI
        version:
          type: string
          example: 1.0.0
        provider:
          type: string
          description: |
            If the provider value is not given user invoking the api will be used as the provider.
          example: admin
        usedResourceList:
          type: array
          description: |
            Resource list which have used the shared scope within this API
          items:
            $ref: '#/components/schemas/SharedScopeUsedAPIResourceInfo'
    SharedScopeUsedAPIResourceInfo:
      title: API resource object using shared scope
      type: object
      properties:
        target:
          type: string
          example: /add
        verb:
          type: string
          example: POST
    APIScope:
      title: APIScope
      required:
        - scope
      type: object
      properties:
        scope:
          $ref: '#/components/schemas/Scope'
        shared:
          type: boolean
          description: |
            States whether scope is shared. This will not be honored when updating/adding scopes to APIs or when
            adding/updating Shared Scopes.
          example: true
    APIOperations:
      title: Operation
      type: object
      properties:
        id:
          type: string
          example: postapiresource
        target:
          type: string
          example: /order/{orderId}
        verb:
          type: string
          example: POST
        authType:
          type: string
          example: Application & Application User
          default: Any
        throttlingPolicy:
          type: string
          example: Unlimited
        throttlingLimit:
          $ref: '#/components/schemas/ThrottlingLimit'
        scopes:
          type: array
          example: []
          items:
            type: string
        usedProductIds:
          type: array
          example: []
          items:
            type: string
        amznResourceName:
          type: string
          example: ""
        amznResourceTimeout:
          type: integer
        payloadSchema:
          type: string
          example: ""
        uriMapping:
          type: string
          example: ""
        operationPolicies:
          $ref: '#/components/schemas/APIOperationPolicies'
    ScopeList:
      title: Scope List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Scopes returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/Scope'
        pagination:
          $ref: '#/components/schemas/Pagination'
    ExternalStore:
      title: External Store
      type: object
      properties:
        id:
          type: string
          description: |
            The external store identifier, which is a unique value.
          example: Store123#
        displayName:
          type: string
          description: |
            The name of the external API Store that is displayed in the Publisher UI.
          example: UKStore
        type:
          type: string
          description: |
            The type of the Store. This can be a WSO2-specific API Store or an external one.
          example: wso2
        endpoint:
          type: string
          description: |
            The endpoint URL of the external store
          example: http://localhost:9764/store
    APIExternalStore:
      title: API External Store
      type: object
      properties:
        id:
          type: string
          description: |
            The external store identifier, which is a unique value.
          example: Store123#
        lastUpdatedTime:
          type: string
          description: |
            The recent timestamp which a given API is updated in the external store.
          example: 2019-09-09T13:57:16.229
    APIExternalStoreList:
      title: API External Store List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of external stores returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/APIExternalStore'
    ExternalStoreList:
      title: External Store List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of external stores returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/ExternalStore'
    Certificates:
      title: Certificates
      type: object
      properties:
        count:
          type: integer
          example: 1
        certificates:
          type: array
          items:
            $ref: '#/components/schemas/CertMetadata'
        pagination:
          $ref: '#/components/schemas/Pagination'
      description: Representation of a list of certificates
    CertMetadata:
      title: Certificate
      type: object
      properties:
        alias:
          type: string
          example: wso2carbon
        endpoint:
          type: string
          example: www.abc.com
      description: Representation of the details of a certificate
    CertificateInfo:
      title: Certificate information
      type: object
      properties:
        status:
          type: string
          example: Active
        validity:
          $ref: '#/components/schemas/CertificateValidity'
        version:
          type: string
          example: V3
        subject:
          type: string
          example: CN=wso2.com, OU=wso2, O=wso2, L=Colombo, ST=Western, C=LK
    CertificateValidity:
      title: Certificate Valid period
      type: object
      properties:
        from:
          type: string
          example: 12-12-2017
        to:
          type: string
          example: 01-01-2019
    ClientCertificates:
      title: Client Certificates
      type: object
      properties:
        count:
          type: integer
          example: 1
        certificates:
          type: array
          items:
            $ref: '#/components/schemas/ClientCertMetadata'
        pagination:
          $ref: '#/components/schemas/Pagination'
      description: Representation of a list of client certificates
    ClientCertMetadata:
      title: Client certificate meta data
      type: object
      properties:
        alias:
          type: string
          example: wso2carbon
        apiId:
          type: string
          example: 64eca60b-2e55-4c38-8603-e9e6bad7d809
        tier:
          type: string
          example: Gold
      description: Meta data of certificate
    LifecycleState:
      title: Lifecycle State
      type: object
      properties:
        state:
          type: string
          example: Created
        checkItems:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: Deprecate old versions after publishing the API
              value:
                type: boolean
                example: false
              requiredStates:
                type: array
                example: []
                items:
                  type: string
        availableTransitions:
          type: array
          items:
            type: object
            properties:
              event:
                type: string
                example: Publish
              targetState:
                type: string
                example: Published
    LifecycleHistory:
      title: Lifecycle history item list
      type: object
      properties:
        count:
          type: integer
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/LifecycleHistoryItem'
    LifecycleHistoryItem:
      title: Lifecycle history item
      type: object
      properties:
        previousState:
          type: string
          example: Created
        postState:
          type: string
          example: Published
        user:
          type: string
          example: admin
        updatedTime:
          type: string
          format: dateTime
          example: 2019-02-31T23:59:60Z
    WorkflowResponse:
      title: workflow Response
      required:
        - workflowStatus
      type: object
      properties:
        workflowStatus:
          type: string
          description: |
            This attribute declares whether this workflow task is approved or rejected.
          example: APPROVED
          enum:
            - CREATED
            - APPROVED
            - REJECTED
            - REGISTERED
        jsonPayload:
          type: string
          description: |
            Attributes that returned after the workflow execution
          example: null
        lifecycleState:
          $ref: '#/components/schemas/LifecycleState'
    OpenAPIDefinitionValidationResponse:
      title: OpenAPI Definition Validation Response
      required:
        - isValid
      type: object
      properties:
        isValid:
          type: boolean
          description: |
            This attribute declares whether this definition is valid or not.
          example: true
        content:
          type: string
          description: |
            OpenAPI definition content.
        info:
          type: object
          properties:
            name:
              type: string
              example: PetStore
            version:
              type: string
              example: 1.0.0
            context:
              type: string
              example: /petstore
            description:
              type: string
              example: A sample API that uses a petstore as an example to demonstrate swagger-2.0 specification
            openAPIVersion:
              type: string
              example: 3.0.0
            endpoints:
              type: array
              description: |
                contains host/servers specified in the OpenAPI file/URL
              items:
                type: string
                example: https://localhost:9443/am/sample/pizzashack/v3/api/
            scopeUsage:
              type: array
              items:
                $ref: '#/components/schemas/ScopeUsageItem'
          description: |
            API definition information
        errors:
          type: array
          description: |
            If there are more than one error list them out.
            For example, list out validation errors by each field.
          items:
            $ref: '#/components/schemas/ErrorListItem'
    ScopeUsageItem:
      title: scope usage response
      required:
        - apiName
        - scope
        - version
      type: object
      properties:
        scope:
          type: string
          description: Name of the scope.
          example: read:pets
        apiId:
          type: string
          description: UUID of the API.
          example: 653f4fdae1826026ee73daba
        apiName:
          type: string
          description: Name of the API.
          example: DefaultAPI
        apiVersion:
          type: string
          description: Version of the API.
          example: v1.0
    WSDLValidationResponse:
      title: WSDL Definition Validation Response
      required:
        - isValid
      type: object
      properties:
        isValid:
          type: boolean
          description: |
            This attribute declares whether this definition is valid or not.
          example: true
        errors:
          type: array
          description: |
            If there are more than one error list them out.
            For example, list out validation errors by each field.
          items:
            $ref: '#/components/schemas/ErrorListItem'
        wsdlInfo:
          type: object
          properties:
            version:
              type: string
              description: |
                WSDL version
              example: "1.1"
            endpoints:
              type: array
              description: |
                A list of endpoints the service exposes
              items:
                type: object
                properties:
                  name:
                    type: string
                    description: Name of the endpoint
                    example: StockQuoteSoap
                  location:
                    type: string
                    description: Endpoint URL
                    example: http://www.webservicex.net/stockquote.asmx
          description: Summary of the WSDL including the basic information
    GraphQLValidationResponse:
      title: GraphQL API definition validation Response
      required:
        - errorMessage
        - isValid
      type: object
      properties:
        isValid:
          type: boolean
          description: |
            This attribute declares whether this definition is valid or not.
          example: true
        errorMessage:
          type: string
          description: |
            This attribute declares the validation error message
        graphQLInfo:
          type: object
          properties:
            operations:
              type: array
              items:
                $ref: '#/components/schemas/APIOperations'
            graphQLSchema:
              $ref: '#/components/schemas/GraphQLSchema'
          description: Summary of the GraphQL including the basic information
    ApiEndpointValidationResponse:
      title: API Endpoint url validation response
      required:
        - statusCode
        - statusMessage
      type: object
      properties:
        statusCode:
          type: integer
          description: HTTP status code
          example: 200
        statusMessage:
          type: string
          description: string
          example: OK
        error:
          type: string
          description: |
            If an error occurs, the error message will be set to this property.
            If not, this will remain null.
          example: null
    ApiValidationResponse:
      title: API validation response
      required:
        - apiExists
      type: object
      properties:
        apiExists:
          type: boolean
          description: API exists or not
          example: true
    ThreatProtectionPolicyList:
      title: Threat Protection Policy List
      type: object
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/ThreatProtectionPolicy'
    ThreatProtectionPolicy:
      title: Threat Protection Policy Schema
      required:
        - name
        - policy
        - type
      type: object
      properties:
        uuid:
          type: string
          description: Policy ID
        name:
          type: string
          description: Name of the policy
        type:
          type: string
          description: Type of the policy
        policy:
          type: string
          description: policy as a json string
    SearchResultList:
      title: Unified Search Result List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of results returned.
          example: 1
        list:
          type: array
          items:
            type: object
        pagination:
          $ref: '#/components/schemas/Pagination'
    SearchResult:
      title: Search Result
      required:
        - name
      type: object
      properties:
        id:
          type: string
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          example: TestAPI
        displayName:
          type: string
          example: TestAPI
        type:
          type: string
          example: API
          enum:
            - DOC
            - API
            - APIProduct
        transportType:
          type: string
          description: Accepted values are HTTP, WS, SOAPTOREST, GRAPHQL
      discriminator:
        propertyName: name
    APISearchResult:
      title: API Result
      allOf:
        - $ref: '#/components/schemas/SearchResult'
        - type: object
          properties:
            description:
              type: string
              description: A brief description about the API
              example: A calculator API that supports basic operations
            context:
              type: string
              description: A string that represents the context of the user's request
              example: CalculatorAPI
            contextTemplate:
              type: string
              description: The templated context of the API
              example: CalculatorAPI/{version}
            version:
              type: string
              description: The version of the API
              example: 1.0.0
            provider:
              type: string
              description: |
                If the provider value is not given, the user invoking the API will be used as the provider.
              example: admin
            status:
              type: string
              description: This describes in which status of the lifecycle the API is
              example: CREATED
            thumbnailUri:
              type: string
              example: /apis/01234567-0123-0123-0123-012345678901/thumbnail
            advertiseOnly:
              type: boolean
              example: true
    APIProductSearchResult:
      title: API Result
      allOf:
        - $ref: '#/components/schemas/SearchResult'
        - type: object
          properties:
            description:
              type: string
              description: A brief description about the API
              example: A calculator API that supports basic operations
            context:
              type: string
              description: A string that represents the context of the user's request
              example: CalculatorAPI
            version:
              type: string
              description: The version of the API Product
              example: 1.0.0
            provider:
              type: string
              description: |
                If the provider value is not given, the user invoking the API will be used as the provider.
              example: admin
            status:
              type: string
              description: This describes in which status of the lifecycle the APIPRODUCT is
              example: PUBLISHED
            thumbnailUri:
              type: string
              example: /apis/01234567-0123-0123-0123-012345678901/thumbnail
    APIMonetizationInfo:
      title: API monetization object
      required:
        - enabled
      type: object
      properties:
        enabled:
          type: boolean
          description: Flag to indicate the monetization status
          example: true
        properties:
          type: object
          additionalProperties:
            type: string
          description: Map of custom properties related to monetization
    DocumentSearchResult:
      title: Document Result
      allOf:
        - $ref: '#/components/schemas/SearchResult'
        - type: object
          properties:
            docType:
              type: string
              example: HOWTO
              enum:
                - HOWTO
                - SAMPLES
                - PUBLIC_FORUM
                - SUPPORT_FORUM
                - API_MESSAGE_FORMAT
                - SWAGGER_DOC
                - OTHER
            summary:
              type: string
              example: Summary of Calculator Documentation
            sourceType:
              type: string
              example: INLINE
              enum:
                - INLINE
                - URL
                - FILE
                - MARKDOWN
            sourceUrl:
              type: string
              example: ""
            otherTypeName:
              type: string
              example: ""
            visibility:
              type: string
              example: API_LEVEL
              enum:
                - OWNER_ONLY
                - PRIVATE
                - API_LEVEL
            apiName:
              type: string
              description: The name of the associated API
              example: TestAPI
            apiDisplayName:
              type: string
              description: The display name of the associated API
              example: TestAPI
            apiVersion:
              type: string
              description: The version of the associated API
              example: 1.0.0
            apiProvider:
              type: string
              example: admin
            apiUUID:
              type: string
            associatedType:
              type: string
    MockResponsePayloadList:
      title: Mock Response Payload list
      type: object
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/MockResponsePayloadInfo'
    MockResponsePayloadInfo:
      title: Mock Response Payload info object
      type: object
      properties:
        path:
          type: string
          description: path of the resource
          example: /menu
        content:
          type: string
          description: new modified code
          example: "var accept = \"\\\"\"+mc.getProperty('AcceptHeader')+\"\\\"\";\nvar responseCode = mc.getProperty('query.param.responseCode');\nvar responseCodeStr = \"\\\"\"+responseCode+\"\\\"\";\nvar responses = [];\n\nif (!responses[200]) {\n responses [200] = [];\n}\nresponses[200][\"application/json\"] = \n[ {\n  \"price\" : \"string\",\n  \"description\" : \"string\",\n  \"name\" : \"string\",\n  \"image\" : \"string\"\n} ]\n\n/*if (!responses[304]) {\n  responses[304] = [];\n}\nresponses[304][\"application/(json or xml)\"] = {}/<>*/\n\nif (!responses[406]) {\n responses [406] = [];\n}\nresponses[406][\"application/json\"] = \n{\n  \"message\" : \"string\",\n  \"error\" : [ {\n    \"message\" : \"string\",\n    \"code\" : 0\n  } ],\n  \"description\" : \"string\",\n  \"code\" : 0,\n  \"moreInfo\" : \"string\"\n}\n\nresponses[501] = [];\nresponses[501][\"application/json\"] = {\n\"code\" : 501,\n\"description\" : \"Not Implemented\"}\nresponses[501][\"application/xml\"] = <response><code>501</code><description>Not Implemented</description></response>;\n\nif (!responses[responseCode]) {\n responseCode = 501;\n}\n\nif (responseCode == null) {\n responseCode = 200;\n responseCodeStr = \"200\";\n}\n\nif (accept == null || !responses[responseCode][accept]) {\n accept = \"application/json\";\n}\n\nif (accept === \"application/json\") {\n mc.setProperty('CONTENT_TYPE', 'application/json');\n mc.setProperty('HTTP_SC', responseCodeStr);\n mc.setPayloadJSON(responses[responseCode][\"application/json\"]);\n} else if (accept === \"application/xml\") {\n mc.setProperty('CONTENT_TYPE', 'application/xml');\n mc.setProperty('HTTP_SC', responseCodeStr);\n mc.setPayloadXML(responses[responseCode][\"application/xml\"]);\n}"
        verb:
          type: string
          example: POST
    ResourcePolicyList:
      title: Resource policy List
      type: object
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/ResourcePolicyInfo'
        count:
          type: integer
          description: |
            Number of policy resources returned.
          example: 1
    ResourcePolicyInfo:
      title: Resource policy Info object with conversion policy resource details.
      type: object
      properties:
        id:
          type: string
          description: |
            UUID of the resource policy registry artifact
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        httpVerb:
          type: string
          description: HTTP verb used for the resource path
          example: get
        resourcePath:
          type: string
          description: A string that represents the resource path of the api for the related resource policy
          example: checkPhoneNumber
        content:
          type: string
          description: The resource policy content
          example: <header description="SOAPAction" name="SOAPAction" scope="transport" value="http://ws.cdyne.com/PhoneVerify/query/CheckPhoneNumber"/>
    Settings:
      title: SettingsDTO
      type: object
      properties:
        devportalUrl:
          type: string
          description: The Developer Portal URL
          example: https://localhost:9443/devportal
        environment:
          type: array
          items:
            $ref: '#/components/schemas/Environment'
        scopes:
          type: array
          example:
            - apim:api_create
            - apim:api_manage
            - apim:api_publish
          items:
            type: string
        monetizationAttributes:
          type: array
          example: []
          items:
            $ref: '#/components/schemas/MonetizationAttribute'
        securityAuditProperties:
          type: object
          properties: {}
        externalStoresEnabled:
          type: boolean
          description: |
            Is External Stores configuration enabled
          example: true
        docVisibilityEnabled:
          type: boolean
          description: |
            Is Document Visibility configuration enabled
          example: false
        crossTenantSubscriptionEnabled:
          type: boolean
          description: |
            Is Cross Tenant Subscriptions Enabled
          example: false
          default: false
        authorizationHeader:
          type: string
          description: Authorization Header
          example: authorization
    SecurityAuditAttribute:
      title: SecurityAuditAttributeDTO
      type: object
      properties:
        isGlobal:
          type: boolean
          example: false
        overrideGlobal:
          type: boolean
          example: false
        apiToken:
          type: string
          example: b1267ytf-b7gc-4aee-924d-ece81241efec
        collectionId:
          type: string
          example: 456ef957-5a79-449f-83y3-9027945d3c60
        baseUrl:
          type: string
    WSDLInfo:
      title: WSDL information of the API. This is only available if the API is a SOAP API.
      type: object
      properties:
        type:
          type: string
          description: Indicates whether the WSDL is a single WSDL or an archive in ZIP format
          enum:
            - WSDL
            - ZIP
    Pagination:
      title: Pagination
      type: object
      properties:
        offset:
          type: integer
          example: 0
        limit:
          type: integer
          example: 1
        total:
          type: integer
          example: 10
        next:
          type: string
          description: |
            Link to the next subset of resources qualified.
            Empty if no more resources are to be returned.
        previous:
          type: string
          description: |
            Link to the previous subset of resources qualified.
            Empty if current subset is the first subset returned.
    MonetizationAttribute:
      title: Monetization attribute object
      type: object
      properties:
        required:
          type: boolean
          description: |
            Is attribute required
          example: true
        name:
          type: string
          description: |
            Name of the attribute
        displayName:
          type: string
          description: |
            Display name of the attribute
        description:
          type: string
          description: |
            Description of the attribute
        hidden:
          type: boolean
          description: |
            Is attribute hidden
        default:
          type: string
          description: |
            Default value of the attribute
    Tenant:
      title: Tenant
      type: object
      properties:
        domain:
          type: string
          description: tenant domain
          example: wso2.com
        status:
          type: string
          description: current status of the tenant active/inactive
          example: active
    TenantList:
      title: Tenant list
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of tenants returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/Tenant'
        pagination:
          $ref: '#/components/schemas/Pagination'
    AdvertiseInfo:
      title: API Advertise info object with advertise details
      type: object
      properties:
        advertised:
          type: boolean
          example: true
        apiExternalProductionEndpoint:
          type: string
          example: https://localhost:9443/devportal
        apiExternalSandboxEndpoint:
          type: string
          example: https://localhost:9443/devportal
        originalDevPortalUrl:
          type: string
          example: https://localhost:9443/devportal
        apiOwner:
          type: string
          example: admin
        vendor:
          type: string
          enum:
            - WSO2
            - AWS
          default: WSO2
    APICategory:
      title: API Category
      required:
        - name
      type: object
      properties:
        id:
          type: string
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          example: Finance
        description:
          type: string
          example: Finance related APIs
    APICategoryList:
      title: API Category List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of API categories returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/APICategory'
    KeyManagerInfo:
      title: Key Manager Info
      required:
        - name
        - type
      type: object
      properties:
        id:
          type: string
          example: 01234567-0123-0123-0123-012345678901
        name:
          type: string
          example: WSO2 IS
        displayName:
          type: string
          description: |
            display name of Keymanager
          example: Keymanager1
        type:
          type: string
          example: IS
        description:
          type: string
          example: This is a key manager for Developers
        enabled:
          type: boolean
          example: true
        additionalProperties:
          type: array
          items:
            type: object
            properties: {}
        tokenEndpoint:
          type: string
          example: https://localhost:9443/oauth2/token
        authorizeEndpoint:
          type: string
          example: https://localhost:9443/oauth2/authorize
        issuer:
          type: string
          example: https://localhost:9443/oauth2/token
        logoutEndpoint:
          type: string
          example: https://localhost:9443/oauth2/logout
        wellKnownEndpoint:
          type: string
          example: https://localhost:9443/oauth2/.well-known/openid-configuration
    KeyManagerList:
      title: Key Manager List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of Key managers returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/KeyManagerInfo'
    APIKey:
      title: API Key details to invoke APIs
      type: object
      properties:
        apikey:
          type: string
          description: API Key
          example: eyJoZWxsbyI6IndvcmxkIn0=.eyJ3c28yIjoiYXBpbSJ9.eyJ3c28yIjoic2lnbmF0dXJlIn0=
        validityTime:
          type: integer
          format: int32
          example: 3600
    EnvironmentProperties:
      title: Envionment specific API properties request body
      type: object
      additionalProperties:
        type: string
      example:
        productionEndpoint: https://localhost:9443/pizzashack/v3/api/
        sandboxEndpoint: https://localhost:9443/pizzashack/v3/api/
    AsyncAPISpecificationValidationResponse:
      title: AsyncAPI Specification Validation Response
      required:
        - isValid
      type: object
      properties:
        isValid:
          type: boolean
          description: This attribute declares whether this definition is valid or not.
          example: true
        content:
          type: string
          description: AsyncAPI specification content
        info:
          type: object
          properties:
            name:
              type: string
              example: Streetlights
            version:
              type: string
              example: 1.0.0
            context:
              type: string
              example: /streetlights
            description:
              type: string
              example: A sample API that uses a streetlights as an example to demonstrate AsyncAPI specifications
            asyncAPIVersion:
              type: string
              example: "2.0"
            protocol:
              type: string
              example: WEBSUB
            endpoints:
              type: array
              description: contains host/servers specified in the AsyncAPI file/URL
              items:
                type: string
                example: https://localhost:9443/am/sample/pizzashack/v3/api/
            gatewayVendor:
              type: string
              example: wso2
            asyncTransportProtocols:
              type: array
              description: contains available transports for an async API
              items:
                type: string
                example: http
          description: API definition information
        errors:
          type: array
          description: If there are more than one error list them out. For example, list out validation error by each field.
          items:
            $ref: '#/components/schemas/ErrorListItem'
    OperationPolicy:
      title: API Operation Policy
      required:
        - policyName
      type: object
      properties:
        policyName:
          type: string
        policyVersion:
          type: string
          default: v1
        policyId:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: object
    APIOperationPolicies:
      title: API Operation Level Policies
      properties:
        request:
          type: array
          items:
            $ref: '#/components/schemas/OperationPolicy'
        response:
          type: array
          items:
            $ref: '#/components/schemas/OperationPolicy'
        fault:
          type: array
          items:
            $ref: '#/components/schemas/OperationPolicy'
    GatewayEnvironmentProtocolURI:
      title: Gateway Environment protocols and URIs
      required:
        - endpointURI
        - protocol
      type: object
      properties:
        protocol:
          type: string
          example: default
        endpointURI:
          type: string
          example: default
    OperationPolicyDataList:
      title: Operation policy List
      type: object
      properties:
        count:
          type: integer
          description: |
            Number of operation policies returned.
          example: 1
        list:
          type: array
          items:
            $ref: '#/components/schemas/OperationPolicyData'
        pagination:
          $ref: '#/components/schemas/Pagination'
    OperationPolicyData:
      title: Operation Policy Data
      type: object
      properties:
        category:
          type: string
          example: Mediation
        id:
          type: string
          example: 121223q41-24141-124124124-12414
        name:
          type: string
          example: removeHeaderPolicy
        displayName:
          type: string
          example: Remove Header Policy
        version:
          type: string
          example: 0.0.1
        description:
          type: string
          example: With this policy, user can add a new header to the request
        applicableFlows:
          type: array
          items:
            type: string
            example: in
        supportedGateways:
          type: array
          items:
            type: string
            example: Synapse
        supportedApiTypes:
          type: array
          items:
            type: string
            example: REST
        isAPISpecific:
          type: boolean
          example: true
        md5:
          type: string
          example: 121223q41-24141-124124124-12414
        policyAttributes:
          type: array
          items:
            $ref: '#/components/schemas/OperationPolicySpecAttribute'
    OperationPolicySpecAttribute:
      title: OperationPolicySpecAttribute
      type: object
      properties:
        name:
          type: string
          description: Name of the attibute
          example: headerName
        displayName:
          type: string
          description: Display name of the attibute
          example: Header Name
        description:
          type: string
          description: Description of the attibute
          example: Name of the header to be removed
        validationRegex:
          type: string
          description: UI validation regex for the attibute
          example: /^[a-z\s]{0,255}$/i
        type:
          type: string
          description: Type of the attibute
          example: string
        required:
          type: boolean
          description: Is this attibute mandetory for the policy
          example: true
        defaultValue:
          type: string
          description: Default value for the attribute
          example: "true"
        allowedValues:
          type: array
          description: If the attribute type is enum, this array should contain all the possible values for the enum.
          items:
            type: string
            example: '["GET","POST","PUT"]'
    ThrottlingLimit:
      title: throttlingLimit
      required:
        - unit
      type: object
      properties:
        requestCount:
          type: integer
          example: 10000
        unit:
          type: string
          enum:
            - SECOND
            - MINUTE
            - HOUR
            - DAY
    APIKeyRevokeRequest:
      title: API Key revoke request object
      type: object
      properties:
        apiKey:
          type: string
          description: API Key to revoke
          example: eyJoZWxsbyI6IndvcmxkIn0=.eyJ3c28yIjoiYXBpbSJ9.eyJ3c28yIjoic2lnbmF0dXJlIn0=
  responses:
    BadRequest:
      description: Bad Request. Invalid request or validation error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Bad Request
            description: Invalid request or validation error
            moreInfo: ""
            error: []
    Conflict:
      description: Conflict. Specified resource already exists.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 409
            message: Conflict
            description: Specified resource already exists
            moreInfo: ""
            error: []
    Forbidden:
      description: Forbidden. The request must be conditional but no condition has been specified.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 403
            message: Forbidden
            description: The request must be conditional but no condition has been specified
            moreInfo: ""
            error: []
    InternalServerError:
      description: Internal Server Error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 500
            message: Internal Server Error
            description: The server encountered an internal error. Please contact administrator.
            moreInfo: ""
            error: []
    NotAcceptable:
      description: Not Acceptable. The requested media type is not supported.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 406
            message: Not Acceptable
            description: The requested media type is not supported
            moreInfo: ""
            error: []
    NotFound:
      description: Not Found. The specified resource does not exist.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 404
            message: Not Found
            description: The specified resource does not exist
            moreInfo: ""
            error: []
    PreconditionFailed:
      description: Precondition Failed. The request has not been performed because one of the preconditions is not met.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 412
            message: Precondition Failed
            description: The request has not been performed because one of the preconditions is not met
            moreInfo: ""
            error: []
    Unauthorized:
      description: Unauthorized. The user is not authorized.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 401
            message: Unauthorized
            description: The user is not authorized
            moreInfo: ""
            error: []
    UnsupportedMediaType:
      description: Unsupported Media Type. The entity of the request was not in a supported format.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 415
            message: Unsupported media type
            description: The entity of the request was not in a supported format
            moreInfo: ""
            error: []
  parameters:
    replyLimit:
      name: replyLimit
      in: query
      description: |
        Maximum size of replies array to return.
      required: false
      style: form
      explode: true
      schema:
        type: integer
        default: 25
    replyOffset:
      name: replyOffset
      in: query
      description: |
        Starting point within the complete list of replies.
      required: false
      style: form
      explode: true
      schema:
        type: integer
        default: 0
    commentId:
      name: commentId
      in: path
      description: |
        Comment Id
      required: true
      style: simple
      explode: false
      schema:
        type: string
    parentCommentID:
      name: replyTo
      in: query
      description: |
        ID of the perent comment.
      required: false
      style: form
      explode: true
      schema:
        type: string
    includeCommenterInfo:
      name: includeCommenterInfo
      in: query
      description: |
        Whether we need to display commentor details.
      required: false
      style: form
      explode: true
      schema:
        type: boolean
        default: false
    apiId:
      name: apiId
      in: path
      description: |
        **API ID** consisting of the **UUID** of the API.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    keyType:
      name: keyType
      in: query
      description: |
        Describes to which environment the api key belongs
      required: false
      style: form
      explode: true
      schema:
        type: string
    endpointId:
      name: endpointId
      in: path
      description: |
        **Endpoint ID** consisting of the **UUID** of the Endpoint**.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    apiId-Q:
      name: apiId
      in: query
      description: |
        **API ID** consisting of the **UUID** of the API.
        The combination of the provider of the API, name of the API and the version is also accepted as a valid API I.
        Should be formatted as **provider-name-version**.
      required: true
      style: form
      explode: true
      schema:
        type: string
    apiId-Q-Opt:
      name: apiId
      in: query
      description: |
        **API ID** consisting of the **UUID** of the API.
        The combination of the provider of the API, name of the API and the version is also accepted as a valid API I.
        Should be formatted as **provider-name-version**.
      required: false
      style: form
      explode: true
      schema:
        type: string
    choreoVersionId-Q-Opt:
      name: choreoVersionId
      in: query
      description: |
        **Choreo Version ID** consisting of the **UUID** of the Choreo Component Version ID.
      required: false
      style: form
      explode: true
      schema:
        pattern: ^([a-f\d]{8}\-[a-f\d]{4}\-[a-f\d]{4}\-[a-f\d]{4}\-[a-f\d]{12}|[a-f\d]{24})$
        type: string
    labelType-Q:
      name: labelType
      in: query
      description: |
        **API ID** consisting of the **UUID** of the API.
        The combination of the provider of the API, name of the API and the version is also accepted as a valid API I.
        Should be formatted as **provider-name-version**.
      required: false
      style: form
      explode: true
      schema:
        type: string
    name:
      name: name
      in: path
      description: |
        Name of the API
      required: true
      style: simple
      explode: false
      schema:
        type: string
    version:
      name: version
      in: path
      description: |
        Version of the API
      required: true
      style: simple
      explode: false
      schema:
        type: string
    apiName-Q:
      name: name
      in: query
      description: |
        Name of the API
      required: false
      style: form
      explode: true
      schema:
        type: string
    apiVersion-Q:
      name: version
      in: query
      description: |
        Version of the API
      required: false
      style: form
      explode: true
      schema:
        type: string
    apiProvider-Q:
      name: providerName
      in: query
      description: |
        Provider name of the API
      required: false
      style: form
      explode: true
      schema:
        type: string
    documentId:
      name: documentId
      in: path
      description: |
        Document Identifier
      required: true
      style: simple
      explode: false
      schema:
        type: string
    applicationId:
      name: applicationId
      in: path
      description: |
        **Application Identifier** consisting of the UUID of the Application.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    subscriptionId:
      name: subscriptionId
      in: path
      description: |
        Subscription Id
      required: true
      style: simple
      explode: false
      schema:
        type: string
    resourcePolicyId:
      name: resourcePolicyId
      in: path
      description: |
        registry resource Id
      required: true
      style: simple
      explode: false
      schema:
        type: string
    subscriptionId-Q:
      name: subscriptionId
      in: query
      description: |
        Subscription Id
      required: true
      style: form
      explode: true
      schema:
        type: string
    operationPolicyId:
      name: operationPolicyId
      in: path
      description: |
        Operation policy Id
      required: true
      style: simple
      explode: false
      schema:
        type: string
    revisionId:
      name: revisionId
      in: path
      description: |
        Revision ID of an API
      required: true
      style: simple
      explode: false
      schema:
        type: string
    revisionId-Q:
      name: revisionId
      in: query
      description: |
        Revision ID of an API
      required: false
      style: form
      explode: true
      schema:
        type: string
    revisionNum-Q:
      name: revisionNumber
      in: query
      description: |
        Revision Number of an API
      required: false
      style: form
      explode: true
      schema:
        type: string
    deploymentId:
      name: deploymentId
      in: path
      description: |
        Base64 URL encoded value of the name of an environment
      required: true
      style: simple
      explode: false
      schema:
        type: string
    policyName:
      name: policyName
      in: path
      description: |
        Tier name
      required: true
      style: simple
      explode: false
      schema:
        type: string
    policyName-Q:
      name: policyName
      in: query
      description: |
        Name of the policy
      required: true
      style: form
      explode: true
      schema:
        type: string
    policyLevel:
      name: policyLevel
      in: path
      description: |
        List API or Application or Resource type policies.
      required: true
      style: simple
      explode: false
      schema:
        type: string
        enum:
          - api
          - subcription
    policyLevel-Q:
      name: policyLevel
      in: query
      description: |
        List API or Application or Resource type policies.
      required: true
      style: form
      explode: true
      schema:
        type: string
        enum:
          - api
          - subcription
    limit:
      name: limit
      in: query
      description: |
        Maximum size of resource array to return.
      required: false
      style: form
      explode: true
      schema:
        type: integer
        default: 25
    Accept:
      name: Accept
      in: header
      description: |
        Media types acceptable for the response. Default is application/json.
      required: false
      style: simple
      explode: false
      schema:
        type: string
        default: application/json
    offset:
      name: offset
      in: query
      description: |
        Starting point within the complete list of items qualified.
      required: false
      style: form
      explode: true
      schema:
        type: integer
        default: 0
    sortBy:
      name: sortBy
      in: query
      description: |
        Criteria for sorting.
      required: false
      style: form
      explode: true
      schema:
        type: string
        enum:
          - apiName
          - version
          - createdTime
          - status
        default: createdTime
    sortOrder:
      name: sortOrder
      in: query
      description: |
        Order of sorting(ascending/descending).
      required: false
      style: form
      explode: true
      schema:
        type: string
        enum:
          - asc
          - desc
        default: desc
    If-None-Match:
      name: If-None-Match
      in: header
      description: |
        Validator for conditional requests; based on the ETag of the formerly retrieved
        variant of the resource.
      required: false
      style: simple
      explode: false
      schema:
        type: string
    If-Match:
      name: If-Match
      in: header
      description: |
        Validator for conditional requests; based on ETag.
      required: false
      style: simple
      explode: false
      schema:
        type: string
    scopeName:
      name: scopeId
      in: path
      description: |
        Base64 URL encoded value of the scope name
      required: true
      style: simple
      explode: false
      schema:
        type: string
    scopeId:
      name: scopeId
      in: path
      description: |
        Scope Id consisting the UUID of the shared scope
      required: true
      style: simple
      explode: false
      schema:
        type: string
    threatProtectionPolicyId:
      name: policyId
      in: path
      description: |
        The UUID of a Policy
      required: true
      style: simple
      explode: false
      schema:
        type: string
    roleId:
      name: roleId
      in: path
      description: |
        The Base 64 URL encoded role name with domain. If the given role is in secondary user-store, role ID should be
        derived as Base64URLEncode({user-store-name}/{role-name}). If the given role is in PRIMARY user-store, role ID
        can be derived as Base64URLEncode(role-name)
      required: true
      style: simple
      explode: false
      schema:
        type: string
    requestedTenant:
      name: X-WSO2-Tenant
      in: header
      description: |
        For cross-tenant invocations, this is used to specify the tenant domain, where the resource need to be
          retirieved from.
      required: false
      style: simple
      explode: false
      schema:
        type: string
    apiProductId:
      name: apiProductId
      in: path
      description: |
        **API Product ID** consisting of the **UUID** of the API Product. Using the **UUID** in the API call is recommended.
      required: true
      style: simple
      explode: false
      schema:
        type: string
        x-encoded: true
      x-encoded: true
    tenantDomain:
      name: tenantDomain
      in: path
      description: |
        The domain of a specific tenant
      required: true
      style: simple
      explode: false
      schema:
        type: string
    alertType:
      name: alertType
      in: path
      description: The alert type.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    configurationId:
      name: configurationId
      in: path
      description: The alert configuration id.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    tierQuotaType:
      name: tierQuotaType
      in: query
      description: Filter the subscription base on tier quota type
      required: false
      style: form
      explode: true
      schema:
        type: string
    envId:
      name: envId
      in: path
      description: |
        **Env ID** consisting of the **UUID** of the gateway environment.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    EnvironmentId:
      name: environmentId
      in: query
      description: |
        Environment template ID of the Choreo environment
      required: false
      style: form
      explode: true
      schema:
        type: string
    apiProductId-Q:
      name: apiProductId
      in: query
      description: |
        **API Product ID** consisting of the **UUID** of the API Product.
        The combination of the provider, name and the version of the API Product is also accepted as a valid API Product ID.
        Should be formatted as **provider-name-version**.
      required: true
      style: form
      explode: true
      schema:
        type: string
    includeFromVersionRange:
      name: includeFromVersionRange
      in: query
      description: Boolean value to include subscriptions from the version range
      required: false
      style: form
      explode: true
      schema:
        type: boolean
        default: false
    governanceCheckEnabled:
      name: governanceCheckEnabled
      in: query
      description: |
        The parameter to enable or disable the governance check. By default, the governance check will be enabled.
      required: false
      style: form
      explode: true
      schema:
        type: boolean
        default: true
  requestBodies:
    threatProtectionPolicy:
      description: |
        Threat protection policy request parameter
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ThreatProtectionPolicy'
      required: true
  securitySchemes:
    OAuth2Security:
      type: oauth2
      flows:
        password:
          tokenUrl: https://localhost:9443/oauth2/token
          scopes:
            openid: Authorize access to user details
            apim:api_view: View API
            apim:api_create: Create API
            apim:api_delete: Delete API
            apim:api_publish: Publish API
            apim:api_manage: Manage all API related operations
            apim:subscription_view: View Subscription
            apim:subscription_block: Block Subscription
            apim:subscription_manage: Manage all Subscription related operations
            apim:threat_protection_policy_create: Create threat protection policies
            apim:threat_protection_policy_manage: Update and delete threat protection policies
            apim:document_create: Create API documents
            apim:document_manage: Create, update and delete API documents
            apim:api_mediation_policy_manage: View, create, update and remove API specific mediation policies
            apim:mediation_policy_view: View mediation policies
            apim:mediation_policy_create: Create mediation policies
            apim:mediation_policy_manage: Update and delete mediation policies
            apim:common_operation_policy_view: View common operation policies
            apim:common_operation_policy_manage: Add, Update and Delete common operation policies
            apim:client_certificates_view: View client certificates
            apim:client_certificates_add: Add client certificates
            apim:client_certificates_update: Update and delete client certificates
            apim:client_certificates_manage: View, create, update and remove client certificates
            apim:ep_certificates_view: View backend endpoint certificates
            apim:ep_certificates_add: Add backend endpoint certificates
            apim:ep_certificates_update: Update and delete backend endpoint certificates
            apim:ep_certificates_manage: View, create, update and remove endpoint certificates
            apim:publisher_settings: Retrieve store settings
            apim:pub_alert_manage: Get/ subscribe/ configure publisher alerts
            apim:shared_scope_manage: Manage shared scopes
            apim:app_import_export: Import and export applications related operations
            apim:api_import_export: Import and export APIs related operations
            apim:api_product_import_export: Import and export API Products related operations
            apim:api_generate_key: Generate Internal Key
            apim:admin: Manage all admin operations
            apim:comment_view: Read permission to comments
            apim:comment_write: Write permission to comments
            apim:comment_manage: Read and Write comments
            apim:tier_view: View throttling policies
            apim:tier_manage: View, update and delete throttling policies
            apim:api_list_view: View, Retrieve API list
            apim:api_definition_view: View, Retrieve API definition
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            apim:document_create: Create API documents
            apim:pub_alert_manage: Get/ subscribe/ configure publisher alerts
            apim:tier_manage: View, update and delete throttling policies
            apim:ep_certificates_manage: View, create, update and remove endpoint certificates
            apim:threat_protection_policy_create: Create threat protection policies
            apim:threat_protection_policy_manage: Update and delete threat protection policies
            apim:ep_certificates_update: Update and delete backend endpoint certificates
            apim:admin: Manage all admin operations
            apim:api_publish: Publish API
            apim:document_manage: Create, update and delete API documents
            apim:ep_certificates_view: View backend endpoint certificates
            apim:publisher_settings: Retrieve store settings
            apim:api_definition_view: View, Retrieve API definition
            apim:subscription_block: Block Subscription
            apim:api_generate_key: Generate Internal Key
            apim:api_product_import_export: Import and export API Products related operations
            apim:comment_write: Write permission to comments
            apim:mediation_policy_view: View mediation policies
            apim:api_create: Create API
            apim:tier_view: View throttling policies
            apim:common_operation_policy_view: View common operation policies
            apim:client_certificates_manage: View, create, update and remove client certificates
            apim:client_certificates_update: Update and delete client certificates
            apim:api_view: View API
            apim:shared_scope_manage: Manage shared scopes
            apim:app_import_export: Import and export applications related operations
            apim:client_certificates_add: Add client certificates
            openid: Authorize access to user details
            apim:common_operation_policy_manage: Add, Update and Delete common operation policies
            apim:comment_manage: Read and Write comments
            apim:api_list_view: View, Retrieve API list
            apim:mediation_policy_manage: Update and delete mediation policies
            apim:api_mediation_policy_manage: View, create, update and remove API specific mediation policies
            apim:api_delete: Delete API
            apim:api_manage: Manage all API related operations
            apim:ep_certificates_add: Add backend endpoint certificates
            apim:mediation_policy_create: Create mediation policies
            apim:subscription_view: View Subscription
            apim:client_certificates_view: View client certificates
            apim:comment_view: Read permission to comments
            apim:api_import_export: Import and export APIs related operations
            apim:subscription_manage: Manage all Subscription related operations
          x-scopes-bindings:
            apim:api_manage: ""
            apim:admin: ""
            apim:common_operation_policy_view: ""
            apim:subscription_manage: ""
            apim:comment_manage: ""
            apim:tier_view: ""
            apim:document_create: ""
            apim:api_list_view: ""
            apim:threat_protection_policy_manage: ""
            apim:common_operation_policy_manage: ""
            apim:subscription_view: ""
            apim:api_create: ""
            apim:shared_scope_manage: ""
            apim:threat_protection_policy_create: ""
            apim:client_certificates_add: ""
            apim:mediation_policy_manage: ""
            apim:ep_certificates_update: ""
            apim:api_product_import_export: ""
            apim:ep_certificates_view: ""
            apim:api_publish: ""
            apim:app_import_export: ""
            apim:api_delete: ""
            apim:client_certificates_update: ""
            openid: ""
            apim:api_definition_view: ""
            apim:client_certificates_view: ""
            apim:api_generate_key: ""
            apim:api_view: ""
            apim:pub_alert_manage: ""
            apim:mediation_policy_create: ""
            apim:api_import_export: ""
            apim:ep_certificates_manage: ""
            apim:document_manage: ""
            apim:publisher_settings: ""
            apim:comment_view: ""
            apim:subscription_block: ""
            apim:mediation_policy_view: ""
            apim:client_certificates_manage: ""
            apim:comment_write: ""
            apim:ep_certificates_add: ""
            apim:tier_manage: ""
            apim:api_mediation_policy_manage: ""
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://choreo-am-service.dev-choreo-apim.svc.cluster.local:9763/api/am/publisher/v2
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-am-service.dev-choreo-apim.svc.cluster.local:9763/api/am/publisher/v2
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/am/publisher/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
