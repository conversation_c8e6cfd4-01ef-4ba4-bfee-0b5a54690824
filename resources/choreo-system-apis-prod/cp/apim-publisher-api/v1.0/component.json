{"data": {"component": {"id": "c0799480-cc6b-4e02-ba58-135ee7a4876c", "name": "apim-publisher-api", "handler": "apim-publisher-api", "description": " ", "displayType": "proxy", "displayName": "APIM Publisher API", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-02-14T08:53:34.000Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "APIM Publisher API", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/am/publisher", "proxyId": "67aef5d4db2936556749136c", "id": "67aef5d4db2936556749136c", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67aef5d4db2936556749136c", "createdAt": "1739519444625", "updatedAt": "2025-02-14 07:50:44.625", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "c0799480-cc6b-4e02-ba58-135ee7a4876c", "latest": true, "versionStrategy": ""}]}}}