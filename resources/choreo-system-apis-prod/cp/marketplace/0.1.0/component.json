{"data": {"component": {"id": "9bf7fd91-9633-4a24-acff-86ec5a3b1c5a", "name": "marketplace", "handler": "cdjums", "description": " ", "displayType": "proxy", "displayName": "Marketplace", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "0.1.0", "labels": [], "createdAt": "2023-06-12T05:43:48.049Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "0.1.0", "proxyName": "Marketplace", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/marketplace", "proxyId": "6486b08be88a407b4f8f5ff1", "id": "6486b08be88a407b4f8f5ff1", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "6486b08be88a407b4f8f5ff1", "createdAt": "1686548619356", "updatedAt": "2023-10-29 08:54:42.24", "apiVersion": "0.1.0", "branch": null, "description": null, "componentId": "9bf7fd91-9633-4a24-acff-86ec5a3b1c5a", "latest": true, "versionStrategy": ""}]}}}