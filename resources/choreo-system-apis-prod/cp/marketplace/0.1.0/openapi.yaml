openapi: 3.0.1
info:
  title: Marketplace
  contact: {}
  version: 0.1.0
servers:
  - url: https://apis.choreo.dev/93tu/marketplace/0.1.0
    variables:
      server:
        default: apis.choreo.dev
      port:
        default: "443"
security:
  - default: []
paths:
  /services:
    get:
      summary: Retrieves services matching with the given filtering parameters.
      operationId: getServices
      parameters:
        - name: x-jwt-assertion
          in: header
          description: JWT token
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: limit
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 20
        - name: offset
          in: query
          description: Offset of the results. By default 0.
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 0
        - name: sortBy
          in: query
          description: |-
            Sort by `name`, `averageRating`, `createdTime`. By default sorted by `name`.
            All results are first sorted and then paginated.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: sortAscending
          in: query
          description: Whether to sort in ascending order. By default `true`.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: true
        - name: searchContent
          in: query
          description: Search within the content (description, summary and IDL) of the service. By default `false`.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
        - name: networkVisibilityFilter
          in: query
          description: Filter services based on network visibility. Possible values are "project", "org", "public".
          required: false
          style: form
          explode: true
          schema:
            type: string
            default: org
        - name: aggregateByMajorVersion
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
        - name: query
          in: query
          description: Optionally filter services based on service name, description, summary and IDL.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: tags
          in: query
          description: Optionally filter services based on tags. Multiple tags can be provided as a comma separated list.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: categories
          in: query
          description: Optionally filter services based on categories. Multiple categories can be provided as a comma separated list.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: isThirdParty
          in: query
          description: Optionally filter services based on whether they are third party or not. By default null, meaning this filter is not effective.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: templateType
          in: query
          required: false
          style: form
          explode: true
          schema:
            nullable: true
        - name: isTemplated
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            nullable: true
        - name: componentId
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: endpointId
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: networkVisibilityprojectId
          in: query
          description: When networkVisibilityFilter is "project", this parameter can be used to filter services
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: includeCreated
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
        - name: name
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: version
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceResponse'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      operationId: postServices
      parameters:
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceRequest'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceCreated'
        "409":
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}:
    get:
      summary: Gets the service with the given id.
      operationId: getServicesServiceid
      parameters:
        - name: serviceId
          in: path
          description: Id of the service to retrieve.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      summary: Updates the service with the given id.
      operationId: putServicesServiceid
      parameters:
        - name: serviceId
          in: path
          description: Id of the service to update.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceRequest'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SucessResponse'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Deletes the service with the given id.
      operationId: deleteServicesServiceid
      parameters:
        - name: serviceId
          in: path
          description: Id of the service to delete.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SucessResponse'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ErrorDetails'
                  - $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/choreo/components/{componentId}/versions/{componentVersion}/endpoints/{endpointId}:
    get:
      summary: Gets the choreo service with the given the choreo component id and endpoint id.
      operationId: getServicesChoreoComponentsComponentidVersionsComponentversionEndpointsEndpointid
      parameters:
        - name: componentId
          in: path
          description: Id of the component id of the service to retrieve.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: componentVersion
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: endpointId
          in: path
          description: Id of the endpoint id of the service to retrieve. This ID is unique within a component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: isVersionRange
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChoreoService'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/idl:
    get:
      summary: Retrieve IDL (service defintion) of the service with the given id of the environment.
      operationId: getServicesServiceidIdl
      parameters:
        - name: serviceId
          in: path
          description: Id of the service having inteneded IDL.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: environmentId
          in: query
          description: Id of the environment. No need to provide this parameter if service is thrid party.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IDL'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      summary: |-
        Updates IDL (service defintion) of the service with the given id. If an environment id is provided,
        IDL of that environment will be updated. Otherwise, service should be third-party one and IDL of it will be updated.
      operationId: putServicesServiceidIdl
      parameters:
        - name: serviceId
          in: path
          description: Id of the service.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: environmentId
          in: query
          description: Id of the environment. No need to provide this parameter if service is thrid party.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SucessResponse'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Registers IDL (service defintion) of the service with the given service.
      operationId: postServicesServiceidIdl
      parameters:
        - name: serviceId
          in: path
          description: Id of the service to register IDL against.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: idlType
          in: query
          description: Type of the IDL. Possible values are "AsyncAPI", "OpenAPI", "GraphQL", "Proto3", "Sdl", "WSDL".
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: environmentId
          in: query
          description: Id of the environment. No need to provide this parameter if service is thrid party.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceCreated'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/idls:
    get:
      operationId: getServicesServiceidIdls
      parameters:
        - name: serviceId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/connection-schemas:
    post:
      summary: Register one or more new connection schema(s) for the service with the given id.
      operationId: postServicesServiceidConnectionSchemas
      parameters:
        - name: serviceId
          in: path
          description: Id of the service.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Connection schema(s) to register. Multiple schemas can be provided as an array.
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ConnectionSchemaInfo'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceCreated'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/connection-schemas/{schemaId}:
    put:
      summary: Updates the connection schema.
      operationId: putServicesServiceidConnectionSchemasSchemaid
      parameters:
        - name: serviceId
          in: path
          description: Id of the service connection schema belongs to.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: schemaId
          in: path
          description: Id of the connection schema to update.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: New connection schema information.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionSchemaInfo'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SucessResponse'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/connection-schemas/{connetionSchemaId}:
    get:
      summary: Retrieves particular connection schema of a service.
      operationId: getServicesServiceidConnectionSchemasConnetionschemaid
      parameters:
        - name: serviceId
          in: path
          description: Id of the service.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: connetionSchemaId
          in: path
          description: Id of the connection schema.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionSchema'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/connection-schemas/{connectionSchemaId}/how-to-use:
    get:
      summary: Retrieves how-to-use document related to the connection schema of the service.
      operationId: getServicesServiceidConnectionSchemasConnectionschemaidHowToUse
      parameters:
        - name: serviceId
          in: path
          description: Id of the service
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: connectionSchemaId
          in: path
          description: Id of the connection schema
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: configGroupId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: connectionName
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: componentType
          in: query
          required: false
          style: form
          explode: true
          schema:
            nullable: true
        - name: buildpackType
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            default: ""
        - name: isProjectLvlConnection
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
        - name: configFileType
          in: query
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
              example: Choreo allows you to link a **configuration group** to a component during deployment, enabling the use of shared configurations. A configuration group consists of key-value pairs
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/icon:
    put:
      summary: Updates the icon of the service.
      operationId: putServicesServiceidIcon
      parameters:
        - name: serviceId
          in: path
          description: Id of the service.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SucessResponse'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Creates an icon for the service.
      operationId: postServicesServiceidIcon
      parameters:
        - name: serviceId
          in: path
          description: Id of the service.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SucessResponse'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/rating:
    post:
      summary: Posts a rating for the service.
      operationId: postServicesServiceidRating
      parameters:
        - name: serviceId
          in: path
          description: Id of the service.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: rating
          in: query
          description: Rating to post (1-5)
          required: true
          style: form
          explode: true
          schema:
            type: integer
            format: int64
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                type: number
                format: float
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/status:
    put:
      summary: Update status of the service.
      operationId: putServicesServiceidStatus
      parameters:
        - name: serviceId
          in: path
          description: Id of the service.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SucessResponse'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/tags:
    get:
      summary: Retrieves all the tags of the services in your organization.
      operationId: getServicesTags
      parameters:
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: resourceType
          in: query
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: array
                items:
                  type: string
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/categories:
    get:
      summary: Retrieves all the categories of the services in your organization.
      operationId: getServicesCategories
      parameters:
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: array
                items:
                  type: string
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/endpoints:
    get:
      summary: Retrieve endpoint for a given environment.
      operationId: getServicesEndpoints
      parameters:
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: endpointIdentifier
          in: query
          description: Endpoint identifier (<serviceId>/<visibility>)
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: environmentId
          in: query
          description: Environment Id
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: serviceId
          in: query
          description: The service id for which the endpoint is required
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: visibility
          in: query
          description: Visibility of the endpoint
          required: false
          style: form
          explode: true
          schema:
            nullable: true
        - name: projectHandle
          in: query
          description: Project Handle (if visibility is project this needs to be provided)
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: useServiceId
          in: query
          description: Generate dependency id and resolve endpoint using the service Id
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            nullable: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Endpoint'
        "404":
          description: NotFound
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/dependencyId:
    get:
      summary: Retrieve identifier used to refer to service in dependency management.
      operationId: getServicesServiceidDependencyid
      parameters:
        - name: serviceId
          in: path
          description: Id of the resource registry service entry
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "404":
          description: NotFound
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/connection-schemas/{schemaId}/config:
    get:
      operationId: getServicesServiceidConnectionSchemasSchemaidConfig
      parameters:
        - name: serviceId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: schemaId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionConfigResponse'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "404":
          description: NotFound
        "403":
          description: Forbidden
        "400":
          description: BadRequest
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      operationId: putServicesServiceidConnectionSchemasSchemaidConfig
      parameters:
        - name: serviceId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: schemaId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionConfigRequest'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionConfigResponse'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "404":
          description: NotFound
        "403":
          description: Forbidden
        "400":
          description: BadRequest
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      operationId: postServicesServiceidConnectionSchemasSchemaidConfig
      parameters:
        - name: serviceId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: schemaId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionConfigRequest'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectionConfigResponse'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "404":
          description: NotFound
        "403":
          description: Forbidden
        "400":
          description: BadRequest
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      operationId: deleteServicesServiceidConnectionSchemasSchemaidConfig
      parameters:
        - name: serviceId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: schemaId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: object
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "404":
          description: NotFound
        "403":
          description: Forbidden
        "400":
          description: BadRequest
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /services/{serviceId}/connection-schemas/{schemaId}/config/{environments}:
    get:
      operationId: getServicesServiceidConnectionSchemasSchemaidConfigEnvironments
      parameters:
        - name: serviceId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: schemaId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: environments
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "404":
          description: NotFound
        "403":
          description: Forbidden
        "400":
          description: BadRequest
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /databases/{resourceId}:
    get:
      summary: Gets the resource with the given id.
      operationId: getDatabasesResourceid
      parameters:
        - name: resourceId
          in: path
          description: Id of the resource to retrieve.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /databases:
    get:
      summary: Retrieves resources matching with the given filtering parameters.
      operationId: getDatabases
      parameters:
        - name: x-jwt-assertion
          in: header
          description: JWT token
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: limit
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 20
        - name: offset
          in: query
          description: Offset of the results. By default 0.
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 0
        - name: sortBy
          in: query
          description: |-
            Sort by `name`, `averageRating`, `createdTime`. By default sorted by `name`.
            All results are first sorted and then paginated.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: sortAscending
          in: query
          description: Whether to sort in ascending order. By default `true`.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: true
        - name: searchContent
          in: query
          description: Search within the content (description, summary and IDL) of the service. By default `false`.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
        - name: query
          in: query
          description: Optionally filter services based on service name, description, summary and IDL.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: tags
          in: query
          description: Optionally filter services based on tags. Multiple tags can be provided as a comma separated list.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: categories
          in: query
          description: Optionally filter services based on categories. Multiple categories can be provided as a comma separated list.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: customFilter
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceResponse'
              example:
                count: 10
                pagination:
                  limit: 20
                  offset: 0
                  total: 100
                data:
                  - averageRating: 4.5
                    totalRatingCount: 10
                    thumbnailUrl: https://example.com/thumbnail.png
                    connectionSchemas: []
                    name: Redis Database Configurations
                    version: 1.2.0
                    resourceType: CONFIGURATION_GROUP
                    organizationId: 2a3278b4-7c68-4cbd-b0e9-6f63fb7f55bc
                    projectId: f6c7ebea-6e09-4a2c-bbd6-34878ebd165c
                    summary: Enterprise resource database server configurations
                    description: Redis database configurations for enterprise applications
                    tags: []
                    categories: []
                    visibility:
                      - ORGANIZATION
                    properties: {}
                    resourceId: 88bad586-fe83-40d7-9262-7cedd3f2be32
                    createdTime: "2025-03-12T14:30:00Z"
                    resourceDetails:
                      configGroupId: 88bad586-fe83-40d7-9262-7cedd3f2be32
                      configGroupName: redis-database-configurations
                      configGroupType: USER
                      configurations:
                        - configKeyId: b736f4b9-4d30-4a29-a5f4-622afe6ab44f
                          configKey: redis.host
                          isSensitive: false
                          isFile: false
                      configGroupProperties:
                        environmentNames:
                          - development
                          - production
                        projectNames:
                          - customer-portal
                          - payment-service
                        componentNames:
                          - notification-service
                          - data-processor
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /databases/{resourceId}/connection-schemas/{connectionGroupId}/how-to-use:
    get:
      summary: Retrieves how-to-use document related to a database connections.
      operationId: getDatabasesResourceidConnectionSchemasConnectiongroupidHowToUse
      parameters:
        - name: resourceId
          in: path
          description: Id of the database
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: connectionGroupId
          in: path
          description: Configuration group Uuid
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgId
          in: query
          description: Organization Id
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: buildpackType
          in: query
          description: |-
            Client component buildpack type
            returned doc will differ. Possible values `console`, `vscode`
          required: false
          style: form
          explode: true
          schema:
            type: string
            default: ""
        - name: connectionName
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            default: ""
        - name: configFileType
          in: query
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /templates/list:
    get:
      summary: Retrieves templates matching with the given template type.
      operationId: getTemplatesList
      parameters:
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: templateType
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TemplateEntry'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /templates/{templateId}:
    get:
      summary: Retrieves template
      operationId: getTemplatesTemplateid
      parameters:
        - name: templateId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: templateType
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateRecord'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configuration-groups:
    get:
      summary: Retrieves resources matching with the given filtering parameters.
      operationId: getConfigurationGroups
      parameters:
        - name: x-jwt-assertion
          in: header
          description: JWT token
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: limit
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 20
        - name: offset
          in: query
          description: Offset of the results. By default 0.
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
            default: 0
        - name: sortBy
          in: query
          description: |-
            Sort by `name`, `averageRating`, `createdTime`. By default sorted by `name`.
            All results are first sorted and then paginated.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: sortAscending
          in: query
          description: Whether to sort in ascending order. By default `true`.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: true
        - name: searchContent
          in: query
          description: Search within the content (description, summary and IDL) of the service. By default `false`.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
        - name: query
          in: query
          description: Optionally filter services based on service name, description, summary and IDL.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: tags
          in: query
          description: Optionally filter services based on tags. Multiple tags can be provided as a comma separated list.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: categories
          in: query
          description: Optionally filter services based on categories. Multiple categories can be provided as a comma separated list.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: customFilter
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceResponse'
              example:
                count: 10
                pagination:
                  limit: 20
                  offset: 0
                  total: 100
                data:
                  - averageRating: 4.5
                    totalRatingCount: 10
                    thumbnailUrl: https://example.com/thumbnail.png
                    connectionSchemas: []
                    name: Redis Database Configurations
                    version: 1.2.0
                    resourceType: CONFIGURATION_GROUP
                    organizationId: 2a3278b4-7c68-4cbd-b0e9-6f63fb7f55bc
                    projectId: f6c7ebea-6e09-4a2c-bbd6-34878ebd165c
                    summary: Enterprise resource database server configurations
                    description: Redis database configurations for enterprise applications
                    tags: []
                    categories: []
                    visibility:
                      - ORGANIZATION
                    properties: {}
                    resourceId: 88bad586-fe83-40d7-9262-7cedd3f2be32
                    createdTime: "2025-03-12T14:30:00Z"
                    resourceDetails:
                      configGroupId: 88bad586-fe83-40d7-9262-7cedd3f2be32
                      configGroupName: redis-database-configurations
                      configGroupType: USER
                      configurations:
                        - configKeyId: b736f4b9-4d30-4a29-a5f4-622afe6ab44f
                          configKey: redis.host
                          isSensitive: false
                          isFile: false
                      configGroupProperties:
                        environmentNames:
                          - development
                          - production
                        projectNames:
                          - customer-portal
                          - payment-service
                        componentNames:
                          - notification-service
                          - data-processor
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configuration-groups/{resourceId}:
    get:
      summary: Gets the resource with the given id.
      operationId: getConfigurationGroupsResourceid
      parameters:
        - name: resourceId
          in: path
          description: Id of the resource to retrieve.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceInfo'
              example:
                averageRating: 4.5
                totalRatingCount: 10
                thumbnailUrl: https://example.com/thumbnail.png
                connectionSchemas: []
                name: Redis Database Configurations
                version: 1.2.0
                resourceType: CONFIGURATION_GROUP
                organizationId: 2a3278b4-7c68-4cbd-b0e9-6f63fb7f55bc
                projectId: f6c7ebea-6e09-4a2c-bbd6-34878ebd165c
                summary: Enterprise resource database server configurations
                description: Redis database configurations for enterprise applications
                tags: []
                categories: []
                visibility:
                  - ORGANIZATION
                properties: {}
                resourceId: 88bad586-fe83-40d7-9262-7cedd3f2be32
                createdTime: "2025-03-12T14:30:00Z"
                resourceDetails:
                  configGroupId: 88bad586-fe83-40d7-9262-7cedd3f2be32
                  configGroupName: redis-database-configurations
                  configGroupType: USER
                  configurations:
                    - configKeyId: b736f4b9-4d30-4a29-a5f4-622afe6ab44f
                      configKey: redis.host
                      isSensitive: false
                      isFile: false
                  configGroupProperties:
                    environmentNames:
                      - development
                      - production
                    projectNames:
                      - customer-portal
                      - payment-service
                    componentNames:
                      - notification-service
                      - data-processor
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configuration-groups/{resourceId}/how-to-use:
    get:
      summary: Retrieves how-to-use document related to a configuration group.
      operationId: getConfigurationGroupsResourceidHowToUse
      parameters:
        - name: resourceId
          in: path
          description: Id of the configuration group
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: x-jwt-assertion
          in: header
          description: JWT token
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgId
          in: query
          description: |-
            Organization Id
            returned doc will differ. Possible values `console`, `vscode`
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
              example: '## Sample how-to-use markdown content'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /platform-resources:
    get:
      summary: Search for platform resources.
      operationId: getPlatformResources
      parameters:
      - name: x-jwt-assertion
        in: header
        required: true
        schema:
          type: string
      - name: limit
        in: query
        schema:
          type: integer
          format: int64
          default: 20
      - name: offset
        in: query
        schema:
          type: integer
          format: int64
          default: 0
      - name: sortBy
        in: query
        content:
          application/json:
            schema:
              type: object
              additionalProperties: true
      - name: sortAscending
        in: query
        schema:
          type: boolean
          default: true
      - name: searchContent
        in: query
        schema:
          type: boolean
          default: false
      - name: networkVisibilityFilter
        in: query
        schema:
          type: string
          default: org
      - name: query
        in: query
        schema:
          type: string
          nullable: true
      - name: tags
        in: query
        schema:
          type: string
          nullable: true
      - name: name
        in: query
        schema:
          type: string
          nullable: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceResponse'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
  /platform-resources/{resourceId}:
    get:
      operationId: getPlatformResourcesResourceid
      summary: Gets the platform resource with the given id.
      parameters:
      - name: resourceId
        in: path
        required: true
        schema:
          type: string
      - name: x-jwt-assertion
        in: header
        required: true
        schema:
          type: string
      responses:
        "404":
          description: NotFound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
components:
  schemas:
    ResourceRequest:
      required:
        - name
        - organizationId
        - resourceType
        - version
        - visibility
      type: object
      properties:
        name:
          type: string
        version:
          type: string
        resourceType:
          type: string
          enum:
            - CONFIGURATION_GROUP
            - DATABASE
            - SERVICE
        organizationId:
          type: string
        projectId:
          type: string
        summary:
          type: string
        description:
          type: string
        tags:
          type: array
          items:
            type: string
        categories:
          type: array
          items:
            type: string
        visibility:
          type: array
          items:
            type: string
            enum:
              - PUBLIC
              - ORGANIZATION
              - PROJECT
        properties:
          type: object
          additionalProperties:
            type: string
        resourceId:
          type: string
    ConnectionSchemaEntry:
      required:
        - isOptional
        - isSensitive
        - name
        - type
      type: object
      properties:
        name:
          type: string
        type:
          type: string
        description:
          type: string
        isSensitive:
          type: boolean
        isOptional:
          type: boolean
    SchemaInfo:
      required:
        - description
        - entries
        - isDefault
        - name
      type: object
      properties:
        name:
          type: string
        id:
          type: string
        description:
          type: string
        isDefault:
          type: boolean
        entries:
          type: array
          items:
            $ref: '#/components/schemas/ConnectionSchemaEntry'
    ChoreoComponent:
      required:
        - componentId
        - endpointId
      type: object
      properties:
        componentId:
          type: string
        endpointId:
          type: string
        apimId:
          type: string
      description: Choreo component info of a marketplace resource.
    IDL:
      required:
        - content
        - environmentId
        - idlType
      type: object
      properties:
        environmentId:
          type: string
        content: {}
        idlType:
          type: string
          enum:
            - UDP
            - TCP
            - WSDL
            - Proto3
            - GraphQL_SDL
            - OpenAPI
            - AsyncAPI
      description: Represents Interface Definition Language of an API resource.
    ThirdPartyEndpoint:
      type: object
      additionalProperties:
        type: string
    ServiceRequest:
      allOf:
        - $ref: '#/components/schemas/ResourceRequest'
        - required:
            - connectionSchemas
            - isThirdParty
            - serviceType
            - status
          type: object
          properties:
            serviceType:
              type: string
              enum:
                - ASYNC_API
                - GRPC
                - GRAPHQL
                - SOAP
                - REST
            status:
              type: string
              enum:
                - CREATED
                - DEPRECATED
                - PUBLISHED
                - PROTOTYPE
            isThirdParty:
              type: boolean
            connectionSchemas:
              type: array
              items:
                $ref: '#/components/schemas/SchemaInfo'
            component:
              $ref: '#/components/schemas/ChoreoComponent'
            templateType:
              type: string
              enum:
                - GenAI
            idl:
              $ref: '#/components/schemas/IDL'
            endpointRefs:
              $ref: '#/components/schemas/ThirdPartyEndpoint'
    ResourceCreated:
      required:
        - details
      type: object
      properties:
        details:
          type: string
        id:
          type: string
    ErrorDetails:
      required:
        - details
        - error
      type: object
      properties:
        error:
          type: string
        details:
          type: string
    Pagination:
      required:
        - limit
        - offset
        - total
      type: object
      properties:
        limit:
          type: integer
          format: int64
        total:
          type: integer
          format: int64
        offset:
          type: integer
          format: int64
    ConnectionSchema:
      allOf:
        - $ref: '#/components/schemas/SchemaInfo'
        - type: object
          properties: {}
    AttachmentSummary:
      required:
        - id
        - name
      type: object
      properties:
        id:
          type: string
        name:
          type: string
    ServiceInfo:
      required:
        - averageRating
        - connectionSchemas
        - createdTime
        - isThirdParty
        - name
        - organizationId
        - resourceType
        - serviceId
        - serviceType
        - status
        - thumbnailUrl
        - totalRatingCount
        - version
        - visibility
      type: object
      properties:
        serviceId:
          type: string
        status:
          type: string
          enum:
            - CREATED
            - DEPRECATED
            - PUBLISHED
            - PROTOTYPE
        serviceType:
          type: string
          enum:
            - ASYNC_API
            - GRPC
            - GRAPHQL
            - SOAP
            - REST
        isThirdParty:
          type: boolean
        connectionSchemas:
          type: array
          items:
            $ref: '#/components/schemas/ConnectionSchema'
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/AttachmentSummary'
        templateType:
          type: string
          enum:
            - GenAI
        thumbnailUrl:
          type: string
        averageRating:
          type: number
          format: float
        totalRatingCount:
          type: integer
          format: int64
        createdTime:
          type: string
        name:
          type: string
        version:
          type: string
        resourceType:
          type: string
          enum:
            - CONFIGURATION_GROUP
            - DATABASE
            - SERVICE
        organizationId:
          type: string
        projectId:
          type: string
        summary:
          type: string
        description:
          type: string
        tags:
          type: array
          items:
            type: string
        categories:
          type: array
          items:
            type: string
        visibility:
          type: array
          items:
            type: string
            enum:
              - PUBLIC
              - ORGANIZATION
              - PROJECT
        properties:
          type: object
          additionalProperties:
            type: string
        resourceId:
          type: string
    ChoreoService:
      allOf:
        - $ref: '#/components/schemas/ServiceInfo'
        - required:
            - component
          type: object
          properties:
            component:
              $ref: '#/components/schemas/ChoreoComponent'
    ThirdPartyService:
      allOf:
        - $ref: '#/components/schemas/ServiceInfo'
        - required:
            - averageRating
            - connectionSchemas
            - createdTime
            - endpointRefs
            - isThirdParty
            - name
            - organizationId
            - resourceType
            - serviceId
            - serviceType
            - status
            - thumbnailUrl
            - totalRatingCount
            - version
            - visibility
          type: object
          properties:
            endpointRefs:
              $ref: '#/components/schemas/ThirdPartyEndpoint'
            serviceId:
              type: string
            status:
              type: string
              enum:
                - CREATED
                - DEPRECATED
                - PUBLISHED
                - PROTOTYPE
            serviceType:
              type: string
              enum:
                - ASYNC_API
                - GRPC
                - GRAPHQL
                - SOAP
                - REST
            isThirdParty:
              type: boolean
            connectionSchemas:
              type: array
              items:
                $ref: '#/components/schemas/ConnectionSchema'
            attachments:
              type: array
              items:
                $ref: '#/components/schemas/AttachmentSummary'
            templateType:
              type: string
              enum:
                - GenAI
            thumbnailUrl:
              type: string
            averageRating:
              type: number
              format: float
            totalRatingCount:
              type: integer
              format: int64
            createdTime:
              type: string
            name:
              type: string
            version:
              type: string
            resourceType:
              type: string
              enum:
                - CONFIGURATION_GROUP
                - DATABASE
                - SERVICE
            organizationId:
              type: string
            projectId:
              type: string
            summary:
              type: string
            description:
              type: string
            tags:
              type: array
              items:
                type: string
            categories:
              type: array
              items:
                type: string
            visibility:
              type: array
              items:
                type: string
                enum:
                  - PUBLIC
                  - ORGANIZATION
                  - PROJECT
            properties:
              type: object
              additionalProperties:
                type: string
            resourceId:
              type: string
    Service:
      oneOf:
        - $ref: '#/components/schemas/ChoreoService'
        - $ref: '#/components/schemas/ThirdPartyService'
    ServiceResponse:
      required:
        - count
        - data
        - pagination
      type: object
      properties:
        count:
          type: integer
          format: int64
        pagination:
          $ref: '#/components/schemas/Pagination'
        data:
          type: array
          items:
            $ref: '#/components/schemas/Service'
    SucessResponse:
      required:
        - message
      type: object
      properties:
        message:
          type: string
    ErrorPayload:
      type: object
      properties:
        reason:
          type: string
          description: Reason phrase
        path:
          type: string
          description: Request path
        method:
          type: string
          description: Method type of the request
        message:
          type: string
          description: Error message
        timestamp:
          type: string
          description: Timestamp of the error
        status:
          type: integer
          description: Relevant HTTP status code
          format: int32
    ConnectionSchemaInfo:
      allOf:
        - $ref: '#/components/schemas/SchemaInfo'
        - type: object
          properties: {}
    Endpoint:
      required:
        - environment
        - url
      type: object
      properties:
        url:
          type: string
        environment:
          type: string
        description:
          type: string
    ConnectionConfigKVPair:
      required:
        - key
        - value
      type: object
      properties:
        key:
          type: string
        value:
          type: string
      description: ConnectionConfigKVPair represents a single configuration.
    EndpointConfig:
      required:
        - environmentTemplateIds
        - name
        - values
      type: object
      properties:
        name:
          type: string
        environmentTemplateIds:
          type: array
          items:
            type: string
        values:
          type: array
          items:
            $ref: '#/components/schemas/ConnectionConfigKVPair'
      description: EndpointConfig.
    ConnectionConfigRequest:
      required:
        - configs
      type: object
      properties:
        configs:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/EndpointConfig'
      description: "ConnectionConfigRequest holds the configs related \nto a connection schema."
    ConnectionConfigResponse:
      required:
        - message
      type: object
      properties:
        message:
          type: string
        configs:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/EndpointConfig'
      description: "ConnectionConfigResponse holds the response returned \nto the client for connection config creation and update."
    Resource:
      required:
        - averageRating
        - createdTime
        - name
        - organizationId
        - resourceType
        - thumbnailUrl
        - totalRatingCount
        - version
        - visibility
      type: object
      properties:
        thumbnailUrl:
          type: string
        averageRating:
          type: number
          format: float
        totalRatingCount:
          type: integer
          format: int64
        createdTime:
          type: string
        name:
          type: string
        version:
          type: string
        resourceType:
          type: string
          enum:
            - CONFIGURATION_GROUP
            - DATABASE
            - SERVICE
        organizationId:
          type: string
        projectId:
          type: string
        summary:
          type: string
        description:
          type: string
        tags:
          type: array
          items:
            type: string
        categories:
          type: array
          items:
            type: string
        visibility:
          type: array
          items:
            type: string
            enum:
              - PUBLIC
              - ORGANIZATION
              - PROJECT
        properties:
          type: object
          additionalProperties:
            type: string
        resourceId:
          type: string
    DatabaseRequest:
      required:
        - cloudProvider
        - cloudRegion
        - databaseServerId
        - databaseServerName
        - databaseType
        - isRestricted
        - status
      type: object
      properties:
        databaseServerId:
          type: string
        databaseServerName:
          type: string
        databaseType:
          type: string
        status:
          type: string
          enum:
            - ERROR
            - DELETED
            - DELETING
            - RESUMING
            - CREATING
            - POWERED_OFF
            - ACTIVE
        isRestricted:
          type: boolean
        cloudProvider:
          type: string
        cloudRegion:
          type: string
        ca_certificate:
          type: string
    Configuration:
      required:
        - configKey
        - configKeyId
        - isFile
        - isSensitive
      type: object
      properties:
        configKeyId:
          type: string
        configKey:
          type: string
        isSensitive:
          type: boolean
        isFile:
          type: boolean
      example:
        configKeyId: b736f4b9-4d30-4a29-a5f4-622afe6ab44f
        configKey: redis.host
        isSensitive: false
        isFile: false
    ConfigurationGroupProperties:
      required:
        - componentNames
        - environmentNames
        - projectNames
      type: object
      properties:
        environmentNames:
          type: array
          items:
            type: string
        projectNames:
          type: array
          items:
            type: string
        componentNames:
          type: array
          items:
            type: string
      example:
        environmentNames:
          - development
          - production
        projectNames:
          - customer-portal
          - payment-service
        componentNames:
          - notification-service
          - data-processor
    ConfigurationGroupRequest:
      required:
        - configGroupId
        - configGroupName
        - configGroupType
        - configurations
      type: object
      properties:
        configGroupId:
          type: string
        configGroupName:
          type: string
        configurations:
          type: array
          items:
            $ref: '#/components/schemas/Configuration'
        configGroupType:
          type: string
          enum:
            - INTERNAL_USER
            - SYSTEM
            - USER
        configGroupProperties:
          $ref: '#/components/schemas/ConfigurationGroupProperties'
      example:
        configGroupId: 88bad586-fe83-40d7-9262-7cedd3f2be32
        configGroupName: Redis Database Configurations
        configGroupType: USER
        configurations:
          - configKeyId: b736f4b9-4d30-4a29-a5f4-622afe6ab44f
            configKey: redis.host
            isSensitive: false
            isFile: false
        configGroupProperties:
          environmentNames:
            - development
            - production
          projectNames:
            - customer-portal
            - payment-service
          componentNames:
            - notification-service
            - data-processor
    PlatformResourceRequest:
      required:
      - platformServiceCategory
      type: object
      properties:
        cloudProvider:
          type: string
        cloudRegion:
          type: string
        platformServiceCategory:
          type: string
      description: |-
        Defines specific details of a platform resource.
        Used to extend the ResourceRequest type.
      example:
        cloudProvider: AWS
        cloudRegion: us-east-1
        platformServiceCategory: Storage
    ResourceTypeRequest:
      oneOf:
        - $ref: '#/components/schemas/DatabaseRequest'
        - $ref: '#/components/schemas/ConfigurationGroupRequest'
        - $ref: '#/components/schemas/PlatformResourceRequest'
    ResourceInfo:
      allOf:
        - $ref: '#/components/schemas/Resource'
        - required:
            - connectionSchemas
          type: object
          properties:
            resourceDetails:
              $ref: '#/components/schemas/ResourceTypeRequest'
            connectionSchemas:
              type: array
              items:
                $ref: '#/components/schemas/SchemaInfo'
    ResourceResponse:
      required:
        - count
        - data
        - pagination
      type: object
      properties:
        count:
          type: integer
          format: int64
        pagination:
          $ref: '#/components/schemas/Pagination'
        data:
          type: array
          items:
            $ref: '#/components/schemas/ResourceInfo'
    TemplateEntry:
      required:
        - name
        - templateId
      type: object
      properties:
        name:
          type: string
        templateId:
          type: string
    ApiSpec:
      required:
        - content
        - specType
      type: object
      properties:
        specType:
          type: string
          enum:
            - UDP
            - TCP
            - WSDL
            - Proto3
            - GraphQL_SDL
            - OpenAPI
            - AsyncAPI
        content: {}
    DefaultConfigs:
      required:
        - key
        - value
      type: object
      properties:
        key:
          type: string
        value:
          type: string
    UserDefinedConfigs:
      required:
        - configType
        - isSensitive
        - name
      type: object
      properties:
        name:
          type: string
        isSensitive:
          type: boolean
        configType:
          type: string
    TemplateRecord:
      required:
        - defaultConfigs
        - description
        - name
        - serviceType
        - spec
        - templateId
        - templateType
        - userDefinedConfigs
        - version
      type: object
      properties:
        templateId:
          type: string
        name:
          type: string
        description:
          type: string
        templateType:
          type: string
        spec:
          $ref: '#/components/schemas/ApiSpec'
        version:
          type: string
        serviceType:
          type: string
          enum:
            - ASYNC_API
            - GRPC
            - GRAPHQL
            - SOAP
            - REST
        defaultConfigs:
          type: array
          items:
            $ref: '#/components/schemas/DefaultConfigs'
        userDefinedConfigs:
          type: array
          items:
            $ref: '#/components/schemas/UserDefinedConfigs'
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://marketplace.prod-choreo-system.svc.cluster.local:9090/marketplace/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://marketplace.prod-choreo-system.svc.cluster.local:9090/marketplace/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/marketplace/0.1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
