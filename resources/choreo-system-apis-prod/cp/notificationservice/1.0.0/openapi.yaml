openapi: 3.0.1
info:
  title: NotificationService
  version: 1.0.0
servers:
  - url: http://alert-notification-service.dev-choreo-system.svc.cluster.local:9090
security:
  - default: []
paths:
  /healthz:
    get:
      parameters: []
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /publishAlerts:
    post:
      operationId: operation_post_/publishAlerts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AlertMessage'
      responses:
        "200":
          description: Ok
        "400":
          description: BadRequest
        "500":
          description: Found unexpected output
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /getAdminEmails/{orgId}:
    get:
      operationId: operation_get_/getAdminEmails/{orgId}
      parameters:
        - name: orgId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: object
        "500":
          description: Found unexpected output
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    MetaData:
      required:
        - alertType
      type: object
      properties:
        alertType:
          type: string
    Properties:
      type: object
      properties: {}
    AlertMessage:
      required:
        - envId
        - metaData
        - orgId
        - properties
        - publisher
        - severity
        - time
      type: object
      properties:
        id:
          type: string
        orgId:
          type: string
        envId:
          type: string
        publisher:
          type: string
        time:
          type: string
        severity:
          type: string
        metaData:
          $ref: '#/components/schemas/MetaData'
        properties:
          $ref: '#/components/schemas/Properties'
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://alert-notification-service.prod-choreo-system.svc.cluster.local:9090
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://alert-notification-service.prod-choreo-system.svc.cluster.local:9090
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/notification-service/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
