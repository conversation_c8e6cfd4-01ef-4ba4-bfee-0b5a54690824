{"data": {"component": {"id": "46493881-5224-4564-a4fe-6707d7abdd84", "name": "notificationservice", "handler": "o41sjl", "description": " ", "displayType": "proxy", "displayName": "NotificationService", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "1.0.0", "labels": [], "createdAt": "2022-01-03T06:12:37.128Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "NotificationService", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/notification-service", "proxyId": "61d293d43234be4e91a47af3", "id": "61d293d43234be4e91a47af3", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "61d293d43234be4e91a47af3", "createdAt": "1641190356623", "updatedAt": "2023-06-13 03:52:34.025", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "46493881-5224-4564-a4fe-6707d7abdd84", "latest": true, "versionStrategy": ""}]}}}