{"id": "667e7240a3a9912fa3455193", "name": "Milvus Proxy", "displayName": "Milvus Proxy", "description": "API to interact with the milvus vector DB", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy", "version": "v1.0", "provider": "278ca2f9-1c2e-44f8-8966-eacb35954a99", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "authorizationHeader": "Authorization", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": ["admin"], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>", "token", "x-request-id"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1719562816821", "lastUpdatedTime": "2024-06-28 08:21:55.873", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://milvus-proxy.choreo-ai:8000/"}, "production_endpoints": {"url": "http://milvus-proxy.choreo-ai:8000/"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/search", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/create_collection", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/upsert_vector", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/filter_data", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/delete_vectors", "verb": "DELETE", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/health", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "278ca2f9-1c2e-44f8-8966-eacb35954a99", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "fb250803-0f53-4e6a-9e41-a5b5fde9c8ee", "versionId": "667e7240a3a9912fa3455193"}}