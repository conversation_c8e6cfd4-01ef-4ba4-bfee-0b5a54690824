openapi: 3.0.0
info:
  title: Milvus Proxy
  version: 0.1.0
servers:
  - url: /
security:
  - default: []
paths:
  /search:
    post:
      summary: Search
      operationId: search_search_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchReqBody'
        required: true
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /create_collection:
    post:
      summary: Create Collection
      operationId: create_collection_create_collection_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateColReqBody'
        required: true
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /upsert_vector:
    post:
      summary: Upsert Vector
      operationId: upsert_vector_upsert_vector_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertReqBody'
        required: true
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /filter_data:
    get:
      summary: Filter Data
      operationId: filter_data_filter_data_get
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterReqBody'
        required: true
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /delete_vectors:
    delete:
      summary: Delete Vectors
      operationId: delete_vectors_delete_vectors_delete
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteReqBody'
        required: true
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /health:
    get:
      summary: Health
      description: Check the api is running
      operationId: health_health_get
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    CreateColReqBody:
      title: CreateColReqBody
      required:
        - collection_name
        - schema_fields
      type: object
      properties:
        collection_name:
          title: Collection Name
          type: string
        schema_fields:
          title: Schema Fields
          type: array
          items: {}
    DeleteReqBody:
      title: DeleteReqBody
      required:
        - collection_name
        - id
      type: object
      properties:
        collection_name:
          title: Collection Name
          type: string
        id:
          title: Id
          type: array
          items: {}
    FilterReqBody:
      title: FilterReqBody
      required:
        - collection_name
        - filter_query
        - output_fields
      type: object
      properties:
        collection_name:
          title: Collection Name
          type: string
        filter_query:
          title: Filter Query
          type: string
        output_fields:
          title: Output Fields
          type: array
          items: {}
    HTTPValidationError:
      title: HTTPValidationError
      type: object
      properties:
        detail:
          title: Detail
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
    SearchReqBody:
      title: SearchReqBody
      required:
        - anns_field
        - collection_name
        - data
        - expr
        - limit
        - output_fields
        - timeout
      type: object
      properties:
        data:
          title: Data
          type: array
          items: {}
        collection_name:
          title: Collection Name
          type: string
        expr:
          title: Expr
          type: string
        output_fields:
          title: Output Fields
          type: array
          items: {}
        timeout:
          title: Timeout
          type: integer
        anns_field:
          title: Anns Field
          anyOf:
            - type: string
            - type: "null"
        limit:
          title: Limit
          type: integer
    UpsertReqBody:
      title: UpsertReqBody
      required:
        - collection_name
        - data
      type: object
      properties:
        collection_name:
          title: Collection Name
          type: string
        data:
          title: Data
          type: object
    ValidationError:
      title: ValidationError
      required:
        - loc
        - msg
        - type
      type: object
      properties:
        loc:
          title: Location
          type: array
          items:
            anyOf:
              - type: string
              - type: integer
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://milvus-proxy.choreo-ai:8000/
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://milvus-proxy.choreo-ai:8000/
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
