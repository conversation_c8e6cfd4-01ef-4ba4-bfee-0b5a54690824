{"data": {"component": {"id": "fb250803-0f53-4e6a-9e41-a5b5fde9c8ee", "name": "Milvus Proxy", "handler": "Milvus Proxy", "description": " ", "displayType": "proxy", "displayName": "Milvus Proxy", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-06-28T08:20:18.197Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Milvus Proxy", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy", "proxyId": "667e7240a3a9912fa3455193", "id": "667e7240a3a9912fa3455193", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "667e7240a3a9912fa3455193", "createdAt": "1719562816821", "updatedAt": "2024-06-28 08:21:55.873", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "fb250803-0f53-4e6a-9e41-a5b5fde9c8ee", "latest": true, "versionStrategy": ""}]}}}