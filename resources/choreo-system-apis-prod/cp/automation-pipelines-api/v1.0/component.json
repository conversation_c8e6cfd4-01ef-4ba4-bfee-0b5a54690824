{"data": {"component": {"id": "94694f46-4a57-4000-bc11-2e6be4736880", "name": "automation-pipelines", "handler": "automation-pipelines", "description": " ", "displayType": "proxy", "displayName": "Automation Pipelines", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-07-09T05:16:13.632Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Automation Pipelines API", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/automation-pipelines", "proxyId": "686df722d34caf156680dfe7", "id": "686df722d34caf156680dfe7", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "686df722d34caf156680dfe7", "createdAt": "1752037154351", "updatedAt": "2025-07-09 04:59:14.351", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "94694f46-4a57-4000-bc11-2e6be4736880", "latest": true, "versionStrategy": ""}]}}}