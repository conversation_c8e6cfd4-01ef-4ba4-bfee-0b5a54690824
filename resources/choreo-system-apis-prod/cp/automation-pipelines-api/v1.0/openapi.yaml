openapi: 3.0.1
info:
  title: Automation Pipelines API
  description: |
    This API provides endpoints for managing automation pipelines, including creating, updating, and deleting pipelines, as well as managing their runs, variables, and secrets.
    It supports operations such as retrieving pipeline details, updating YAML configurations, and managing pipeline runs and their statuses.
  contact: {}
  version: v1.0
servers:
  - url: http://localhost:8080/93tu/automation-pipelines/v1.0
    variables:
      server:
        default: ""
      port:
        default: "8080"
security:
  - default: []
paths:
  /pipelines:
    get:
      operationId: getAutomationPipelines
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelinesResponse'
              example:
                success: true
                message: Successfully retrieved automation pipelines
                data:
                  - id: 550E8400-E29B-41D4-A716-446655440000
                    name: Production Deployment Pipeline
                    description: Pipeline for production deployments
                    createdByUserId: user123
                    createdAt: "2024-03-20T10:00:00Z"
                    lastUpdatedAt: "2024-03-20T10:00:00Z"
                    lastRunStatus: SUCCESSFUL
                    sourceType: git-repo
                  - id: 6BA7B810-9DAD-11D1-80B4-00C04FD430C8
                    name: Staging Pipeline
                    description: Pipeline for staging environment
                    createdByUserId: user456
                    createdAt: "2024-03-19T15:30:00Z"
                    lastUpdatedAt: "2024-03-19T15:30:00Z"
                    lastRunStatus: RUNNING
                    sourceType: yaml
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines
                method: GET
                message: Failed to retrieve automation pipelines
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      operationId: postAutomationPipelines
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAutomationPipelineData'
            example:
              name: New Deployment Pipeline
              description: Pipeline for automated deployments
              sourceType: git-repo
              createdByUserId: user123
              gitRepoData:
                repoOrg: wso2
                repoName: choreo-system-apis
                branch: main
                filePath: pipelines/deployment.yaml
                gitProvider: github
                secretRef: github-secret
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineResponse'
              example:
                success: true
                message: Successfully created automation pipeline
                data:
                  id: 550E8400-E29B-41D4-A716-446655440000
                  name: New Deployment Pipeline
                  description: Pipeline for automated deployments
                  createdByUserId: user123
                  createdAt: "2024-03-20T10:00:00Z"
                  lastUpdatedAt: "2024-03-20T10:00:00Z"
                  sourceType: git-repo
                  yaml:
                    id: 550E8400-E29B-41D4-A716-446655440000
                    versionNumber: 1
                    yamlBlob: c3RlcHM6CiAgLSAtIG5hbWU6IDFhLWhlbGxvLXdvcmxkCiAgICAgIHRlbXBsYXRlOiBwcmludC1tZXNzYWdlCiAgICAtIG5hbWU6IDFiLWhlbGxvLXdvcmxkCiAgICAgIHRlbXBsYXRlOiBwcmludC1tZXNzYWdlCiAgLSBuYW1lOiAyLWhlbGxvLXdvcmxkCiAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQogIC0gLSBuYW1lOiAzYS1oZWxsby13b3JsZAogICAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQogICAgLSBuYW1lOiAzYS1oZWxsby13b3JsZAogICAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQoKdGVtcGxhdGVzOgogIC0gbmFtZTogcHJpbnQtbWVzc2FnZQogICAgaW5saW5lU2NyaXB0OiB8CiAgICAgICMhL2Jpbi9zaAogICAgICBlY2hvICJIZWxsbywgQXJnbyBXb3JrZmxvd3MhIg==
                    createdAt: "2024-03-20T10:00:00Z"
                    createdByUserId: user123
                  gitRepoData:
                    repoOrg: wso2
                    repoName: choreo-system-apis
                    branch: main
                    filePath: pipelines/deployment.yaml
                    gitProvider: github
                    secretRef: github-secret
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines
                method: POST
                message: Failed to create automation pipeline
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}:
    get:
      operationId: getAutomationPipelinesPipelineid
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineResponse'
              example:
                success: true
                message: Successfully retrieved automation pipeline
                data:
                  id: 550E8400-E29B-41D4-A716-446655440000
                  name: Production Deployment Pipeline
                  description: Pipeline for production deployments
                  createdByUserId: user123
                  createdAt: "2024-03-20T10:00:00Z"
                  lastUpdatedAt: "2024-03-20T10:00:00Z"
                  lastRunStatus: SUCCESSFUL
                  sourceType: git-repo
                  yaml:
                    id: 550E8400-E29B-41D4-A716-446655440000
                    versionNumber: 1
                    yamlBlob: c3RlcHM6CiAgLSAtIG5hbWU6IDFhLWhlbGxvLXdvcmxkCiAgICAgIHRlbXBsYXRlOiBwcmludC1tZXNzYWdlCiAgICAtIG5hbWU6IDFiLWhlbGxvLXdvcmxkCiAgICAgIHRlbXBsYXRlOiBwcmludC1tZXNzYWdlCiAgLSBuYW1lOiAyLWhlbGxvLXdvcmxkCiAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQogIC0gLSBuYW1lOiAzYS1oZWxsby13b3JsZAogICAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQogICAgLSBuYW1lOiAzYS1oZWxsby13b3JsZAogICAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQoKdGVtcGxhdGVzOgogIC0gbmFtZTogcHJpbnQtbWVzc2FnZQogICAgaW5saW5lU2NyaXB0OiB8CiAgICAgICMhL2Jpbi9zaAogICAgICBlY2hvICJIZWxsbywgQXJnbyBXb3JrZmxvd3MhIg==
                    createdAt: "2024-03-20T10:00:00Z"
                    createdByUserId: user123
                  gitRepoData:
                    repoOrg: wso2
                    repoName: choreo-system-apis
                    branch: main
                    filePath: pipelines/deployment.yaml
                    gitProvider: github
                    secretRef: github-secret
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000
                method: GET
                message: Failed to retrieve automation pipeline
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      operationId: deleteAutomationPipelinesPipelineid
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineCommonResponse'
              example:
                success: true
                message: Successfully deleted automation pipeline
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000
                method: DELETE
                message: Failed to delete automation pipeline
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/update-yaml:
    post:
      operationId: postAutomationPipelinesPipelineidUpdateYaml
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAutomationPipelineYaml'
            example:
              yaml: c3RlcHM6CiAgLSAtIG5hbWU6IDFhLWhlbGxvLXdvcmxkCiAgICAgIHRlbXBsYXRlOiBwcmludC1tZXNzYWdlCiAgICAtIG5hbWU6IDFiLWhlbGxvLXdvcmxkCiAgICAgIHRlbXBsYXRlOiBwcmludC1tZXNzYWdlCiAgLSBuYW1lOiAyLWhlbGxvLXdvcmxkCiAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQogIC0gLSBuYW1lOiAzYS1oZWxsby13b3JsZAogICAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQogICAgLSBuYW1lOiAzYS1oZWxsby13b3JsZAogICAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQoKdGVtcGxhdGVzOgogIC0gbmFtZTogcHJpbnQtbWVzc2FnZQogICAgaW5saW5lU2NyaXB0OiB8CiAgICAgICMhL2Jpbi9zaAogICAgICBlY2hvICJIZWxsbywgQXJnbyBXb3JrZmxvd3MhIg==
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineYamlResponse'
              example:
                success: true
                message: Successfully updated pipeline YAML
                data:
                  id: 550E8400-E29B-41D4-A716-446655440000
                  versionNumber: 2
                  yamlBlob: c3RlcHM6CiAgLSAtIG5hbWU6IDFhLWhlbGxvLXdvcmxkCiAgICAgIHRlbXBsYXRlOiBwcmludC1tZXNzYWdlCiAgICAtIG5hbWU6IDFiLWhlbGxvLXdvcmxkCiAgICAgIHRlbXBsYXRlOiBwcmludC1tZXNzYWdlCiAgLSBuYW1lOiAyLWhlbGxvLXdvcmxkCiAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQogIC0gLSBuYW1lOiAzYS1oZWxsby13b3JsZAogICAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQogICAgLSBuYW1lOiAzYS1oZWxsby13b3JsZAogICAgICB0ZW1wbGF0ZTogcHJpbnQtbWVzc2FnZQoKdGVtcGxhdGVzOgogIC0gbmFtZTogcHJpbnQtbWVzc2FnZQogICAgaW5saW5lU2NyaXB0OiB8CiAgICAgICMhL2Jpbi9zaAogICAgICBlY2hvICJIZWxsbywgQXJnbyBXb3JrZmxvd3MhIg==
                  createdAt: "2024-03-20T10:00:00Z"
                  createdByUserId: user123
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/update-yaml
                method: POST
                message: Failed to update pipeline YAML
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/runs:
    get:
      operationId: getAutomationPipelinesPipelineidRuns
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineRunsResponse'
              example:
                success: true
                message: Successfully retrieved pipeline runs
                data:
                  - id: 6BA7B810-9DAD-11D1-80B4-00C04FD430C8
                    pipelineId: 550E8400-E29B-41D4-A716-446655440000
                    clusterId: cluster-123
                    runNumber: 1
                    commitId: abc123
                    commitMessage: Update deployment pipeline
                    yamlVersionId: 550E8400-E29B-41D4-A716-446655440000
                    status: SUCCESSFUL
                    startedAt: "2024-03-20T10:00:00Z"
                    updatedAt: "2024-03-20T10:05:00Z"
                    duration: PT5M
                    stoppedByUserId: null
                    versionNumber: 1
                  - id: 6BA7B810-9DAD-11D1-80B4-00C04FD430C9
                    pipelineId: 550E8400-E29B-41D4-A716-446655440000
                    clusterId: cluster-123
                    runNumber: 2
                    commitId: def456
                    commitMessage: Fix build issues
                    yamlVersionId: 550E8400-E29B-41D4-A716-446655440000
                    status: RUNNING
                    startedAt: "2024-03-20T11:00:00Z"
                    updatedAt: "2024-03-20T11:01:00Z"
                    duration: PT1M
                    stoppedByUserId: null
                    versionNumber: 1
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/runs
                method: GET
                message: Failed to retrieve pipeline runs
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      operationId: postAutomationPipelinesPipelineidRuns
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineRunResponse'
              example:
                success: true
                message: Successfully created pipeline run
                data:
                  id: 6BA7B810-9DAD-11D1-80B4-00C04FD430C8
                  pipelineId: 550E8400-E29B-41D4-A716-446655440000
                  clusterId: cluster-123
                  runNumber: 1
                  commitId: abc123
                  commitMessage: Update deployment pipeline
                  yamlVersionId: 550E8400-E29B-41D4-A716-446655440000
                  status: RUNNING
                  startedAt: "2024-03-20T10:00:00Z"
                  updatedAt: "2024-03-20T10:00:00Z"
                  duration: null
                  stoppedByUserId: null
                  versionNumber: 1
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/runs
                method: POST
                message: Failed to create pipeline run
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/runs/{runId}:
    get:
      operationId: getAutomationPipelinesPipelineidRunsRunid
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
        - name: runId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 6BA7B810-9DAD-11D1-80B4-00C04FD430C8
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineRunResponse'
              example:
                success: true
                message: Successfully retrieved pipeline run
                data:
                  id: 6BA7B810-9DAD-11D1-80B4-00C04FD430C8
                  pipelineId: 550E8400-E29B-41D4-A716-446655440000
                  clusterId: cluster-123
                  runNumber: 1
                  commitId: abc123
                  commitMessage: Update deployment pipeline
                  yamlVersionId: 550E8400-E29B-41D4-A716-446655440000
                  status: SUCCESSFUL
                  startedAt: "2024-03-20T10:00:00Z"
                  updatedAt: "2024-03-20T10:05:00Z"
                  duration: PT5M
                  stoppedByUserId: null
                  versionNumber: 1
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/runs/6BA7B810-9DAD-11D1-80B4-00C04FD430C8
                method: GET
                message: Failed to retrieve pipeline run
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      operationId: deleteAutomationPipelinesPipelineidRunsRunid
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
        - name: runId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 6BA7B810-9DAD-11D1-80B4-00C04FD430C8
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineCommonResponse'
              example:
                success: true
                message: Successfully deleted pipeline run
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/runs/6BA7B810-9DAD-11D1-80B4-00C04FD430C8
                method: DELETE
                message: Failed to delete pipeline run
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/runs/{runId}/update-status:
    post:
      operationId: postAutomationPipelinesPipelineidRunsRunidUpdateStatus
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
        - name: runId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 6BA7B810-9DAD-11D1-80B4-00C04FD430C8
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAutomationPipelineRunStatus'
            example:
              status: SUCCESSFUL
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineCommonResponse'
              example:
                success: true
                message: Successfully updated pipeline run status
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/runs/6BA7B810-9DAD-11D1-80B4-00C04FD430C8/update-status
                method: POST
                message: Failed to update pipeline run status
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/runs/{runId}/stop:
    post:
      operationId: postAutomationPipelinesPipelineidRunsRunidStop
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
        - name: runId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 6BA7B810-9DAD-11D1-80B4-00C04FD430C8
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineCommonResponse'
              example:
                success: true
                message: Successfully stopped pipeline run
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/runs/6BA7B810-9DAD-11D1-80B4-00C04FD430C8/stop
                method: POST
                message: Failed to stop pipeline run
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/variables:
    get:
      operationId: getAutomationPipelinesPipelineidVariables
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineVariablesResponse'
              example:
                success: true
                message: Successfully retrieved pipeline variables
                data:
                  - id: var123
                    name: ENVIRONMENT
                    value: production
                    createdAt: "2024-03-20T10:00:00Z"
                    updatedAt: "2024-03-20T10:00:00Z"
                  - id: var456
                    name: REGION
                    value: us-west-2
                    createdAt: "2024-03-20T10:00:00Z"
                    updatedAt: "2024-03-20T10:00:00Z"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/variables
                method: GET
                message: Failed to retrieve pipeline variables
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      operationId: postAutomationPipelinesPipelineidVariables
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
            example:
              name: ENVIRONMENT
              value: production
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineCommonResponse'
              example:
                success: true
                message: Successfully created pipeline variable
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/variables
                method: POST
                message: Failed to create pipeline variable
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/variables/{variableId}:
    delete:
      operationId: deleteAutomationPipelinesPipelineidVariablesVariableid
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
        - name: variableId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: var123
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineCommonResponse'
              example:
                success: true
                message: Successfully deleted pipeline variable
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/variables/var123
                method: DELETE
                message: Failed to delete pipeline variable
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/secrets:
    get:
      operationId: getAutomationPipelinesPipelineidSecrets
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineSecretsResponse'
              example:
                success: true
                message: Successfully retrieved pipeline secrets
                data:
                  - name: GITHUB_TOKEN
                    id: secret123
                    createdAt: "2024-03-20T10:00:00Z"
                    updatedAt: "2024-03-20T10:00:00Z"
                  - name: AWS_ACCESS_KEY
                    id: secret456
                    createdAt: "2024-03-20T10:00:00Z"
                    updatedAt: "2024-03-20T10:00:00Z"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/secrets
                method: GET
                message: Failed to retrieve pipeline secrets
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      operationId: postAutomationPipelinesPipelineidSecrets
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
            example:
              name: GITHUB_TOKEN
              value: ghp_123456789
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineCommonResponse'
              example:
                success: true
                message: Successfully created pipeline secret
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/secrets
                method: POST
                message: Failed to create pipeline secret
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/secrets/{secretId}:
    delete:
      operationId: deleteAutomationPipelinesPipelineidSecretsSecretid
      parameters:
        - name: pipelineId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 550E8400-E29B-41D4-A716-446655440000
        - name: secretId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: secret123
      requestBody:
        content:
          '*/*':
            schema:
              description: Any type of entity body
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutomationPipelineCommonResponse'
              example:
                success: true
                message: Successfully deleted pipeline secret
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
              example:
                reason: Internal Server Error
                path: /api/v1/pipelines/550E8400-E29B-41D4-A716-446655440000/secrets/secret123
                method: DELETE
                message: Failed to delete pipeline secret
                timestamp: "2024-03-20T10:00:00Z"
                status: 500
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    AutomationPipeline:
      required:
        - createdAt
        - createdByUserId
        - description
        - id
        - lastUpdatedAt
        - name
        - sourceType
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        createdByUserId:
          type: string
        createdAt:
          type: string
        lastUpdatedAt:
          type: string
        lastRunStatus:
          type: string
        sourceType:
          type: string
          enum:
            - git-repo
            - yaml
    AutomationPipelinesResponse:
      required:
        - data
        - message
        - success
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/AutomationPipeline'
        message:
          type: string
    ErrorPayload:
      type: object
      properties:
        reason:
          type: string
          description: Reason phrase
        path:
          type: string
          description: Request path
        method:
          type: string
          description: Method type of the request
        message:
          type: string
          description: Error message
        timestamp:
          type: string
          description: Timestamp of the error
        status:
          type: integer
          description: Relevant HTTP status code
          format: int32
    GitRepoData:
      required:
        - branch
        - filePath
        - gitProvider
        - repoName
        - repoOrg
        - secretRef
      type: object
      properties:
        repoOrg:
          type: string
        repoName:
          type: string
        branch:
          type: string
        filePath:
          type: string
        gitProvider:
          type: string
        secretRef:
          type: string
    CreateAutomationPipelineData:
      required:
        - createdByUserId
        - name
        - sourceType
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        sourceType:
          type: string
          enum:
            - git-repo
            - yaml
        createdByUserId:
          type: string
        yamlBlob:
          type: string
        gitRepoData:
          $ref: '#/components/schemas/GitRepoData'
    AutomationPipelineYaml:
      required:
        - createdAt
        - createdByUserId
        - id
        - yamlBlob
      type: object
      properties:
        id:
          type: string
        versionNumber:
          type: integer
          format: int64
        yamlBlob:
          type: string
        createdAt:
          type: string
        createdByUserId:
          type: string
    AutomationPipelineData:
      allOf:
        - $ref: '#/components/schemas/AutomationPipeline'
        - type: object
          properties:
            yaml:
              $ref: '#/components/schemas/AutomationPipelineYaml'
            gitRepoData:
              $ref: '#/components/schemas/GitRepoData'
    AutomationPipelineResponse:
      required:
        - data
        - message
        - success
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AutomationPipelineData'
        message:
          type: string
    UpdateAutomationPipelineYaml:
      required:
        - yaml
      type: object
      properties:
        yaml:
          type: string
    AutomationPipelineYamlResponse:
      required:
        - data
        - message
        - success
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AutomationPipelineYaml'
        message:
          type: string
    AutomationPipelineCommonResponse:
      required:
        - message
        - success
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
    AutomationPipelineRun:
      required:
        - clusterId
        - commitId
        - duration
        - id
        - pipelineId
        - startedAt
        - status
        - stoppedByUserId
        - updatedAt
        - yamlVersionId
      type: object
      properties:
        id:
          type: string
        pipelineId:
          type: string
        clusterId:
          type: string
        runNumber:
          type: integer
          format: int64
        commitId:
          type: string
        commitMessage:
          type: string
        yamlVersionId:
          type: string
        status:
          type: string
        startedAt:
          type: string
        updatedAt:
          type: string
        duration:
          type: string
        stoppedByUserId:
          type: string
        versionNumber:
          type: integer
          format: int64
    AutomationPipelineRunsResponse:
      required:
        - data
        - message
        - success
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/AutomationPipelineRun'
        message:
          type: string
    AutomationPipelineRunResponse:
      required:
        - data
        - message
        - success
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AutomationPipelineRun'
        message:
          type: string
    UpdateAutomationPipelineRunStatus:
      required:
        - status
      type: object
      properties:
        status:
          type: string
          enum:
            - STOPPED
            - FAILED
            - SUCCESSFUL
            - RUNNING
    GetAutomationPipelineVariable:
      required:
        - createdAt
        - id
        - name
        - updatedAt
        - value
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        value:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
    AutomationPipelineVariablesResponse:
      required:
        - data
        - message
        - success
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetAutomationPipelineVariable'
        message:
          type: string
    GetAutomationPipelineSecret:
      required:
        - createdAt
        - id
        - name
        - updatedAt
      type: object
      properties:
        name:
          type: string
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
    AutomationPipelineSecretsResponse:
      required:
        - data
        - message
        - success
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetAutomationPipelineSecret'
        message:
          type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-api-key-header: api-key
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/automation-pipelines/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
