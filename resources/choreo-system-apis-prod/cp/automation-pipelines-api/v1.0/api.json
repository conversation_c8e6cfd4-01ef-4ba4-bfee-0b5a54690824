{"id": "686df722d34caf156680dfe7", "name": "Automation Pipelines API", "displayName": "Automation Pipelines API", "description": "API for managing automation pipelines.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/automation-pipelines", "version": "v1.0", "provider": "0228631a-5468-445b-af43-ba78b5468457", "lifeCycleStatus": "CREATED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": "api-key", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": ["admin", "choreo-platform-engineer"], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1752037154351", "lastUpdatedTime": "2025-07-09 04:59:14.351", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}, "production_endpoints": {"url": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "endpointImplementationType": "ENDPOINT", "subTypeConfiguration": null, "tokenBasedThrottlingConfiguration": null, "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/pipelines", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/update-yaml", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/update-yaml", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/runs", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/runs", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/runs", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/runs", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/runs/{runId}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/runs/{runId}", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/runs/{runId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/runs/{runId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/runs/{runId}/update-status", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/runs/{runId}/update-status", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/runs/{runId}/stop", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/runs/{runId}/stop", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/variables", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/variables", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/variables", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/variables", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/variables/{variableId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/variables/{variableId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/secrets", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/secrets", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/secrets", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/secrets", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/secrets/{secretId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "40b46572-dd9a-4917-a916-e53b267af92f", "backendOperation": {"target": "/pipelines/{pipelineId}/secrets/{secretId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/automation-pipelines"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "0228631a-5468-445b-af43-ba78b5468457", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": null, "componentId": null, "versionId": "686df722d34caf156680dfe7"}}