openapi: 3.0.1
info:
  title: Appdev Authorization Service
  description: |
    This API specification outlines the endpoints and operations for the Authorization Service.
  contact: {}
  version: v1.0
servers:
  - url: https://app.choreo.dev/93tu/appdev-authz/v1.0
security:
  - default: []
paths:
  /authorize:
    post:
      tags:
        - Authorize
      summary: Authorize User
      description: Authorize a user with the provided details.
      operationId: postAuthorize
      requestBody:
        description: Authorization request details.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizationRequestModel'
      responses:
        "200":
          description: Allowed permissions for user groups.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizationResponseModel'
        "400":
          $ref: '#/components/responses/BadRequest'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    Error:
      type: object
      properties:
        code:
          type: string
          description: Error code.
          example: AMT-60xxx
        message:
          type: string
          description: Error message.
          example: Bad Request
        traceId:
          type: string
          description: Trace ID if it exists.
          example: 0b9b6caf-7e5a-17ce-8c76-06be9f3f0fcf
      description: Error that occurred while processing the request.
    AuthorizationRequestModel:
      required:
        - environmentId
        - groups
        - organizationId
        - projectId
        - requestedScopes
      type: object
      properties:
        organizationId:
          type: string
          description: ID of the organization the user belongs to.
          example: c76e6caf-7e5a-17ce-8c76-06be9f3f0fcf
        environmentId:
          type: string
          description: ID of the environment the user is in.
          example: 3fcc6735-4abe-1a60-80ae-c7b3fcc5cf30
        projectId:
          type: string
          description: ID of the project the user is working on.
          example: 9f3f6caf-7e5a-17ce-8c76-06be9f3f0fcf
        groups:
          type: array
          description: Groups the user belongs to.
          items:
            type: string
            example: Engineering Manager
        requestedScopes:
          type: array
          description: Scopes requesting access to.
          example:
            - view_employee
            - view_salary
          items:
            type: string
      description: Request to authorize a user.
    AuthorizationResponseModel:
      required:
        - allowedScopes
      type: object
      properties:
        allowedScopes:
          type: array
          description: Allowed scopes
          example:
            - view_employee
          items:
            type: string
      description: Allowed scopes for groups
  responses:
    BadRequest:
      description: Bad Request. Invalid request or validation error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: AMT-60xxx
            message: Bad Request
    Conflict:
      description: Conflict. Specified resource already exists.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: AMT-60xxx
            message: Conflict
    InternalServerError:
      description: Internal Server Error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: AMT-65xx
            message: Internal Server Error
    NotFound:
      description: Not Found. The specified resource does not exist.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: AMT-60xxx
            message: Not Found
    Unauthorized:
      description: Unauthorized. The user is not authorized to perform the action.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: AMT-60xx
            message: Unauthorized
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/appdev-authz/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
