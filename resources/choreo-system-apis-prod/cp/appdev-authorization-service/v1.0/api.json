{"id": "672b4f0ffc8ed50ac30988e0", "name": "Appdev Authorization Service", "displayName": "Appdev Authorization Service", "description": "API for the Appdev Authorization Service.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/appdev-authz", "version": "v1.0", "provider": "749f8806-73d2-4a26-99d0-399de85e5586", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": false, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1730891535319", "lastUpdatedTime": "2025-04-01 07:48:46.16", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090"}, "production_endpoints": {"url": "http://app-dev-authorization-service.prod-choreo-system.svc.cluster.local:9090"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/authorize", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "72190a6a-1f9c-4f72-bac2-246d3a90c32f", "backendOperation": {"target": "/authorize", "verb": "POST"}}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "749f8806-73d2-4a26-99d0-399de85e5586", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "65bbd86f-f194-4715-9747-87e2049e05e1", "versionId": "672b4f0ffc8ed50ac30988e0"}}