{"data": {"component": {"id": "65bbd86f-f194-4715-9747-87e2049e05e1", "name": "appdev-authorization-service", "handler": "appdev-authorization-service", "description": " ", "displayType": "proxy", "displayName": "Appdev Authorization Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-11-06T11:16:01.459Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Appdev Authorization Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/appdev-authz", "proxyId": "672b4f0ffc8ed50ac30988e0", "id": "672b4f0ffc8ed50ac30988e0", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "672b4f0ffc8ed50ac30988e0", "createdAt": "1730891535319", "updatedAt": "2024-11-06 11:12:15.319", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "65bbd86f-f194-4715-9747-87e2049e05e1", "latest": true, "versionStrategy": ""}]}}}