{"data": {"component": {"id": "2d26895f-421e-408e-a84d-1ba394de0a95", "name": "copilot-data-collector", "handler": "copilot-data-collector", "description": " ", "displayType": "proxy", "displayName": "Copilot Data Collector", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-12-04T12:35:45.488Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "copilot-data-collector", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/copilotdatacollector", "proxyId": "67504961d4aa1b404b9d20f9", "id": "67504961d4aa1b404b9d20f9", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67504961d4aa1b404b9d20f9", "createdAt": "1733314913283", "updatedAt": "2024-12-04 12:21:53.283", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "2d26895f-421e-408e-a84d-1ba394de0a95", "latest": true, "versionStrategy": ""}]}}}