openapi: 3.0.0
info:
  title: Copilot Data Collector
  contact: {}
  version: v1.0
servers:
  - url: https://choreoapis.dev/93tu/copilotdatacollector/v1.0
security:
  - default: []
paths:
  /collect:
    post:
      summary: Collect data
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CopilotData'
        required: true
      responses:
        "200":
          description: Data collected successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  org_id:
                    type: string
                  permission:
                    type: boolean
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /health:
    get:
      summary: Health check
      responses:
        "200":
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    CopilotData:
      type: object
      properties:
        org_id:
          type: string
        session_id:
          type: string
        correlation_id:
          type: string
        user_question:
          type: string
        text_response:
          type: string
        tool_selection:
          type: array
          items:
            $ref: '#/components/schemas/ToolSelection'
    ToolSelection:
      type: object
      properties:
        call_id:
          type: string
        tool:
          type: string
        reason:
          type: string
        question:
          type: string
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://copilot-datacollector.choreo-ai:8080/
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://copilot-datacollector.choreo-ai:8080/
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/copilotdatacollector/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
