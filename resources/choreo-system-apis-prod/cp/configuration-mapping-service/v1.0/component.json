{"data": {"component": {"id": "21f95460-d968-4e2d-8a57-005b072b267b", "name": "configuration-mapping-service", "handler": "ziypsb", "description": " ", "displayType": "proxy", "displayName": "Configuration Mapping Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2023-11-02T07:30:46.901Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Configuration Mapping Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc", "proxyId": "654350269e53c83b329ab097", "id": "654350269e53c83b329ab097", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "654350269e53c83b329ab097", "createdAt": "1698910246275", "updatedAt": "2024-06-26 09:37:47.797", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "21f95460-d968-4e2d-8a57-005b072b267b", "latest": true, "versionStrategy": ""}]}}}