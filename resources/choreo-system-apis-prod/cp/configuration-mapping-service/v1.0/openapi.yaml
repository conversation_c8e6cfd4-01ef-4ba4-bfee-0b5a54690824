openapi: 3.0.1
info:
  title: Configuration Mapping Service
  contact: {}
  version: v1.0
servers:
  - url: https://api.choreo.dev/93tu/config-mapping-svc/v1.0
    description: Production Environment
security:
  - default: []
paths:
  /configs/mappings/deploy:
    get:
      summary: Get configuration mappings for deployment.
      operationId: getConfigsMappingsDeploy
      parameters:
        - name: projectId
          in: query
          description: The UUID of the project.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: componentId
          in: query
          description: The UUID of the component.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: envTemplateId
          in: query
          description: The UUID of the env template.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: deploymentTrackId
          in: query
          description: The UUID of the deployment track
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationMappingResponse'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configs/mappings:
    get:
      summary: Get configuration mappings for UI.
      operationId: getConfigsMappings
      parameters:
        - name: projectId
          in: query
          description: The UUID of the project.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: componentId
          in: query
          description: The UUID of the component.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: envTemplateId
          in: query
          description: The UUID of the env template.
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
        - name: deploymentTrackId
          in: query
          description: The UUID of the deployment track
          required: false
          content:
            application/json:
              schema:
                type: object
                additionalProperties: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationMapping'
        "204":
          description: NoContent
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Create a new configuration mapping.
      operationId: postConfigsMappings
      requestBody:
        description: Configuration Mapping object
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationMapping'
      responses:
        "200":
          description: Ok
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationMapping'
        "204":
          description: NoContent
        "400":
          description: BadRequest
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configs/mappings/{configMappingId}/promote/{targetEnvTemplateId}:
    post:
      summary: Promotes an existing configuration mapping.
      operationId: postConfigsMappingsConfigmappingidPromoteTargetenvtemplateid
      parameters:
        - name: configMappingId
          in: path
          description: Configuration Mapping ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: targetEnvTemplateId
          in: path
          description: Target env template ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationMapping'
        "204":
          description: NoContent
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configs/mappings/resolve-secrets:
    post:
      operationId: postConfigsMappingsResolveSecrets
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResolveSecretsRequest'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResolveSecretsResponse'
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "400":
          description: BadRequest
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    ConfigurationMappingResponse:
      required:
        - configurations
      type: object
      properties:
        mappingId:
          type: string
          description: The mapping UUID (read-only).
        organizationId:
          type: string
          description: The org UUID (read-only).
        projectId:
          type: string
          description: The project UUID (read-only).
        componentId:
          type: string
          description: The component UUID (read-only).
        deploymentTrackId:
          type: string
          description: The component UUID (read-only).
        envTemplateId:
          type: string
          description: The app environment UUID (read-only).
        configurations:
          type: array
          description: The configurations in the configuration mapping.
          items:
            $ref: '#/components/schemas/MountConfiguration'
    MountConfiguration:
      required:
        - envId
        - isSensitive
        - key
        - type
      type: object
      properties:
        keyId:
          type: string
          description: The UUID of the configuration key.
        key:
          type: string
          description: The name of the configuration key.
        valueRef:
          type: string
          description: The valueRef for the configuration key for given environment.
        value:
          type: string
          description: The value for the configuration key for given environment.
        isSensitive:
          type: boolean
          description: Indicates whether the value contains sensitive data.
        type:
          type: string
          description: Indicates whether the configuration is a file or env.
        envId:
          type: string
          description: environment uuid of the configuration.
    Secret:
      required:
        - key
        - valueRef
      type: object
      properties:
        key:
          type: string
          description: The key of the configuration.
          example: API_KEY
        valueRef:
          type: string
          description: The value ref of the configuration.
          example: 123e4567-e89b-12d3-a456-************
        value:
          type: string
          description: The value of the configuration.
          example: secret value
    ResolveSecretsRequest:
      required:
        - componentId
        - deploymentTrackId
        - envTemplateId
        - projectId
        - secrets
      type: object
      properties:
        componentId:
          type: string
          description: The UUID of the component.
          example: 123e4567-e89b-12d3-a456-************
        deploymentTrackId:
          type: string
          description: The UUID of the deployment track.
          example: 8a1f15ee-741d-4c3a-a0ee-f6e57cf0aa10
        envTemplateId:
          type: string
          description: The UUID of the environment template.
          example: bc48d507-2143-4d90-8ee7-b1de0b7b3c34
        projectId:
          type: string
          description: The UUID of the project.
          example: 1e2a478b-2e79-4d30-b223-395de229f9c1
        secrets:
          type: array
          description: requested secret list.
          example:
            - key: API_KEY
              valueRef: 123e4567-e89b-12d3-a456-************
            - key: DB_PASSWORD
              valueRef: 01f016bc-905a-1870-9bda-8549f8c91a42
          items:
            $ref: '#/components/schemas/Secret'
    ResolveSecretsResponse:
      required:
        - secrets
      type: object
      properties:
        secrets:
          type: array
          example:
            - key: API_KEY
              valueRef: 123e4567-e89b-12d3-a456-************
              value: abcd1234-SECRET
            - key: DB_PASSWORD
              valueRef: 01f016bc-905a-1870-9bda-8549f8c91a42
              value: securepassword
          items:
            $ref: '#/components/schemas/Secret'
    ErrorPayload:
      type: object
      properties:
        reason:
          type: string
          description: Reason phrase
        path:
          type: string
          description: Request path
        method:
          type: string
          description: Method type of the request
        message:
          type: string
          description: Error message
        timestamp:
          type: string
          description: Timestamp of the error
        status:
          type: integer
          description: Relevant HTTP status code
          format: int32
    MappingConfiguration:
      required:
        - configGroupName
        - configKeyName
        - isDynamic
        - isFile
        - isSensitive
        - key
        - values
      type: object
      properties:
        keyId:
          type: string
          description: The UUID of the configuration key.
        key:
          type: string
          description: The name of the configuration key.
        values:
          type: array
          description: The value for the configuration key for each environment.
          items:
            $ref: '#/components/schemas/Value'
        isDynamic:
          type: boolean
          description: Indicates whether the value is hardcoded or selected from config group
        isSensitive:
          type: boolean
          description: Indicates whether the value contains sensitive data.
        isFile:
          type: boolean
          description: Indicates whether the configuration is a file.
        configGroupId:
          type: string
          description: Config group UUID if configuration refers to config group.
        configKeyId:
          type: string
          description: Config key UUID if configuration refers to config group.
        configGroupName:
          type: string
          description: Config group name if configuration refers to config group.
        configKeyName:
          type: string
          description: Config key name if configuration refers to config group.
    ConfigurationMapping:
      required:
        - componentId
        - configurations
        - deploymentTrackId
        - envTemplateId
        - projectId
      type: object
      properties:
        projectId:
          type: string
          description: The project UUID (read-only).
        componentId:
          type: string
          description: The component UUID (read-only).
        envTemplateId:
          type: string
          description: The app environment UUID (read-only).
        deploymentTrackId:
          type: string
          description: The deployment track UUID (read-only).
        mappingId:
          type: string
          description: The mapping ID.
        configurations:
          type: array
          description: The configurations in the configuration mapping.
          items:
            $ref: '#/components/schemas/MappingConfiguration'
        createdAt:
          type: string
          description: Indicating when the configuration mapping was created.
        updatedAt:
          type: string
          description: Indicating when the configuration mapping was last updated.
    Value:
      required:
        - environmentUuid
        - value
      type: object
      properties:
        value:
          type: string
          description: The value for the configuration key.
        valueRef:
          type: string
          description: The KV reference for the configuration key if it is sensitive.
        environmentUuid:
          type: string
          description: The template UUID of the environment to which the configuration value is associated.
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - https://api.choreo.dev
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - https://api.choreo.dev
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
