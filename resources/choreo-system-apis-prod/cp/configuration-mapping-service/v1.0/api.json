{"id": "654350269e53c83b329ab097", "name": "Configuration Mapping Service", "displayName": "Configuration Mapping Service", "description": "This api is used to connect to the Configuration Mapping Service service", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-mapping-svc", "version": "v1.0", "provider": "0db20236-8369-43bc-92de-bdac53f3fd7c", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Bronze"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": ["admin"], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1698910246275", "lastUpdatedTime": "2025-06-26 03:41:55.781", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "https://api.choreo.dev"}, "production_endpoints": {"url": "https://api.choreo.dev"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": "", "operations": [{"id": "", "target": "/configs/mappings/deploy", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "1c3c8605-ca37-4c8d-9c39-c664e8e3cbce", "backendOperation": {"target": "/configs/mappings/deploy", "verb": "GET", "endpoint": "https://api.choreo.dev"}}, "operationProxyMapping": null}, {"id": "", "target": "/configs/mappings", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "1c3c8605-ca37-4c8d-9c39-c664e8e3cbce", "backendOperation": {"target": "/configs/mappings", "verb": "GET", "endpoint": "https://api.choreo.dev"}}, "operationProxyMapping": null}, {"id": "", "target": "/configs/mappings", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "1c3c8605-ca37-4c8d-9c39-c664e8e3cbce", "backendOperation": {"target": "/configs/mappings", "verb": "POST", "endpoint": "https://api.choreo.dev"}}, "operationProxyMapping": null}, {"id": "", "target": "/configs/mappings/{configMappingId}/promote/{targetEnvTemplateId}", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "1c3c8605-ca37-4c8d-9c39-c664e8e3cbce", "backendOperation": {"target": "/configs/mappings/{configMappingId}/promote/{targetEnvTemplateId}", "verb": "POST", "endpoint": "https://api.choreo.dev"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": "https://api.choreo.dev", "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "0db20236-8369-43bc-92de-bdac53f3fd7c", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "21f95460-d968-4e2d-8a57-005b072b267b", "versionId": "654350269e53c83b329ab097"}}