{"id": "645c87bf29258d5e5cd9125d", "name": "Components Management", "displayName": "Components Management", "description": "This is the Choreo Components Management API", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt", "version": "1.0.0", "provider": "d63bd494-74d1-4ee6-a98d-c303dcfb6623", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1683785663821", "lastUpdatedTime": "2024-11-06 05:51:24.822", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://api-server.prod-choreo-system.svc.cluster.local:80"}, "production_endpoints": {"url": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "endpointImplementationType": "ENDPOINT", "scopes": [{"scope": {"id": null, "name": "component_config_view", "displayName": "component_config_view", "description": "View component configs", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_create", "displayName": "component_create", "description": "Create component", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_file_view", "displayName": "component_file_view", "description": "View component file", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_init_view", "displayName": "component_init_view", "description": "View component init status", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_logs_view", "displayName": "component_logs_view", "description": "View component logs", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_manage", "displayName": "component_manage", "description": "Manage component", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_trigger", "displayName": "component_trigger", "description": "Trigger component", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "openid", "displayName": "openid", "description": "Authorize access to user details", "bindings": [], "usageCount": null}, "shared": false}], "scopePrefix": "urn:choreosystem:componentsmanagement:", "operations": [{"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/releases/{releaseId}/run-pod", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_trigger", "component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/environments/{environmentUuid}/key-sets/map", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/environments/{environmentUuid}/key-sets/generate", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/environments/{environmentUuid}/key-sets/{keyId}", "verb": "PUT", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgId}/component-count", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_create", "component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/triggers/configurable-generation", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_trigger", "component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/runs/{runId}/logs", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_logs_view", "component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/versions/{versionId}/commits/{commitHash}/configurable-commit-mapping", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_config_view", "component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/init/status", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_init_view", "component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/file/content", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_file_view", "component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/releases/{releaseId}/managed-auth/local-development", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgHandle}/projects/{projectId}/components/{componentId}/releases/{releaseId}/managed-auth/local-development", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/repositories/{gitOrganization}/{repoName}/branches/{branch}/contents", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage", "component_file_view"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "d63bd494-74d1-4ee6-a98d-c303dcfb6623", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "8e9e97ab-945e-4095-b53c-17b290f13760", "versionId": "645c87bf29258d5e5cd9125d"}}