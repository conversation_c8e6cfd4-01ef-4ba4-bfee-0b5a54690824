openapi: 3.0.3
info:
  title: Components Management
  description: This is the Choreo Components Management API
  contact: {}
  version: 1.0.0
servers:
  - url: https://app.choreo.dev/93tu/component-mgt/1.0.0
security:
  - default: []
paths:
  /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/releases/{releaseId}/run-pod:
    post:
      tags:
        - Component
      summary: Run a pod for a specific component release
      description: Run a scheduled or manual task for the given specific component
      operationId: triggerComponent
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/releaseId'
      responses:
        "200":
          description: Success
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_trigger
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_trigger
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/environments/{environmentUuid}/key-sets/map:
    post:
      tags:
        - Component
      summary: Create OAuth Credentials for a specific component release.
      description: Create OAuth Credentials entry for an environment of a component.
      operationId: createKeySet
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/environmentUuid'
      requestBody:
        description: Request body that contains OAuth credentials.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeySetCreateRequest'
      responses:
        "200":
          description: Success
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/environments/{environmentUuid}/key-sets/generate:
    post:
      tags:
        - Component
      summary: Create OAuth Credentials for a specific component release.
      description: Create OAuth Credentials entry for an environment of a component.
      operationId: generateKeySet
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/environmentUuid'
      requestBody:
        description: Request body that contains OAuth credentials.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeySetGenerateRequest'
      responses:
        "200":
          description: Success
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/environments/{environmentUuid}/key-sets/{keyId}:
    put:
      tags:
        - Component
      summary: Create OAuth Credentials for a specific component release.
      description: Create OAuth Credentials entry for an environment of a component.
      operationId: regenerateKeySetSecret
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/environmentUuid'
        - $ref: '#/components/parameters/keyId'
      responses:
        "200":
          description: Success
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgId}/component-count:
    get:
      tags:
        - Component
      summary: Get the component count for a specified organization
      operationId: getComponentCountByOrgId
      parameters:
        - $ref: '#/components/parameters/orgId'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentCountResponse'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_create
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_create
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgUuid}/component-limits:
    get:
      tags:
        - Component
      summary: Get the component limits for a specified organization
      operationId: getComponentLimitsByOrgUuid
      parameters:
        - $ref: '#/components/parameters/orgUuid'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentLimitsResponse'
              example:
                success: true
                data:
                  orgId: 123
                  componentCount: 15
                  isWebappConstrained: false
                message: Component limits retrieved successfully.
        "400":
          description: Bad request
          content:
            application/json:
              example:
                success: false
                message: Invalid orgUuid format. Please provide a valid UUID.
        "404":
          description: Not found
          content:
            application/json:
              example:
                success: false
                message: Organization with the provided UUID not found.
      security:
        - OAuth2Security:
            - component_create
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_create
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/projects/{projectId}/triggers/configurable-generation:
    post:
      tags:
        - Component
      summary: Configurable Generation Trigger
      description: Creates a new trigger for configurable generation in the specified project.
      operationId: configurableGenerationTrigger
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
      requestBody:
        description: The request body for creating a new trigger
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostConfigurationGenerationResponse'
        required: true
      responses:
        "201":
          description: The newly created trigger
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostConfigurationGenerationResponse'
      security:
        - OAuth2Security:
            - component_trigger
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_trigger
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/runs/{runId}/logs:
    get:
      tags:
        - Component
      summary: Get logs for action run
      operationId: getActionRunLogs
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/runId'
      responses:
        "200":
          description: Deployment status information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentRunLog'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_logs_view
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_logs_view
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  ? /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/versions/{versionId}/commits/{commitHash}/configurable-commit-mapping
  : get:
      tags:
        - Component
      summary: Get configuration commit mapping status
      operationId: proxyGetConfigurationCommitMappingStatus
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/versionId'
        - $ref: '#/components/parameters/commitHash'
      responses:
        "200":
          description: Component deployment status information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConfigurationStatusResponse'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_config_view
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_config_view
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/init/status:
    get:
      tags:
        - Component
      summary: Get component initialization status
      description: Retrieves the initialization status of a component
      operationId: getComponentInitStatus
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
      responses:
        "200":
          description: Component deployment status information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentInitStatus'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_init_view
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_init_view
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/file/content:
    get:
      tags:
        - Component
      summary: Get file content for a component
      operationId: getFileContent
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/filePath'
      responses:
        "200":
          description: Component deployment status information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSTResponse'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_file_view
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_file_view
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/projects/{projectId}/components/{componentId}/releases/{releaseId}/managed-auth/local-development:
    get:
      tags:
        - Component
      summary: Get managed authentication local development configurations for a release.
      description: Get managed authentication local development configurations for a release.
      operationId: getLocalDevConfigs
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/releaseId'
      responses:
        "200":
          description: Local development configurations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LocalDevConfig'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - Component
      summary: Update managed authentication local development configurations for a release.
      description: Update managed authentication local development configurations for a release.
      operationId: updateLocalDevConfigs
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/releaseId'
      requestBody:
        description: Request body that contains local development configurations.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LocalDevConfig'
      responses:
        "200":
          description: Success
        "400":
          description: Bad request
      security:
        - OAuth2Security:
            - component_manage
        - default:
            - urn:choreosystem:componentsmanagement:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /repositories/{gitOrganization}/{repoName}/branches/{branch}/contents:
    get:
      tags:
        - Component
      summary: Retrieve GitHub repository contente
      operationId: getRepositoryContent
      parameters:
        - name: gitOrganization
          in: path
          description: GitHub organization name
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: repoName
          in: path
          description: GitHub repository name
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: branch
          in: path
          description: GitHub branch name
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: repository content received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  description:
                    type: string
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default:
            - urn:choreosystem:componentsmanagement:component_manage
            - urn:choreosystem:componentsmanagement:component_file_view
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    PostConfigurationGenerationResponse:
      type: object
      properties:
        success:
          type: string
        message:
          type: string
    ComponentCountResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Flag indicating whether the request was successful
        data:
          $ref: '#/components/schemas/ComponentCount'
        message:
          type: string
          description: Additional information or error message
    ComponentLimitsResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Flag indicating whether the request was successful
          example: true
        data:
          $ref: '#/components/schemas/ComponentLimits'
        message:
          type: string
          description: Additional information or error message
          example: Component limits retrieved successfully.
    ComponentCount:
      type: object
      properties:
        orgId:
          type: integer
          description: The ID of the organization.
        componentCount:
          type: integer
          description: The number of components associated with the organization
    ComponentLimits:
      type: object
      properties:
        orgId:
          type: integer
          description: The ID of the organization.
          example: 123
        componentCount:
          type: integer
          description: The number of components associated with the organization
          example: 15
        isWebappConstrained:
          type: boolean
          description: Indicates whether the webapp deployment hours are utilized by the organization
          example: false
    ConfigurationStatus:
      type: object
      properties:
        configMappingId:
          type: string
          description: The ID of the configuration mapping
        componentId:
          type: string
          description: The ID of the component
        versionId:
          type: string
          description: The ID of the version
        sourceCommitId:
          type: string
          description: The ID of the source commit
        gitOpsCommitId:
          type: string
          description: The ID of the GitOps commit
        workflowStatus:
          type: string
          description: The status of the workflow
        createdTime:
          type: string
          description: The time the configuration status was created
        updatedTime:
          type: string
          description: The time the configuration status was last updated
        runId:
          type: string
          description: The ID of the run
        runStatus:
          type: string
          description: The status of the run
    GetConfigurationStatusResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the request was successful or not
        data:
          $ref: '#/components/schemas/ConfigurationStatus'
        message:
          type: string
          description: A message about the configuration status
    ComponentInitStatus:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates if the initialization was successful or not
        message:
          type: string
          description: A message related to the initialization status
        data:
          type: object
          properties:
            conclusion:
              type: string
              description: The conclusion of the initialization process
            status:
              type: string
              description: The status of the initialization process
    GetSTResponse:
      type: object
      properties:
        name:
          type: string
          description: The name of the file or directory
        path:
          type: string
          description: The API path for the file or directory
        sha:
          type: string
          description: The SHA hash of the file or directory
        size:
          type: integer
          description: The size of the file in bytes
        url:
          type: string
          description: The API URL for the file or directory
        html_url:
          type: string
          description: The HTML URL for the file or directory
        git_url:
          type: string
          description: The Git URL for the file or directory
        download_url:
          type: string
          description: The download URL for the file
        type:
          type: string
          description: The type of file or directory
        content:
          type: string
          description: The content of the file if it is a file and its size is not too large
        encoding:
          type: string
          description: The encoding used for the file content
    Step:
      type: object
      properties:
        name:
          type: string
        status:
          type: string
        conclusion:
          type: string
        number:
          type: integer
        started_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time
    Pipeline:
      type: object
      properties:
        init:
          type: object
          properties:
            status:
              type: string
            steps:
              type: array
              items:
                $ref: '#/components/schemas/Step'
        build:
          type: object
          properties:
            log:
              type: string
            status:
              type: string
            steps:
              type: array
              items:
                $ref: '#/components/schemas/Step'
        deploy:
          type: object
          properties:
            status:
              type: string
            steps:
              type: array
              items:
                $ref: '#/components/schemas/Step'
    ComponentRunLog:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/Pipeline'
        message:
          type: string
    KeySetCreateRequest:
      type: object
      properties:
        clientId:
          type: string
        clientSecret:
          type: string
        idpId:
          type: string
          format: uuid
        authorizationClientId:
          type: string
        authorizationClientSecret:
          type: string
        keySetType:
          type: string
    KeySetGenerateRequest:
      type: object
      properties:
        redirect_uris:
          type: array
          example:
            - str1
            - str2
            - str3
          items:
            type: string
        grant_types:
          type: array
          example:
            - authorization_code
            - refresht_token
          items:
            type: string
    LocalDevConfig:
      type: object
      properties:
        enable:
          type: boolean
          example: true
        allowedUris:
          type: array
          example:
            - http://localhost:10000
            - http://localhost:10001
          items:
            type: string
  parameters:
    orgHandle:
      name: orgHandle
      in: path
      description: The handle of the organization
      required: true
      style: simple
      explode: false
      schema:
        type: string
    orgId:
      name: orgId
      in: path
      description: The ID of the organization
      required: true
      style: simple
      explode: false
      schema:
        type: string
    orgUuid:
      name: orgUuid
      in: path
      description: The UUID of the organization
      required: true
      style: simple
      explode: false
      schema:
        type: string
        example: 123e4567-e89b-12d3-a456-************
    projectId:
      name: projectId
      in: path
      description: Project ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    componentId:
      name: componentId
      in: path
      description: Component ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    releaseId:
      name: releaseId
      in: path
      description: Release ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    environmentUuid:
      name: environmentUuid
      in: path
      description: Environment UUID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    keyId:
      name: keyId
      in: path
      description: OAuth Client ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    runId:
      name: runId
      in: path
      description: The action run ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    versionId:
      name: versionId
      in: path
      description: The version ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    commitHash:
      name: commitHash
      in: path
      description: The commit hash
      required: true
      style: simple
      explode: false
      schema:
        type: string
    filePath:
      name: filePath
      in: query
      description: Path of the file to get the content of
      required: true
      style: form
      explode: true
      schema:
        type: string
  securitySchemes:
    OAuth2Security:
      type: oauth2
      flows:
        password:
          tokenUrl: https://localhost:9443/oauth2/token
          scopes:
            openid: Authorize access to user details
            component_trigger: Trigger component
            component_create: Create component
            component_logs_view: View component logs
            component_config_view: View component configs
            component_init_view: View component init status
            component_file_view: View component file
            component_manage: Manage component
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            urn:choreosystem:componentsmanagement:component_config_view: View component configs
            urn:choreosystem:componentsmanagement:component_manage: Manage component
            urn:choreosystem:componentsmanagement:component_create: Create component
            urn:choreosystem:componentsmanagement:component_trigger: Trigger component
            urn:choreosystem:componentsmanagement:component_file_view: View component file
            urn:choreosystem:componentsmanagement:component_logs_view: View component logs
            urn:choreosystem:componentsmanagement:openid: Authorize access to user details
            urn:choreosystem:componentsmanagement:component_init_view: View component init status
          x-scopes-bindings:
            urn:choreosystem:componentsmanagement:openid: ""
            urn:choreosystem:componentsmanagement:component_create: ""
            urn:choreosystem:componentsmanagement:component_init_view: ""
            urn:choreosystem:componentsmanagement:component_trigger: ""
            urn:choreosystem:componentsmanagement:component_manage: ""
            urn:choreosystem:componentsmanagement:component_config_view: ""
            urn:choreosystem:componentsmanagement:component_file_view: ""
            urn:choreosystem:componentsmanagement:component_logs_view: ""
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://api-server.prod-choreo-system.svc.cluster.local:80
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://api-server.prod-choreo-system.svc.cluster.local:80
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
