{"data": {"component": {"id": "8e9e97ab-945e-4095-b53c-17b290f13760", "name": "components-management", "handler": "doj<PERSON><PERSON>", "description": " ", "displayType": "proxy", "displayName": "Components Management", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "1.0.0", "labels": [], "createdAt": "2023-05-11T06:14:30.188Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "Components Management", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-mgt", "proxyId": "645c87bf29258d5e5cd9125d", "id": "645c87bf29258d5e5cd9125d", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "645c87bf29258d5e5cd9125d", "createdAt": "1683785663821", "updatedAt": "2024-06-10 06:11:06.221", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "8e9e97ab-945e-4095-b53c-17b290f13760", "latest": true, "versionStrategy": ""}]}}}