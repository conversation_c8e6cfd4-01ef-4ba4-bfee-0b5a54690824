{"data": {"component": {"id": "87f4ca0c-34a9-4dd3-b80a-b2f4c70ed154", "name": "arch-agent-api-design", "handler": "arch-agent-api-design", "description": " ", "displayType": "proxy", "displayName": "Architect Agent API Design", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-07-25T11:45:48.833Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Architect Agent API Design", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/architect-agent-api-design", "proxyId": "68836dada8a44317db28ef32", "id": "68836dada8a44317db28ef32", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "68836dada8a44317db28ef32", "createdAt": "1753443757014", "updatedAt": "2025-07-25 11:42:37.014", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "87f4ca0c-34a9-4dd3-b80a-b2f4c70ed154", "latest": true, "versionStrategy": ""}]}}}