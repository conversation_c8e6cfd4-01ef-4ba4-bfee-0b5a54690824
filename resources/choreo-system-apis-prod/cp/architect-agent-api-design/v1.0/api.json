{"id": "68836dada8a44317db28ef32", "name": "Architect Agent API Design", "displayName": "Architect Agent API Design", "description": "API for Architect Agent API Design", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/architect-agent-api-design", "version": "v1.0", "provider": "749f8806-73d2-4a26-99d0-399de85e5586", "lifeCycleStatus": "CREATED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "authorizationHeader": "Authorization", "apiKeyHeader": "api-key", "securityScheme": [], "maxTps": null, "visibility": "RESTRICTED", "visibleRoles": ["admin"], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>", "token", "x-request-id"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1753443757014", "lastUpdatedTime": "2025-07-25 11:42:37.014", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://architect-agent-apis.choreo-ai:9090/"}, "production_endpoints": {"url": "http://architect-agent-apis.choreo-ai:9090/"}}, "endpointImplementationType": "ENDPOINT", "subTypeConfiguration": {"subType": "DEFAULT", "egress": false, "configuration": null}, "tokenBasedThrottlingConfiguration": null, "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/analyze/project", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/analyze/project", "verb": "POST", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}, {"id": "", "target": "/report/project", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/report/project", "verb": "GET", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}, {"id": "", "target": "/report/component", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/report/component", "verb": "GET", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}, {"id": "", "target": "/documents", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/documents", "verb": "GET", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}, {"id": "", "target": "/documents", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/documents", "verb": "POST", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}, {"id": "", "target": "/documents/{documentId}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/documents/{documentId}", "verb": "GET", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}, {"id": "", "target": "/documents/{documentId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/documents/{documentId}", "verb": "PUT", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}, {"id": "", "target": "/documents/{documentId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/documents/{documentId}", "verb": "DELETE", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}, {"id": "", "target": "/health", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "28923841-e7a8-416a-bee9-c6bd63763265", "backendOperation": {"target": "/health", "verb": "GET", "endpoint": "http://architect-agent-apis.choreo-ai:9090/"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "749f8806-73d2-4a26-99d0-399de85e5586", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "87f4ca0c-34a9-4dd3-b80a-b2f4c70ed154", "versionId": "68836dada8a44317db28ef32"}}