openapi: 3.0.1
info:
  title: Architect Agent API Design
  contact: {}
  version: v1.0
servers:
  - url: https://choreoapis.dev/93tu/architect-agent-api-design/v1.0
    variables:
      server:
        default: ""
      port:
        default: "9090"
security:
  - default: []
paths:
  /analyze/project:
    post:
      operationId: postAnalyzeProject
      parameters:
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectAnalysisRequest'
        required: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - {}
                  - $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "201":
          description: Created
          content:
            application/json:
              schema:
                type: object
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /report/project:
    get:
      operationId: getReportProject
      parameters:
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
        - name: projectId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: orgUuid
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - {}
                  - $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - $ref: '#/components/schemas/ProjectAnalysisResponse'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /report/component:
    get:
      operationId: getReportComponent
      parameters:
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
        - name: versionId
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: componentId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: projectId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: orgUuid
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                oneOf:
                  - {}
                  - $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - $ref: '#/components/schemas/ComponentAnalysisResponse'
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /documents:
    get:
      operationId: getDocuments
      parameters:
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "202":
          description: Accepted
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      operationId: postDocuments
      parameters:
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
        required: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "202":
          description: Accepted
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /documents/{documentId}:
    get:
      operationId: getDocumentsDocumentid
      parameters:
        - name: documentId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "202":
          description: Accepted
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    put:
      operationId: putDocumentsDocumentid
      parameters:
        - name: documentId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
        required: true
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "202":
          description: Accepted
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      operationId: deleteDocumentsDocumentid
      parameters:
        - name: documentId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "202":
          description: Accepted
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /health:
    get:
      operationId: getHealth
      parameters:
        - name: Authorization
          in: header
          required: false
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "401":
          description: Unauthorized
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: object
        "400":
          description: BadRequest
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    ApiAnalysisResponse:
      type: object
      additionalProperties: false
      properties:
        apiId:
          type: string
        apiName:
          type: string
        summary:
          type: string
        overallScore:
          type: integer
          format: int64
        detailedFindings:
          type: array
          items:
            $ref: '#/components/schemas/ComplianceFinding'
      required:
        - apiId
        - apiName
        - detailedFindings
        - overallScore
        - summary
    ComplianceFinding:
      type: object
      additionalProperties: false
      properties:
        guideline:
          type: string
        status:
          type: string
        priority:
          type: integer
          format: int64
        description:
          type: string
        suggestedFix:
          type: string
      required:
        - description
        - guideline
        - priority
        - status
        - suggestedFix
    ComponentAnalysisResponse:
      type: object
      additionalProperties: false
      properties:
        date_created:
          type: string
        componentId:
          type: string
        apiAnalysisResults:
          type: array
          items:
            $ref: '#/components/schemas/ApiAnalysisResponse'
      required:
        - componentId
    DocumentRequest:
      type: object
      additionalProperties: false
      properties:
        orgUuid:
          type: string
        name:
          type: string
        fileType:
          type: string
        description:
          type: string
        content:
          type: string
        isShared:
          type: boolean
      required:
        - content
        - description
        - fileType
        - name
        - orgUuid
    ErrorPayload:
      type: object
      properties:
        timestamp:
          type: string
        status:
          type: integer
          format: int64
        reason:
          type: string
        message:
          type: string
        path:
          type: string
        method:
          type: string
      required:
        - message
        - method
        - path
        - reason
        - status
        - timestamp
    ProjectAnalysisRequest:
      type: object
      additionalProperties: false
      properties:
        orgUuid:
          type: string
        orgId:
          type: integer
          format: int64
        projectId:
          type: string
      required:
        - orgId
        - orgUuid
        - projectId
    ProjectAnalysisResponse:
      type: object
      properties:
        analysisStatus:
          type: string
        date_created:
          type: string
        projectId:
          type: string
        projectName:
          type: string
        summary:
          type: string
        totalApis:
          type: integer
          format: int64
        analyzedApis:
          type: integer
          format: int64
        overallScore:
          type: number
          format: double
        detailedFindings:
          type: array
          items:
            $ref: '#/components/schemas/ComplianceFinding'
        apiAnalysisResults:
          type: array
          items:
            $ref: '#/components/schemas/ApiAnalysisResponse'
      required:
        - detailedFindings
        - overallScore
        - projectId
        - projectName
        - summary
    SuccessResponse:
      type: object
      additionalProperties: false
      properties:
        status:
          type: string
        message:
          type: string
      required:
        - message
        - status
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-api-key-header: api-key
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://architect-agent-apis.choreo-ai:9090/
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://architect-agent-apis.choreo-ai:9090/
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/architect-agent-api-design/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
