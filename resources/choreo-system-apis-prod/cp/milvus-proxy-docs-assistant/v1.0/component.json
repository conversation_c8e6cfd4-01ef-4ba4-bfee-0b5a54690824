{"data": {"component": {"id": "3856bfc7-a436-40ce-a3f3-aa2918739aea", "name": "Milvus Proxy Docs Assistant", "handler": "Milvus Proxy Docs Assistant", "description": " ", "displayType": "proxy", "displayName": "Milvus Proxy Docs Assistant", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-06-28T07:57:59.564Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Milvus Proxy Docs Assistant", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy-docs-assistant", "proxyId": "667e6d06ea5fb93b6741cf29", "id": "667e6d06ea5fb93b6741cf29", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "667e6d06ea5fb93b6741cf29", "createdAt": "1719561478142", "updatedAt": "2024-06-28 08:00:19.403", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "3856bfc7-a436-40ce-a3f3-aa2918739aea", "latest": true, "versionStrategy": ""}]}}}