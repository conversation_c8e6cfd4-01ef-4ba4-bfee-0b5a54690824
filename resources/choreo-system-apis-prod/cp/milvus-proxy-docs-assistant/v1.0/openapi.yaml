openapi: 3.0.0
info:
  title: Milvus Proxy Docs Assistant
  version: 0.1.0
servers:
  - url: /
security:
  - default: []
paths:
  /doc_search:
    post:
      summary: Doc Search
      operationId: doc_search_doc_search_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocSearchReqBody'
        required: true
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
        "422":
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /health:
    get:
      summary: Health
      description: Check the api is running
      operationId: health_health_get
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    DocSearchReqBody:
      title: DocSearchReqBody
      required:
        - anns_field
        - collection_name
        - data
        - limit
        - output_fields
        - timeout
      type: object
      properties:
        data:
          title: Data
          type: array
          items: {}
        collection_name:
          title: Collection Name
          type: string
        output_fields:
          title: Output Fields
          type: array
          items: {}
        timeout:
          title: Timeout
          type: integer
        anns_field:
          title: Anns Field
          anyOf:
            - type: string
            - type: "null"
        limit:
          title: Limit
          type: integer
    HTTPValidationError:
      title: HTTPValidationError
      type: object
      properties:
        detail:
          title: Detail
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'
    ValidationError:
      title: ValidationError
      required:
        - loc
        - msg
        - type
      type: object
      properties:
        loc:
          title: Location
          type: array
          items:
            anyOf:
              - type: string
              - type: integer
        msg:
          title: Message
          type: string
        type:
          title: Error Type
          type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://milvus-proxy.choreo-ai:8000/
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://milvus-proxy.choreo-ai:8000/
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/milvus-proxy-docs-assistant/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
