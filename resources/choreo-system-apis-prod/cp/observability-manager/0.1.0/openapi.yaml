openapi: 3.0.3
info:
  title: Observability Manager
  contact: {}
  version: 0.1.0
servers:
  - url: http://obsmanager.{dev/93tu/observability/obsmanager/0.1.0
security:
  - default: []
paths:
  /authorize:
    get:
      summary: Authorize by either release id, component id or project id
      description: |
        Checks whether the user is eligible to view the logs associated with the
        release id, componentId or project id.
      operationId: authorize
      parameters:
        - $ref: '#/components/parameters/authorizeQueryParams'
      responses:
        "200":
          description: Successful response optionally containing namespace.
          content:
            application/json:
              schema:
                type: object
                properties:
                  namespace:
                    type: string
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /org:
    get:
      summary: Authorize and get org details
      description: |
        Authorize the user and return the org details.
      operationId: authorizeAndGetOrg
      parameters:
        - $ref: '#/components/parameters/environmentIdQueryParam'
      responses:
        "200":
          description: Org details of the user.
          content:
            application/json:
              schema:
                type: object
                properties:
                  organization:
                    type: object
                    properties:
                      uuid:
                        pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
                        type: string
                        format: uuid
                      handle:
                        type: string
                      id:
                        type: integer
                      name:
                        type: string
                  namespace:
                    type: string
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /gwLogsParams:
    post:
      summary: Authorize and get gateway logs parameters
      description: |
        Authorize the user and return the gateway logs parameters.
      operationId: authorizeAndGetGwLogsParams
      parameters:
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/backendTokenHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GwLogsParamRequest'
        required: true
      responses:
        "200":
          description: Gateway access logs query params.
          content:
            application/json:
              schema:
                type: object
                properties:
                  apiIdToVersionMap:
                    type: object
                  gatewayVhosts:
                    type: array
                    items:
                      type: string
                  orgUuid:
                    type: string
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /healthz:
    get:
      summary: Retrieve health of the obsmanager
      description: |
        Retrieve the status of the API. This may not reflect exactly the current state of the API.
      operationId: checkHealth
      responses:
        "200":
          description: Response indicating an ok status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthOkResponse'
        "503":
          description: Response indicating a service unavailable status.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /projectEnvironments:
    get:
      summary: Authorizes the organization and fetches all project environments based on the environment template id
      description: |
        Authorizes the organization and fetches all project environments based on the environment template id
      operationId: getProjectEnvironments
      parameters:
        - name: environmentTemplateId
          in: query
          description: Environment template ID to filter environments
          required: false
          style: form
          explode: true
          schema:
            type: string
            format: uuid
        - $ref: '#/components/parameters/requestIdHeader'
        - $ref: '#/components/parameters/backendTokenHeader'
      responses:
        "200":
          description: Successful response containing project uuids and corresponding environment uuids.
          content:
            application/json:
              schema:
                type: object
                properties:
                  projectEnvironments:
                    type: array
                    items:
                      $ref: '#/components/schemas/ProjectEnvironment'
              example:
                projectEnvironments:
                  - projectId: f47ac10b-58cc-4372-a567-0e02b2c3d479
                    environmentId: 9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d
                  - projectId: 550e8400-e29b-41d4-a716-************
                    environmentId: 6ba7b810-9dad-11d1-80b4-00c04fd430c8
        "400":
          description: Response indicating an invalid input.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    GwLogsParamRequest:
      required:
        - componentId
        - environmentId
      type: object
      properties:
        componentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Component id of the user application.
          format: uuid
        environmentId:
          pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
          type: string
          description: Environment id of the user application.
          format: uuid
        versionList:
          type: array
          description: List of versions of the application.
          items:
            pattern: ^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)$
            type: string
            format: semantic-version
    ErrorResponse:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/BackendErrorCodes'
        message:
          type: string
          description: The summarized error message
    HealthOkResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - OK
    HealthErrorResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - ERROR
    BackendErrorCodes:
      type: integer
      description: |
        Error code indicating the specific error that occurred.
        * OBS-M-10 - Parameters not received
        * OBS-M-12 - Invalid input
        * OBS-M-13 - Authentication failure
        * OBS-M-22 - Internal error
      enum:
        - null
        - null
        - null
        - null
    ProjectEnvironment:
      type: object
      properties:
        projectId:
          type: string
          description: Project UUID
          format: uuid
        environmentId:
          type: string
          description: Environment UUID of the project that corresponds to the environment template ID
          format: uuid
  responses:
    Unauthorized:
      description: Response indicating an unauthorized request.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            InvalidCredentials:
              description: |
                The provided credentials does not have permission to view logs.
              value:
                code: OBS-L-13
                message: The provided credentials are not valid
    InternalError:
      description: Response indicating an internal server error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  parameters:
    requestIdHeader:
      name: X-Request-Id
      in: header
      description: |
        Request ID for the request in order to track it across systems.
        A new id will be generated if not provided.
      required: false
      style: simple
      explode: false
      schema:
        pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
        type: string
        format: uuid
    backendTokenHeader:
      name: x-jwt-assertion
      in: header
      description: |
        Authorization header containing the backend JWT token.
      required: true
      style: simple
      explode: false
      schema:
        type: string
        format: <JWT>
    authorizeQueryParams:
      name: authorizeParams
      in: query
      description: Check authorization based on one of these params.
      required: true
      style: form
      explode: true
      schema:
        properties:
          releaseId:
            pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
            type: string
            description: Release Id of the user application.
            format: uuid
          componentId:
            pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
            type: string
            description: Component Id of the user application.
            format: uuid
          projectId:
            pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
            type: string
            format: uuid
          environmentId:
            pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
            type: string
            description: Id of the environment the user application is deployed to.
            format: uuid
          needs:
            type: array
            description: List of information required in the response. Currently only supports `namespace`.
            items:
              type: string
        anyOf:
          - required:
              - releaseId
          - required:
              - componentId
              - environmentId
          - required:
              - environmentId
              - projectId
    environmentIdQueryParam:
      name: environmentId
      in: query
      description: Id of the environment the user application is deployed to.
      required: true
      style: form
      explode: true
      schema:
        pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
        type: string
        format: uuid
    componentIdQueryParam:
      name: componentId
      in: query
      description: Component Id of the user application.
      required: true
      style: form
      explode: true
      schema:
        pattern: ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$
        type: string
        format: uuid
  examples: {}
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://obsmanager.prod-choreo-system.svc.cluster.local:9094/obsmanager
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://obsmanager.prod-choreo-system.svc.cluster.local:9094/obsmanager
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager/0.1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
