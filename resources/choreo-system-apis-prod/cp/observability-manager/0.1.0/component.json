{"data": {"component": {"id": "cf4ae16a-23d0-420a-923f-893c8592859b", "name": "observability-manager", "handler": "sdqjeu", "description": " ", "displayType": "proxy", "displayName": "Observability Manager", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "0.1.0", "labels": [], "createdAt": "2023-07-17T06:31:51.328Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "0.1.0", "proxyName": "Observability Manager", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/observability/obsmanager", "proxyId": "64b4e04e6f73c71fa9969691", "id": "64b4e04e6f73c71fa9969691", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "64b4e04e6f73c71fa9969691", "createdAt": "1689575502411", "updatedAt": "2023-08-22 04:43:17.167", "apiVersion": "0.1.0", "branch": null, "description": null, "componentId": "cf4ae16a-23d0-420a-923f-893c8592859b", "latest": true, "versionStrategy": ""}]}}}