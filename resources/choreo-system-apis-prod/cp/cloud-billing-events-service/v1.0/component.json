{"data": {"component": {"id": "602dda31-afca-4585-9f90-e0d8243e715d", "name": "billing-events-service", "handler": "billing-events-service", "description": " ", "displayType": "proxy", "displayName": "Cloud Billing Events Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-07-17T09:32:58.278Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Cloud Billing Events Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/billing-events", "proxyId": "6878ad817ab2ee657b846112", "id": "6878ad817ab2ee657b846112", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "6878ad817ab2ee657b846112", "createdAt": "1752739201496", "updatedAt": "2025-07-17 08:00:01.496", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "602dda31-afca-4585-9f90-e0d8243e715d", "latest": true, "versionStrategy": ""}]}}}