openapi: 3.0.3
info:
  title: Cloud Billing Events Service
  description: API for managing cloud billing events.
  contact: {}
  version: v1.0
servers:
  - url: https://choreoapis.dev/93tu/billing-events/v1.0
  - url: http://localhost:8080/93tu/billing-events/v1.0
    description: Local development server
security:
  - default: []
tags:
  - name: Websubhub Callback
    description: WebsubHub callback endpoints for external integrations
paths:
  /callback:
    get:
      tags:
        - Websubhub Callback
      summary: Callback endpoint verification
      description: Processes websubhub verification requests with challenge-response mechanism
      operationId: handleVerification
      parameters:
        - name: hub.mode
          in: query
          description: The verification mode (typically 'subscribe' or 'unsubscribe')
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: subscribe
        - name: hub.topic
          in: query
          description: The topic being subscribed to or unsubscribed from
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: https://api.example.com/events
        - name: hub.challenge
          in: query
          description: A random string that must be echoed back to verify the endpoint
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: 1234567890abcdef
        - name: hub.reason
          in: query
          description: Reason for the verification (typically for unsubscribe operations)
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: User requested unsubscription
        - name: message
          in: query
          description: Additional message related to the verification
          required: false
          style: form
          explode: true
          schema:
            type: string
            example: Verification message
      responses:
        "200":
          description: Callback received successfully
          content:
            text/plain:
              schema:
                type: string
                description: The challenge string echoed back
                example: 1234567890abcdef
        "400":
          description: Verification failed
          content:
            text/plain:
              schema:
                type: string
                example: Error processing verification
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - Websubhub Callback
      summary: Callback endpoint content processing
      description: Processes websubhub content delivery from external systems
      operationId: handleContentDelivery
      parameters:
        - name: X-Hub-Signature
          in: header
          description: Signature for verifying the authenticity of the payload
          required: false
          style: simple
          explode: false
          schema:
            type: string
            example: sha256=5d41402abc4b2a76b9719d911017c592
      requestBody:
        description: Payload to be sent
        content:
          application/json:
            schema:
              type: object
              additionalProperties: true
              description: Generic event payload that varies based on the event source
        required: true
      responses:
        "200":
          description: Callback processed successfully
          content:
            text/plain:
              schema:
                type: string
                example: OK
        "400":
          description: Error processing event
          content:
            text/plain:
              schema:
                type: string
                example: Error processing event
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: true
x-wso2-api-key-header: api-key
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://cloud-billing-api-service.prod-choreo-system.svc.cluster.local:8080
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://cloud-billing-api-service.prod-choreo-system.svc.cluster.local:8080
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/billing-events/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
