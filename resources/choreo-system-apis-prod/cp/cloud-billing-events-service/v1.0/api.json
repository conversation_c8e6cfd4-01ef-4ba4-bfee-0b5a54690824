{"id": "6878ad817ab2ee657b846112", "name": "Cloud Billing Events Service", "displayName": "Cloud Billing Events Service", "description": "API for managing cloud billing events.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/billing-events", "version": "v1.0", "provider": "a579828c-19e4-4406-92f4-15df9033c2fc", "lifeCycleStatus": "CREATED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": "api-key", "securityScheme": [], "maxTps": null, "visibility": "PUBLIC", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1752739201496", "lastUpdatedTime": "2025-07-17 08:00:01.496", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://cloud-billing-api-service.prod-choreo-system.svc.cluster.local:8080"}, "production_endpoints": {"url": "http://cloud-billing-api-service.prod-choreo-system.svc.cluster.local:8080"}}, "endpointImplementationType": "ENDPOINT", "subTypeConfiguration": {"subType": "DEFAULT", "egress": false, "configuration": null}, "tokenBasedThrottlingConfiguration": null, "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/callback", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "dee2dce0-9617-4638-8b9c-c881384b65eb", "backendOperation": {"target": "/callback", "verb": "GET", "endpoint": "http://cloud-billing-api-service.prod-choreo-system.svc.cluster.local:8080"}}, "operationProxyMapping": null}, {"id": "", "target": "/callback", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "dee2dce0-9617-4638-8b9c-c881384b65eb", "backendOperation": {"target": "/callback", "verb": "POST", "endpoint": "http://cloud-billing-api-service.prod-choreo-system.svc.cluster.local:8080"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "a579828c-19e4-4406-92f4-15df9033c2fc", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "602dda31-afca-4585-9f90-e0d8243e715d", "versionId": "6878ad817ab2ee657b846112"}}