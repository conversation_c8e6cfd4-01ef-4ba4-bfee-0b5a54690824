# Cloud Billing Events Service - OpenAPI Specification

This repository contains the OpenAPI definition for the **Cloud Billing Events Service**, a REST API designed to handle callback events from WebSubHub-compatible publishers.

## Overview

The API provides two main endpoints under `/callback` to support the WebSubHub protocol:

- **GET `/callback`**  
  Used for verifying subscriber intent via challenge-response.
  
- **POST `/callback`**  
  Used for receiving and processing actual event payloads from publishers.


## WebSubHub Parameters (GET)

- `hub.mode`: Mode of operation (`subscribe`, `unsubscribe`)
- `hub.topic`: Topic URL
- `hub.challenge`: Random string to be echoed
- `hub.reason`: Optional reason for unsubscription
- `message`: Optional human-readable message

## Webhook Payload (POST)

- Supports `application/json`, expects flexible event payloads.


## Spec Version

- OpenAPI: `3.0.3`
- API Version: `v1.0`

---

For more information, refer to the full [OpenAPI spec](./openapi.yaml).
