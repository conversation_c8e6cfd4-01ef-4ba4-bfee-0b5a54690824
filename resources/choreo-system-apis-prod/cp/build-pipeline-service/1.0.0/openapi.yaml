openapi: 3.0.3
info:
  title: Build Pipeline Service
  description: API for the Build Pipeline Service.
  contact: {}
  version: 1.0.0
servers:
  - url: https://app.choreo.dev/93tu/build-pipeline-service/1.0.0
security:
  - default: []
paths:
  /components/{componentId}/default-pipeline:
    get:
      summary: Get the default pipeline for a component
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Base64 encoded default pipeline yaml
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: string
              examples:
                multiple_secrets:
                  summary: Response with multiple secrets
                  value:
                    data: c3RlcHM6CiAgICAgIC0gbmFtZTogRG9ja2VyZmlsZSBTY2FuCiAgICAgICAgdGVtcGxhdGU6IGNob3Jlby9kb2NrZXJmaWxlLXNjYW5AdjEKICAgICAgLSBuYW1lOiBEb2NrZXIgQnVpbGQKICAgICAgICB0ZW1wbGF0ZTogY2hvcmVvL2RvY2tlci1idWlsZEB2MQogICAgICAtIG5hbWU6IFZ1bG5lcmFiaWxpdHkgU2NhbgogICAgICAgIHRlbXBsYXRlOiBjaG9yZW8vdHJpdnktc2NhbkB2MQ==
                empty:
                  summary: Response with no secrets
                  value:
                    data: []
        "404":
          description: Component or deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets:
    get:
      summary: Get all secrets for a deployment track
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: A list of secrets
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Secret'
              examples:
                multiple_secrets:
                  summary: Response with multiple secrets
                  value:
                    data:
                      - id: e813c2fd-c312-4b48-83ee-9c38fd54036a
                        name: DATABASE_PASSWORD
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                      - id: a285e0e9-2fb3-4f65-9062-161b71ac0021
                        name: API_KEY
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                empty:
                  summary: Response with no secrets
                  value:
                    data: []
        "404":
          description: Component or deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      summary: Create or update multiple secrets
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
            examples:
              single_secret:
                summary: Create single secret
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
              multiple_secrets:
                summary: Create multiple secrets
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
                  - key: API_KEY
                    value: sk_live_abcdef123456
      responses:
        "200":
          description: Secrets created or updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      message:
                        type: string
              examples:
                success:
                  summary: Secrets created or updated successfully
                  value:
                    message: Secrets created or updated successfully
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets/{secretId}:
    delete:
      summary: Delete a secret
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Secret deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              examples:
                success:
                  summary: Secret deleted successfully
                  value:
                    message: Secret deleted successfully
        "404":
          description: Secret not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/variables:
    get:
      summary: Get all variables for a deployment track
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Variables retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Variable'
              examples:
                multiple_variables:
                  summary: Response with multiple variables
                  value:
                    data:
                      - id: e813c2fd-c312-4b48-83ee-9c38fd54036a
                        name: DATABASE_PASSWORD
                        value: my-secure-password-123
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                      - id: a285e0e9-2fb3-4f65-9062-161b71ac0021
                        name: API_KEY
                        value: sk_live_abcdef123456
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                empty:
                  summary: Response with no variables
                  value:
                    data: []
        "404":
          description: Component or deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      summary: Create or update multiple variables
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
            examples:
              single_variable:
                summary: Create single variable
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
              multiple_variables:
                summary: Create multiple variables
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
                  - key: API_KEY
                    value: sk_live_abcdef123456
      responses:
        "200":
          description: Variables created or updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              examples:
                success:
                  summary: Variables created or updated successfully
                  value:
                    message: Variables created or updated successfully
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/variables/{variableId}:
    delete:
      summary: Delete a variable
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Variable deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              examples:
                success:
                  summary: Variable deleted successfully
                  value:
                    message: Variable deleted successfully
        "404":
          description: Variable not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    KeyValuePair:
      type: object
      properties:
        key:
          type: string
        value:
          type: string
    Secret:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        componentId:
          type: string
        deploymentTrackId:
          type: string
    Variable:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        value:
          type: string
        componentId:
          type: string
        deploymentTrackId:
          type: string
    ErrorResponse:
      type: object
      properties:
        message:
          type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://devops-portal-api.stage-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://devops-portal-api.stage-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipeline-service/1.0.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
