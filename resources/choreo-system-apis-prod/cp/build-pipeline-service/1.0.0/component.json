{"data": {"component": {"id": "ac3cbe3f-41e8-4d35-af57-9d3b77b88be4", "name": "build-pipeline-service", "handler": "build-pipeline-service", "description": " ", "displayType": "proxy", "displayName": "Build Pipeline Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-03-03T04:32:01.050Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "Build Pipeline Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipeline-service", "proxyId": "67c5307b75ae805502c72267", "id": "67c5307b75ae805502c72267", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67c5307b75ae805502c72267", "createdAt": "1740976251631", "updatedAt": "2025-03-03 04:30:51.631", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "ac3cbe3f-41e8-4d35-af57-9d3b77b88be4", "latest": true, "versionStrategy": ""}]}}}