{"id": "670e3f6055f33d048d031340", "name": "Component Utils", "displayName": "Component Utils", "description": "This is Component Management Utils API", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils", "version": "1.0.0", "provider": "d03b0b16-7db3-4ba1-a60b-e8601d0998cb", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Bronze", "Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": [], "accessControlAllowCredentials": false, "accessControlAllowHeaders": [], "accessControlAllowMethods": [], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1728986976365", "lastUpdatedTime": "2025-07-09 05:20:17.199", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}, "production_endpoints": {"url": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "endpointImplementationType": "ENDPOINT", "subTypeConfiguration": null, "tokenBasedThrottlingConfiguration": null, "scopes": [{"scope": {"id": null, "name": "component_config_view", "displayName": "component_config_view", "description": "View configs", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_create", "displayName": "component_create", "description": "Create component", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_file_view", "displayName": "component_file_view", "description": "View file", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_logs_view", "displayName": "component_logs_view", "description": "View Logs", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_manage", "displayName": "component_manage", "description": "Manage component", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "component_trigger", "displayName": "component_trigger", "description": "Trigger component", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "openid", "displayName": "openid", "description": "Authorize access to component utils", "bindings": [], "usageCount": null}, "shared": false}], "scopePrefix": "urn:choreosystem:componentutils:", "operations": [{"id": "", "target": "/choreo/components/{componentId}/build", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ff520065-b218-45fc-859a-5b86fac72d3a", "backendOperation": {"target": "/choreo/components/{componentId}/build", "verb": "POST", "endpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/choreo/components/{componentId}/code-push", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage", "component_trigger"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ff520065-b218-45fc-859a-5b86fac72d3a", "backendOperation": {"target": "/choreo/components/{componentId}/code-push", "verb": "POST", "endpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/alert/runners/status", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage", "component_trigger"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ff520065-b218-45fc-859a-5b86fac72d3a", "backendOperation": {"target": "/alert/runners/status", "verb": "GET", "endpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/alert/runners/webhook/deliveries", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage", "component_trigger"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ff520065-b218-45fc-859a-5b86fac72d3a", "backendOperation": {"target": "/alert/runners/webhook/deliveries", "verb": "GET", "endpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/actions/runs/status", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ff520065-b218-45fc-859a-5b86fac72d3a", "backendOperation": {"target": "/actions/runs/status", "verb": "POST", "endpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{organizationId}/automation-pipelines/{pipelineId}/runs/{runId}", "verb": "PATCH", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ff520065-b218-45fc-859a-5b86fac72d3a", "backendOperation": {"target": "/orgs/{organizationId}/automation-pipelines/{pipelineId}/runs/{runId}", "verb": "PATCH", "endpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/actions/components/{componentId}/deployment-tracks/{trackId}/workflows/{workflowName}/status", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ff520065-b218-45fc-859a-5b86fac72d3a", "backendOperation": {"target": "/actions/components/{componentId}/deployment-tracks/{trackId}/workflows/{workflowName}/status", "verb": "POST", "endpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/repositories/{gitOrganization}/{repoName}/visibility-level", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["component_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "ff520065-b218-45fc-859a-5b86fac72d3a", "backendOperation": {"target": "/repositories/{gitOrganization}/{repoName}/visibility-level", "verb": "GET", "endpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": "http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1", "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "d03b0b16-7db3-4ba1-a60b-e8601d0998cb", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "b4f9480c-6397-464f-85c4-d6715e8b1fc2", "versionId": "670e3f6055f33d048d031340"}}