openapi: 3.0.3
info:
  title: Component Utils
  description: This is Component Management Utils API
  contact: {}
  version: 1.0.0
servers:
  - url: https://app.choreo.dev/93tu/component-utils/1.0.0
paths:
  /choreo/components/{componentId}/build:
    post:
      tags:
        - Utils
      summary: Notify when the component builds
      operationId: componentBuild
      parameters:
        - name: componentId
          in: path
          description: Component ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: component build successful information
          content:
            application/json:
              schema:
                type: object
                properties:
                  description:
                    type: string
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default:
            - urn:choreosystem:componentutils:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /choreo/components/{componentId}/code-push:
    post:
      tags:
        - Utils
      summary: Notify code-push events
      operationId: componentCodePush
      parameters:
        - name: componentId
          in: path
          description: Component ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: secret
          in: query
          description: Secret Ref
          required: false
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: component code push event information
          content:
            application/json:
              schema:
                type: object
                properties:
                  description:
                    type: string
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default:
            - urn:choreosystem:componentutils:component_manage
            - urn:choreosystem:componentutils:component_trigger
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /alert/runners/status:
    get:
      tags:
        - Utils
      summary: Get GitHub runner status
      operationId: getGitHubRunnerStatus
      responses:
        "200":
          description: repository runner status received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  description:
                    type: string
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default:
            - urn:choreosystem:componentutils:component_manage
            - urn:choreosystem:componentutils:component_trigger
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /alert/runners/webhook/deliveries:
    get:
      tags:
        - Utils
      summary: Get GitHub webhook delivery status
      operationId: getGitHubWebhookDeliveries
      responses:
        "200":
          description: webhook delivery status received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  description:
                    type: string
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default:
            - urn:choreosystem:componentutils:component_manage
            - urn:choreosystem:componentutils:component_trigger
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /actions/runs/status:
    post:
      tags:
        - Utils
      summary: Save action run status
      operationId: actionRunStatus
      responses:
        "200":
          description: Action run status succesfully saved
          content:
            application/json:
              schema:
                type: object
                properties:
                  description:
                    type: string
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /actions/components/{componentId}/deployment-tracks/{trackId}/workflows/{workflowName}/status:
    post:
      tags:
        - Utils
      summary: Update workflow status from Argo workflows
      operationId: workflowStatusUpdate
      parameters:
        - name: componentId
          in: path
          description: Component ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: trackId
          in: path
          description: Deployment Track ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: workflowName
          in: path
          description: Workflow Name
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Success response
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{organizationId}/automation-pipelines/{pipelineId}/runs/{runId}:
    patch:
      summary: Update automation pipeline run status
      operationId: updateAutomationPipelineRunStatus
      parameters:
        - name: organizationId
          in: path
          description: Organization ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: Pipeline ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: runId
          in: path
          description: Run ID
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Pipeline run status update request
        content:
          application/json:
            schema:
              required:
                - status
              type: object
              properties:
                status:
                  type: string
                  description: The status of the pipeline run
                  enum:
                    - SUCCESSFUL
                    - FAILED
                    - RUNNING
                    - STOPPED
        required: true
      responses:
        "200":
          description: Success response
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /repositories/{gitOrganization}/{repoName}/visibility-level:
    get:
      tags:
        - Utils
      summary: Retrieve GitHub repository visibility level
      operationId: getRepositoryVisibilityLevel
      parameters:
        - name: gitOrganization
          in: path
          description: GitHub organization name
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: repoName
          in: path
          description: GitHub repository name
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: repository visibility received successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  description:
                    type: string
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default:
            - urn:choreosystem:componentutils:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    Response:
      type: object
      properties:
        description:
          type: string
  parameters:
    componentId:
      name: componentId
      in: path
      description: Component ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    organizationId:
      name: organizationId
      in: path
      description: Organization ID
      required: true
      style: simple
      explode: false
      schema:
        type: string
    gitOrganization:
      name: gitOrganization
      in: path
      description: GitHub organization name
      required: true
      style: simple
      explode: false
      schema:
        type: string
    repoName:
      name: repoName
      in: path
      description: GitHub repository name
      required: true
      style: simple
      explode: false
      schema:
        type: string
    branch:
      name: branch
      in: path
      description: GitHub branch name
      required: true
      style: simple
      explode: false
      schema:
        type: string
  securitySchemes:
    OAuth2Security:
      type: oauth2
      flows:
        password:
          tokenUrl: https://sts.choreo.dev/oauth2/token
          scopes:
            openid: Authorize access to component utils
            component_logs_view: View Logs
            component_file_view: View file
            component_trigger: Trigger component
            component_manage: Manage component
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            urn:choreosystem:componentutils:component_create: Create component
            urn:choreosystem:componentutils:component_trigger: Trigger component
            urn:choreosystem:componentutils:component_logs_view: View Logs
            urn:choreosystem:componentutils:component_config_view: View configs
            urn:choreosystem:componentutils:component_manage: Manage component
            urn:choreosystem:componentutils:openid: Authorize access to component utils
            urn:choreosystem:componentutils:component_file_view: View file
          x-scopes-bindings:
            urn:choreosystem:componentutils:component_file_view: ""
            urn:choreosystem:componentutils:component_create: ""
            urn:choreosystem:componentutils:component_logs_view: ""
            urn:choreosystem:componentutils:component_trigger: ""
            urn:choreosystem:componentutils:component_config_view: ""
            urn:choreosystem:componentutils:component_manage: ""
            urn:choreosystem:componentutils:openid: ""
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins: []
  accessControlAllowCredentials: false
  accessControlAllowHeaders: []
  accessControlAllowMethods: []
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
