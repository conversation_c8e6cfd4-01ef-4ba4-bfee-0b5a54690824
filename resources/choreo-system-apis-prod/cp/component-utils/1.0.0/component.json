{"data": {"component": {"id": "b4f9480c-6397-464f-85c4-d6715e8b1fc2", "name": "component-utils", "handler": "component-utils", "description": " ", "displayType": "proxy", "displayName": "Component Utils", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-10-15T10:16:05.385Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "Component Utils", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/component-utils", "proxyId": "670e3f6055f33d048d031340", "id": "670e3f6055f33d048d031340", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "670e3f6055f33d048d031340", "createdAt": "1728986976365", "updatedAt": "2024-10-15 10:09:36.365", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "b4f9480c-6397-464f-85c4-d6715e8b1fc2", "latest": true, "versionStrategy": ""}]}}}