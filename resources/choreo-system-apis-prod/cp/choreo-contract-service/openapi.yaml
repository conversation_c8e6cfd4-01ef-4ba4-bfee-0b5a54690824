basePath: /api/v1
definitions:
  api.ListResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/github.GithubOpenAPIEntry'
        type: array
      nextPage:
        type: string
    type: object
  github.GithubOpenAPIEntry:
    properties:
      downloadURL:
        type: string
      htmlurl:
        type: string
      name:
        type: string
      path:
        type: string
      sha:
        type: string
      size:
        type: integer
      type:
        type: string
    type: object
host: localhost:8080
info:
  contact: {}
  description: A service to fetch and list OpenAPI specifications from ballerina-platform/openapi-connectors
    Github Repository.
  title: Contract Service API
  version: "1.0"
paths:
  /healthz:
    get:
      description: Responds with a simple status message to indicate that the service
        is running
      produces:
      - text/plain
      responses:
        "200":
          description: OK
          schema:
            type: string
      summary: Health Check
      x-auth-type: None
      tags:
      - health
  /openapi:
    get:
      consumes:
      - application/json
      description: Get the OpenAPI specification for a specific service
      parameters:
      - description: Service name
        in: query
        name: name
        required: true
        type: string
      produces:
      - application/yaml
      responses:
        "200":
          description: OpenAPI specification in YAML format
          schema:
            type: string
        "400":
          description: Service name is required
          schema:
            type: string
        "404":
          description: OpenAPI specification not found
          schema:
            type: string
        "500":
          description: Failed to fetch OpenAPI specification
          schema:
            type: string
      summary: Get OpenAPI specification
      x-auth-type: None
      tags:
      - services
  /services:
    get:
      consumes:
      - application/json
      description: Get a paginated list of services with their OpenAPI specifications
      parameters:
      - description: Pagination cursor
        in: query
        name: cursor
        type: string
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.ListResponse'
        "400":
          description: Invalid limit parameter
          schema:
            type: string
        "500":
          description: Failed to list services
          schema:
            type: string
      summary: List available services
      x-auth-type: None
      tags:
      - services
swagger: "2.0"