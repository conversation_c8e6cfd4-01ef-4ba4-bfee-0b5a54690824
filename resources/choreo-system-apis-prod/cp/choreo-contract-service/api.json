{"name": "Choreo Contract Service", "displayName": "Choreo Contract Service", "description": "API for the Choreo Contract Service.", "context": "/contract-service", "version": "1.0.0", "transport": ["https"], "policies": ["Unlimited"], "visibility": "PUBLIC", "enableBackendJWT": true, "securityScheme": [], "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://contract-service.prod-choreo-system.svc.cluster.local:8080/api/v1"}, "production_endpoints": {"url": "http://contract-service.prod-choreo-system.svc.cluster.local:8080/api/v1"}}, "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}}