{"id": "67d41a3a002162174ac5729d", "name": "Choreo Contract Service", "displayName": "Choreo Contract Service", "description": "API for the Choreo Contract Service.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/contract-service", "version": "1.0.0", "provider": "a579828c-19e4-4406-92f4-15df9033c2fc", "lifeCycleStatus": "CREATED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": [], "maxTps": null, "visibility": "PUBLIC", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": false, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1741953594569", "lastUpdatedTime": "2025-03-14 11:59:54.569", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://contract-service.prod-choreo-system.svc.cluster.local:8080/api/v1"}, "production_endpoints": {"url": "http://contract-service.prod-choreo-system.svc.cluster.local:8080/api/v1"}}, "endpointImplementationType": "ENDPOINT", "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/healthz", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "0295c35f-19a6-47e8-8cbd-47c839608c35", "backendOperation": {"target": "/healthz", "verb": "GET"}}}, {"id": "", "target": "/openapi", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "0295c35f-19a6-47e8-8cbd-47c839608c35", "backendOperation": {"target": "/openapi", "verb": "GET"}}}, {"id": "", "target": "/services", "verb": "GET", "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "0295c35f-19a6-47e8-8cbd-47c839608c35", "backendOperation": {"target": "/services", "verb": "GET"}}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "a579828c-19e4-4406-92f4-15df9033c2fc", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": null, "componentId": null, "versionId": "67d41a3a002162174ac5729d"}}