{"data": {"component": {"id": "6dcff29e-7002-44f4-9e3f-5b4a2ab9960f", "name": "choreo-contract-service", "handler": "choreo-contract-service", "description": " ", "displayType": "proxy", "displayName": "Choreo Contract Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-03-14T12:00:54.383Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "Choreo Contract Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/contract-service", "proxyId": "67d41a3a002162174ac5729d", "id": "67d41a3a002162174ac5729d", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67d41a3a002162174ac5729d", "createdAt": "1741953594569", "updatedAt": "2025-03-14 11:59:54.569", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "6dcff29e-7002-44f4-9e3f-5b4a2ab9960f", "latest": true, "versionStrategy": ""}]}}}