swagger: "2.0"
info:
  description: A service to fetch and list OpenAPI specifications from ballerina-platform/openapi-connectors Github Repository.
  version: 1.0.0
  title: Choreo Contract Service
  contact: {}
host: localhost:8080
basePath: /93tu/contract-service/1.0.0
security:
  - default: []
paths:
  /healthz:
    get:
      tags:
        - health
      summary: Health Check
      description: Responds with a simple status message to indicate that the service is running
      produces:
        - text/plain
      parameters: []
      responses:
        "200":
          description: OK
          schema:
            type: string
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /openapi:
    get:
      tags:
        - services
      summary: Get OpenAPI specification
      description: Get the OpenAPI specification for a specific service
      consumes:
        - application/json
      produces:
        - application/yaml
      parameters:
        - name: name
          in: query
          description: Service name
          required: true
          type: string
      responses:
        "200":
          description: OpenAPI specification in YAML format
          schema:
            type: string
        "400":
          description: Service name is required
          schema:
            type: string
        "404":
          description: OpenAPI specification not found
          schema:
            type: string
        "500":
          description: Failed to fetch OpenAPI specification
          schema:
            type: string
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /services:
    get:
      tags:
        - services
      summary: List available services
      description: Get a paginated list of services with their OpenAPI specifications
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: cursor
          in: query
          description: Pagination cursor
          required: false
          type: string
        - name: limit
          in: query
          description: Number of items per page
          required: false
          type: integer
          default: 10
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.ListResponse'
        "400":
          description: Invalid limit parameter
          schema:
            type: string
        "500":
          description: Failed to list services
          schema:
            type: string
      security:
        - default: []
      x-auth-type: None
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
securityDefinitions:
  default:
    type: oauth2
    authorizationUrl: https://test.com
    flow: implicit
definitions:
  api.ListResponse:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: '#/definitions/github.GithubOpenAPIEntry'
      nextPage:
        type: string
  github.GithubOpenAPIEntry:
    type: object
    properties:
      downloadURL:
        type: string
      htmlurl:
        type: string
      name:
        type: string
      path:
        type: string
      sha:
        type: string
      size:
        type: integer
      type:
        type: string
x-wso2-disable-security: true
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://contract-service.prod-choreo-system.svc.cluster.local:8080/api/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://contract-service.prod-choreo-system.svc.cluster.local:8080/api/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/contract-service/1.0.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
