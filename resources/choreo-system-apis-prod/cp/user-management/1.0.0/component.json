{"data": {"component": {"id": "a77eac9f-fb79-45e9-987d-ab1555278500", "name": "user-management", "handler": "eftews", "description": "Owned by Org-Mgt team", "displayType": "proxy", "displayName": "User Management", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "1.0.0", "labels": [], "createdAt": "2023-08-15T03:30:23.300Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "User Management", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-mgt", "proxyId": "64daf146d5aa8b4bada40cde", "id": "64daf146d5aa8b4bada40cde", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "64daf146d5aa8b4bada40cde", "createdAt": "1692070214459", "updatedAt": "2024-07-22 05:25:39.561", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "a77eac9f-fb79-45e9-987d-ab1555278500", "latest": true, "versionStrategy": ""}]}}}