{"id": "64daf146d5aa8b4bada40cde", "name": "User Management", "displayName": "User Management", "description": "Owned by Org-Mgt team", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-mgt", "version": "1.0.0", "provider": "d63bd494-74d1-4ee6-a98d-c303dcfb6623", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": "<PERSON><PERSON><PERSON>", "businessOwnerEmail": "<EMAIL>", "technicalOwner": "<PERSON><PERSON><PERSON>", "technicalOwnerEmail": "<EMAIL>"}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1692070214459", "lastUpdatedTime": "2025-07-22 10:00:02.576", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://api-server.prod-choreo-system.svc.cluster.local:80"}, "production_endpoints": {"url": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "endpointImplementationType": "ENDPOINT", "subTypeConfiguration": {"subType": "DEFAULT", "egress": false, "configuration": null}, "tokenBasedThrottlingConfiguration": null, "scopes": [{"scope": {"id": null, "name": "invitation_delete", "displayName": "invitation_delete", "description": "Delete invitation", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "invitation_manage", "displayName": "invitation_manage", "description": "Manage invitation", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "invitation_send", "displayName": "invitation_send", "description": "Send invitation", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "invitation_view", "displayName": "invitation_view", "description": "View invitation", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "openid", "displayName": "openid", "description": "Authorize access to user details", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "permission_view", "displayName": "permission_view", "description": "View permissions", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_create", "displayName": "role_create", "description": "Create roles", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_delete", "displayName": "role_delete", "description": "Delete roles", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_manage", "displayName": "role_manage", "description": "Manage roles", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_mapping_create", "displayName": "role_mapping_create", "description": "Create group role mapping", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_mapping_delete", "displayName": "role_mapping_delete", "description": "Delete group role mapping", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_mapping_manage", "displayName": "role_mapping_manage", "description": "Manage group role mapping", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_mapping_update", "displayName": "role_mapping_update", "description": "Update group role mapping", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_mapping_view", "displayName": "role_mapping_view", "description": "View group role mapping", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_update", "displayName": "role_update", "description": "Update roles", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "role_view", "displayName": "role_view", "description": "View roles", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "user_delete", "displayName": "user_delete", "description": "Create user", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "user_manage", "displayName": "user_manage", "description": "Manage users", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "user_update", "displayName": "user_update", "description": "Update users", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "user_view", "displayName": "user_view", "description": "View user", "bindings": [], "usageCount": null}, "shared": false}], "scopePrefix": "urn:choreosystem:usermanagement:", "operations": [{"id": "", "target": "/orgs/{orgHandle}/users/{uuid}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["user_view", "user_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/users/{uuid}", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/users/{uuid}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["user_delete", "user_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/users/{uuid}", "verb": "DELETE", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/users", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["user_view", "user_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/users", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/users", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["user_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/users", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/users/{uuid}/groups", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["user_view", "user_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/users/{uuid}/groups", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/users/{uuid}/groups", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["user_update", "user_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/users/{uuid}/groups", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/users/{uuid}/roles", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["user_update", "user_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/users/{uuid}/roles", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/roles/permissions", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["permission_view"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/roles/permissions", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgUUID}/users-list", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["user_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgUUID}/users-list", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{org<PERSON>and<PERSON>}/roles", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_view", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{org<PERSON>and<PERSON>}/roles", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{org<PERSON>and<PERSON>}/roles", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_create", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{org<PERSON>and<PERSON>}/roles", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/roles/{roleHandle}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_view", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/roles/{roleHandle}", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/roles/{roleHandle}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_update", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/roles/{roleHandle}", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/roles/{roleHandle}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_delete", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/roles/{roleHandle}", "verb": "DELETE", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/roles/{roleHandle}/users", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_view", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/roles/{roleHandle}/users", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/roles/{roleHandle}/users", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_update", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/roles/{roleHandle}/users", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/roles/{roleHandle}/assign-groups", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_create", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/roles/{roleHandle}/assign-groups", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/roles/{roleHandle}/remove-groups", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_delete", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/roles/{roleHandle}/remove-groups", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/roles/{roleHandle}/groups", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_view", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/roles/{roleHandle}/groups", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_view", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_view", "role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}", "verb": "DELETE", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}/members", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}/members", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}/members/{uuid}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_update"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}/members/{uuid}", "verb": "DELETE", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}/roles", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_view", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}/roles", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}/assign-roles", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_create", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}/assign-roles", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}/remove-roles", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_delete", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}/remove-roles", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/groups/{groupHandle}/enterprise-groups", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_view", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/groups/{groupHandle}/enterprise-groups", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/enterprise-groups", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_view", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/enterprise-groups", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/enterprise-groups", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_create", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/enterprise-groups", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/enterprise-groups/{groupName}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_view", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/enterprise-groups/{groupName}", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/enterprise-groups/{groupName}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_update", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/enterprise-groups/{groupName}", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/enterprise-groups/{groupName}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_delete", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/enterprise-groups/{groupName}", "verb": "DELETE", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/group-role-mappings", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_view", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/group-role-mappings", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/group-role-mappings", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_create", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/group-role-mappings", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/group-role-mappings/{groupName}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/group-role-mappings/{groupName}", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/group-role-mappings/{groupName}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_update", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/group-role-mappings/{groupName}", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/group-role-mappings/{groupName}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["role_mapping_delete", "role_mapping_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/group-role-mappings/{groupName}", "verb": "DELETE", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/invitations", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["invitation_view", "invitation_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/invitations", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/invitations", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["invitation_send", "invitation_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/invitations", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/invitations", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["invitation_delete", "invitation_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/invitations", "verb": "DELETE", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{org<PERSON><PERSON><PERSON>}/invitations/{confirmation<PERSON>ey}", "verb": "GET", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{org<PERSON><PERSON><PERSON>}/invitations/{confirmation<PERSON>ey}", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{org<PERSON><PERSON><PERSON>}/invitations/{confirmation<PERSON>ey}", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{org<PERSON><PERSON><PERSON>}/invitations/{confirmation<PERSON>ey}", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/v2/invitations", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["invitation_view", "invitation_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/v2/invitations", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/v2/invitations", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["invitation_send", "invitation_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/v2/invitations", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{orgHandle}/v2/invitations", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["invitation_delete", "invitation_manage"], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{orgHandle}/v2/invitations", "verb": "DELETE", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{org<PERSON>andle}/v2/invitations/{confirmation<PERSON>ey}", "verb": "GET", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{org<PERSON>andle}/v2/invitations/{confirmation<PERSON>ey}", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{org<PERSON>andle}/v2/invitations/{confirmation<PERSON>ey}", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{org<PERSON>andle}/v2/invitations/{confirmation<PERSON>ey}", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{org}/self-subscribe", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{org}/self-subscribe", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/orgs/{org}/self-unsubscribe", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/orgs/{org}/self-unsubscribe", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/validate/orgname", "verb": "GET", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/validate/orgname", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/validate/user", "verb": "GET", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/validate/user", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/add-enterprise-user", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/add-enterprise-user", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/register-user", "verb": "POST", "description": null, "authType": "None", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/register-user", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/user/salesforce-lead", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/user/salesforce-lead", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/consent/tos", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/consent/tos", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/consent/tos", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/consent/tos", "verb": "PUT", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/consent/user", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/consent/user", "verb": "GET", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}, {"id": "", "target": "/consent/user", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "cc550f8f-3f8f-4f5f-b4a5-8d14067dd703", "backendOperation": {"target": "/consent/user", "verb": "POST", "endpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": "http://api-server.prod-choreo-system.svc.cluster.local:80", "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "d63bd494-74d1-4ee6-a98d-c303dcfb6623", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "a77eac9f-fb79-45e9-987d-ab1555278500", "versionId": "64daf146d5aa8b4bada40cde"}}