openapi: 3.0.3
info:
  title: User Management
  description: This is the Choreo Runtime User Management API
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
  version: 1.0.0
servers:
  - url: https://app.choreo.dev/93tu/user-mgt/1.0.0
security:
  - default: []
paths:
  /orgs/{orgHandle}/users/{uuid}:
    get:
      tags:
        - UserManagement/User
      summary: Get user information
      operationId: getUserInfo
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/uuid'
        - name: include
          in: query
          description: Specify whether to include roles
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: User information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        "404":
          description: User not found
      security:
        - OAuth2Security:
            - user_view
            - user_manage
        - default:
            - urn:choreosystem:usermanagement:user_view
            - urn:choreosystem:usermanagement:user_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/User
      summary: Delete a user from an organization
      operationId: deleteUserFromOrganization
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/uuid'
        - $ref: '#/components/parameters/email'
      responses:
        "200":
          description: The user was successfully deleted from the organization
        "404":
          description: The user or organization was not found
      security:
        - OAuth2Security:
            - user_delete
            - user_manage
        - default:
            - urn:choreosystem:usermanagement:user_delete
            - urn:choreosystem:usermanagement:user_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/users:
    get:
      tags:
        - UserManagement/User
      summary: Get Users for Organization
      description: |
        Retrieves a list of users for a given organization. An optional role parameter can be provided to filter the list by a specific role.
      operationId: getUsersForOrganizationByRole
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - name: include
          in: query
          description: Specify whether to include roles
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: A list of users for the given organization and role (if provided).
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
        "400":
          description: Invalid input parameters.
        "404":
          description: The organization was not found.
      security:
        - OAuth2Security:
            - user_view
            - user_manage
        - default:
            - urn:choreosystem:usermanagement:user_view
            - urn:choreosystem:usermanagement:user_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/User
      summary: Add Users to Organization
      operationId: addUsersToOrganization
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - name: include
          in: query
          description: Specify whether to include roles
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: group
          in: query
          description: Group name
          required: true
          style: form
          explode: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddOrganizationUsersRequest'
        required: true
      responses:
        "200":
          description: Users added to the organization successfully.
        "400":
          description: Invalid input parameters.
        "404":
          description: The organization was not found.
      security:
        - OAuth2Security:
            - user_manage
        - default:
            - urn:choreosystem:usermanagement:user_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/users/{uuid}/groups:
    get:
      tags:
        - UserManagement/User
      summary: Get groups of a user
      operationId: getGroupsOfUser
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/uuid'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserGroups'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - user_view
            - user_manage
        - default:
            - urn:choreosystem:usermanagement:user_view
            - urn:choreosystem:usermanagement:user_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/User
      summary: Update groups of a user
      operationId: updateGroupsOfUser
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/uuid'
      requestBody:
        description: The list of groups to add to the user
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserGroupsPayload'
        required: true
      responses:
        "200":
          description: Groups of the user successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserGroups'
        "400":
          description: Invalid input data
        "404":
          description: User not found
      security:
        - OAuth2Security:
            - user_update
            - user_manage
        - default:
            - urn:choreosystem:usermanagement:user_update
            - urn:choreosystem:usermanagement:user_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/users/{uuid}/roles:
    put:
      tags:
        - UserManagement/User
      summary: Update roles of a member
      operationId: updateRolesOfMember
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/uuid'
      requestBody:
        description: The list of roles to add to the user
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdditionalRoleHandles'
        required: true
      responses:
        "200":
          description: Roles updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        "400":
          description: Invalid request body
        "404":
          description: Member not found
      security:
        - OAuth2Security:
            - user_update
            - user_manage
        - default:
            - urn:choreosystem:usermanagement:user_update
            - urn:choreosystem:usermanagement:user_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /roles/permissions:
    get:
      tags:
        - UserManagement/User
      summary: Get list of permissions
      operationId: getPermissionsList
      responses:
        "200":
          description: Permission list retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Permissions'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgUUID}/users-list:
    post:
      tags:
        - UserManagement/User
      summary: Get Users for Organization by List
      operationId: getUsersForOrganizationByList
      parameters:
        - name: orgUUID
          in: path
          description: Organization UUID
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: include
          in: query
          description: Specify whether to include roles
          required: false
          style: form
          explode: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FindUsersByOrganizationByListRequest'
        required: true
      responses:
        "200":
          description: Users retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FindUsersByOrganizationByListResponse'
        "400":
          description: Invalid input parameters.
        "404":
          description: The organization was not found.
      security:
        - OAuth2Security:
            - user_manage
        - default:
            - urn:choreosystem:usermanagement:user_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles:
    get:
      tags:
        - UserManagement/Role
      summary: Get a list of roles of an organization
      operationId: listRolesOfOrganization
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - name: include
          in: query
          description: Specify whether to include members
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: List of roles of an organization
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Roles'
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/Role
      summary: Create a new role for an organization
      operationId: createRole
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: The roles to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolePayload'
        required: true
      responses:
        "200":
          description: Role created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
      security:
        - OAuth2Security:
            - role_create
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_create
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/{roleHandle}:
    get:
      tags:
        - UserManagement/Role
      summary: Get information about a role of an organization
      operationId: getRoleInfo
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
        - name: include
          in: query
          description: Specify whether to include permission
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Role information successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/Role
      summary: Update role information
      operationId: updateRoleInfo
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      requestBody:
        description: The updated role information
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolePermission'
        required: true
      responses:
        "200":
          description: Role information updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
        "400":
          description: Invalid input data
        "404":
          description: Role not found
      security:
        - OAuth2Security:
            - role_update
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_update
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/Role
      summary: Delete a role
      operationId: deleteRole
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      responses:
        "200":
          description: Role successfully deleted
        "404":
          description: Role not found
      security:
        - OAuth2Security:
            - role_delete
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_delete
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/{roleHandle}/users:
    get:
      tags:
        - UserManagement/Role
      summary: List users of a role
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListUsersOfRoleResponse'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/Role
      summary: Update members of a role
      operationId: updateMembersOfRole
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      requestBody:
        description: The list of users to add to the role
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdditionalUserIds'
        required: true
      responses:
        "200":
          description: Members of the role successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
        "400":
          description: Invalid input data
        "404":
          description: Role not found
      security:
        - OAuth2Security:
            - role_update
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_update
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/{roleHandle}/assign-groups:
    post:
      tags:
        - UserManagement/Role
      summary: Assigns groups to role
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      requestBody:
        description: Role to groups assignment
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleGroupsAssignmentPayload'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroupsAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_create
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_create
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/{roleHandle}/remove-groups:
    post:
      tags:
        - UserManagement/Role
      summary: Remove groups from role
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      requestBody:
        description: Groups to be removed
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupsRemovePayload'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroupsAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_delete
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_delete
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/{roleHandle}/groups:
    get:
      tags:
        - UserManagement/Role
      summary: List groups of a role
      operationId: listGroupsOfRole
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroupsAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups:
    get:
      tags:
        - UserManagement/Group
      summary: List groups of an organization
      operationId: listGroupsOfOrganization
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      responses:
        "200":
          description: List of groups of an organization
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupList'
        "404":
          description: The organization was not found
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/Group
      summary: Create a new group for an organization
      operationId: createGroup
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: The group to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupPayload'
        required: true
      responses:
        "200":
          description: Group created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        "400":
          description: Invalid input data
        "404":
          description: The organization was not found
      security:
        - OAuth2Security:
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/{groupHandle}:
    get:
      tags:
        - UserManagement/Group
      summary: Get information about a group of an organization
      operationId: getGroupInfo
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      responses:
        "200":
          description: Group information successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/Group
      summary: Update group information
      operationId: updateGroupInfo
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: The updated group information
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupPayload'
        required: true
      responses:
        "200":
          description: Group information updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        "400":
          description: Invalid input data
        "404":
          description: Group not found
      security:
        - OAuth2Security:
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/Group
      summary: Delete a group
      operationId: deleteGroup
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      responses:
        "200":
          description: Group successfully deleted
        "404":
          description: Group not found
      security:
        - OAuth2Security:
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/{groupHandle}/members:
    post:
      tags:
        - UserManagement/Group
      summary: Add members to group
      operationId: addMembersToGroup
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: The list of users to add to the group
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupMembersPayload'
        required: true
      responses:
        "200":
          description: Members of the group successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupMembers'
        "400":
          description: Invalid input data
        "404":
          description: Group not found
      security:
        - OAuth2Security:
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/{groupHandle}/members/{uuid}:
    delete:
      tags:
        - UserManagement/Group
      summary: Delete user from group
      operationId: deleteUserFromGroup
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
        - $ref: '#/components/parameters/uuid'
      responses:
        "200":
          description: Group successfully deleted from user
        "404":
          description: Group not found
      security:
        - OAuth2Security:
            - role_mapping_update
        - default:
            - urn:choreosystem:usermanagement:role_mapping_update
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/{groupHandle}/roles:
    get:
      tags:
        - UserManagement/Group
      summary: List roles of a group
      operationId: listRolesOfGroup
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRolesAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/{groupHandle}/assign-roles:
    post:
      tags:
        - UserManagement/Group
      summary: Assigns roles to group
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: Group to roles assignment
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRolesAssignmentPayload'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRolesAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_create
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_create
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/{groupHandle}/remove-roles:
    post:
      tags:
        - UserManagement/Group
      summary: Remove roles from group
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: Roles to be removed
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolesRemovePayload'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRolesAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_delete
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_delete
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/{groupHandle}/enterprise-groups:
    put:
      tags:
        - UserManagement/Group
      summary: Update enterprise groups of group
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: Enterprise groups to be mapped to group
        content:
          application/json:
            schema:
              type: object
              properties:
                enterpriseGroupNames:
                  type: array
                  items:
                    type: string
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  orgHandle:
                    type: string
                    description: The Organization handle
                  choreoGroup:
                    type: object
                    properties:
                      handle:
                        type: string
                        description: The handle of the group
                      uuid:
                        type: string
                        description: The UUID of the group
                      displayName:
                        type: string
                        description: The description of the group
                  enterpriseGroups:
                    type: array
                    items:
                      type: string
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/enterprise-groups:
    get:
      tags:
        - UserManagement/EnterpriseGroup
      summary: List enterprise groups of an organization
      operationId: listEnterpriseGroupsOfOrganization
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      responses:
        "200":
          description: List of enterprise groups of an organization
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ListEnterpriseGroups'
        "404":
          description: The organization was not found
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/EnterpriseGroup
      summary: Create a new enterprise group for an organization
      operationId: createEnterpriseGroup
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: The enterprise group to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnterpriseGroupPayload'
        required: true
      responses:
        "200":
          description: Enterprise group created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnterpriseGroup'
        "400":
          description: Invalid input data
        "404":
          description: The organization was not found
      security:
        - OAuth2Security:
            - role_mapping_create
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_create
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/enterprise-groups/{groupName}:
    get:
      tags:
        - UserManagement/EnterpriseGroup
      summary: Get information about an enterprise group of an organization
      operationId: getEnterpriseGroupInfo
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupName'
      responses:
        "200":
          description: Enterprise group information successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnterpriseGroup'
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/EnterpriseGroup
      summary: Update enterprise group information
      operationId: updateEnterpriseGroupInfo
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupName'
      requestBody:
        description: The updated enterprise group information
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnterpriseGroupPayload'
        required: true
      responses:
        "200":
          description: Enterprise group information updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnterpriseGroup'
        "400":
          description: Invalid input data
        "404":
          description: Enterprise group not found
      security:
        - OAuth2Security:
            - role_mapping_update
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_update
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/EnterpriseGroup
      summary: Delete an enterprise group
      operationId: deleteEnterpriseGroup
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupName'
      responses:
        "200":
          description: Enterprise group successfully deleted
        "404":
          description: Enterprise group not found
      security:
        - OAuth2Security:
            - role_mapping_delete
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_delete
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/group-role-mappings:
    get:
      tags:
        - UserManagement/GroupRoleMapping
      summary: List Group Role Mappings
      description: Returns a list of all group-to-role mappings in the specified organization
      operationId: listGroupRoleMappings
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      responses:
        "200":
          description: List of group-to-role mappings successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRoleMappings'
        "400":
          description: Invalid request parameter
        "404":
          description: Group role mapping not found
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/GroupRoleMapping
      summary: Create Group Role Mapping
      description: Creates a new group-to-role mapping in the specified organization
      operationId: createGroupRoleMapping
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: The group-to-role mapping to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRoleMappingPayload'
        required: true
      responses:
        "200":
          description: Group role mapping successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRoleMapping'
        "400":
          description: Invalid request parameter(s) or body
        "404":
          description: The specified organization does not exist
      security:
        - OAuth2Security:
            - role_mapping_create
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_create
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/group-role-mappings/{groupName}:
    get:
      tags:
        - UserManagement/GroupRoleMapping
      summary: Get Group Role Mapping Info
      operationId: GetGroupRoleMappingInfo
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupName'
      responses:
        "200":
          description: Group role mapping info successfully returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRoleMapping'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/GroupRoleMapping
      summary: Update Group Role Mapping
      description: Updates the group-to-role mapping with the specified group name in the specified organization
      operationId: updateGroupRoleMapping
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupName'
      requestBody:
        description: The updated group-to-role mapping
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRoleMappingPayload'
        required: true
      responses:
        "200":
          description: Group-to-role mapping successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRoleMapping'
        "400":
          description: Invalid request parameter(s) or body
        "404":
          description: The specified organization or group-to-role mapping does not exist
      security:
        - OAuth2Security:
            - role_mapping_update
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_update
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/GroupRoleMapping
      summary: Delete Group Role Mapping
      description: Deletes the group role mapping with the specified group name in the given organization
      operationId: deleteGroupRoleMapping
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupName'
      responses:
        "200":
          description: Group role mapping successfully deleted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRoleMapping'
        "400":
          description: Invalid request parameters
        "404":
          description: The specified organization or group role mapping does not exist
      security:
        - OAuth2Security:
            - role_mapping_delete
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_delete
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/v2:
    get:
      tags:
        - UserManagement/Role
      summary: Get a list of roles of an organization
      operationId: listRolesOfOrganizationV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - name: include
          in: query
          description: Specify whether to include members
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: List of roles of an organization
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Roles'
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/Role
      summary: Create a new role for an organization
      operationId: createRoleV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: The roles to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolePayload'
        required: true
      responses:
        "200":
          description: Role created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
      security:
        - OAuth2Security:
            - role_create
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_create
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/v2/{roleHandle}:
    get:
      tags:
        - UserManagement/Role
      summary: Get information about a role of an organization
      operationId: getRoleInfoV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
        - name: include
          in: query
          description: Specify whether to include permission
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Role information successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/Role
      summary: Update role information
      operationId: updateRoleInfoV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      requestBody:
        description: The updated role information
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolePermission'
        required: true
      responses:
        "200":
          description: Role information updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
        "400":
          description: Invalid input data
        "404":
          description: Role not found
      security:
        - OAuth2Security:
            - role_update
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_update
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/Role
      summary: Delete a role
      operationId: deleteRoleV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      responses:
        "200":
          description: Role successfully deleted
        "404":
          description: Role not found
      security:
        - OAuth2Security:
            - role_delete
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_delete
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/v2/{roleHandle}/assign-groups:
    post:
      tags:
        - UserManagement/Role
      summary: Assigns groups to role
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      requestBody:
        description: Role to groups assignment
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleGroupsAssignmentPayload'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroupsAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_create
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_create
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/v2/{roleHandle}/remove-groups:
    post:
      tags:
        - UserManagement/Role
      summary: Remove groups from role
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      requestBody:
        description: Groups to be removed
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupsRemovePayload'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroupsAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_delete
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_delete
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/roles/v2/{roleHandle}/groups:
    get:
      tags:
        - UserManagement/Role
      summary: List groups of a role
      operationId: listGroupsOfRoleV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/roleHandle'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleGroupsAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/v2:
    get:
      tags:
        - UserManagement/Group
      summary: List groups of an organization
      operationId: listGroupsOfOrganizationV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      responses:
        "200":
          description: List of groups of an organization
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupList'
        "404":
          description: The organization was not found
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/Group
      summary: Create a new group for an organization
      operationId: createGroupV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: The group to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupPayload'
        required: true
      responses:
        "200":
          description: Group created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        "400":
          description: Invalid input data
        "404":
          description: The organization was not found
      security:
        - OAuth2Security:
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/v2/{groupHandle}:
    get:
      tags:
        - UserManagement/Group
      summary: Get information about a group of an organization
      operationId: getGroupInfoV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      responses:
        "200":
          description: Group information successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
      security:
        - OAuth2Security:
            - role_view
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_view
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/Group
      summary: Update group information
      operationId: updateGroupInfoV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: The updated group information
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupPayload'
        required: true
      responses:
        "200":
          description: Group information updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Group'
        "400":
          description: Invalid input data
        "404":
          description: Group not found
      security:
        - OAuth2Security:
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/Group
      summary: Delete a group
      operationId: deleteGroupV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      responses:
        "200":
          description: Group successfully deleted
        "404":
          description: Group not found
      security:
        - OAuth2Security:
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/v2/{groupHandle}/members:
    post:
      tags:
        - UserManagement/Group
      summary: Add members to group
      operationId: addMembersToGroupV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: The list of users to add to the group
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupMembersPayload'
        required: true
      responses:
        "200":
          description: Members of the group successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupMembers'
        "400":
          description: Invalid input data
        "404":
          description: Group not found
      security:
        - OAuth2Security:
            - role_manage
        - default:
            - urn:choreosystem:usermanagement:role_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/v2/{groupHandle}/members/{uuid}:
    delete:
      tags:
        - UserManagement/Group
      summary: Delete user from group
      operationId: deleteUserFromGroupV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
        - $ref: '#/components/parameters/uuid'
      responses:
        "200":
          description: Group successfully deleted from user
        "404":
          description: Group not found
      security:
        - OAuth2Security:
            - role_mapping_update
        - default:
            - urn:choreosystem:usermanagement:role_mapping_update
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/v2/{groupHandle}/roles:
    get:
      tags:
        - UserManagement/Group
      summary: List roles of a group
      operationId: listRolesOfGroupV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRolesAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/v2/{groupHandle}/assign-roles:
    post:
      tags:
        - UserManagement/Group
      summary: Assigns roles to group
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: Group to roles assignment
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRolesAssignmentPayload'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRolesAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_create
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_create
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/v2/{groupHandle}/remove-roles:
    post:
      tags:
        - UserManagement/Group
      summary: Remove roles from group
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: Roles to be removed
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolesRemovePayload'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRolesAssignment'
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_delete
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_delete
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/groups/v2/{groupHandle}/enterprise-groups:
    put:
      tags:
        - UserManagement/Group
      summary: Update enterprise groups of group
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/groupHandle'
      requestBody:
        description: Enterprise groups to be mapped to group
        content:
          application/json:
            schema:
              type: object
              properties:
                enterpriseGroupNames:
                  type: array
                  items:
                    type: string
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  orgHandle:
                    type: string
                    description: The Organization handle
                  choreoGroup:
                    type: object
                    properties:
                      handle:
                        type: string
                        description: The handle of the group
                      uuid:
                        type: string
                        description: The UUID of the group
                      displayName:
                        type: string
                        description: The description of the group
                  enterpriseGroups:
                    type: array
                    items:
                      type: string
        "400":
          description: Invalid request
        "404":
          description: Resource not found
      security:
        - OAuth2Security:
            - role_mapping_view
            - role_mapping_manage
        - default:
            - urn:choreosystem:usermanagement:role_mapping_view
            - urn:choreosystem:usermanagement:role_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/invitations:
    get:
      tags:
        - UserManagement/Invitation
      summary: List member invitations
      operationId: listMemberInvitationsV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Invitation'
      security:
        - OAuth2Security:
            - invitation_view
            - invitation_manage
        - default:
            - urn:choreosystem:usermanagement:invitation_view
            - urn:choreosystem:usermanagement:invitation_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/Invitation
      summary: Send member invitation
      operationId: sendMemberInvitationV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: The invitation payload to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserInvite'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessfulList'
      security:
        - OAuth2Security:
            - invitation_send
            - invitation_manage
        - default:
            - urn:choreosystem:usermanagement:invitation_send
            - urn:choreosystem:usermanagement:invitation_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/Invitation
      summary: Delete member invitation
      operationId: deleteMemberInvitationV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/email'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
      security:
        - OAuth2Security:
            - invitation_delete
            - invitation_manage
        - default:
            - urn:choreosystem:usermanagement:invitation_delete
            - urn:choreosystem:usermanagement:invitation_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/invitations/{confirmationKey}:
    get:
      tags:
        - UserManagement/Invitation
      summary: Get member invitation
      operationId: getMemberInvitationV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/confirmationKey'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInvite'
        "404":
          description: Not Found
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
    post:
      tags:
        - UserManagement/Invitation
      summary: Accept member invitation
      operationId: acceptMemberInvitationV2
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/confirmationKey'
      requestBody:
        description: The invitation payload to accept
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserInvite'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInvite'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /orgs/{orgHandle}/v2/invitations:
    get:
      tags:
        - UserManagement/Invitation
      summary: List member invitations
      operationId: listMemberInvitations
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InvitationV2'
      security:
        - OAuth2Security:
            - invitation_view
            - invitation_manage
        - default:
            - urn:choreosystem:usermanagement:invitation_view
            - urn:choreosystem:usermanagement:invitation_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/Invitation
      summary: Send member invitation
      operationId: sendMemberInvitation
      parameters:
        - $ref: '#/components/parameters/orgHandle'
      requestBody:
        description: The invitation payload to create
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserInviteV2'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessfulList'
      security:
        - OAuth2Security:
            - invitation_send
            - invitation_manage
        - default:
            - urn:choreosystem:usermanagement:invitation_send
            - urn:choreosystem:usermanagement:invitation_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - UserManagement/Invitation
      summary: Delete member invitation
      operationId: deleteMemberInvitation
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/email'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
      security:
        - OAuth2Security:
            - invitation_delete
            - invitation_manage
        - default:
            - urn:choreosystem:usermanagement:invitation_delete
            - urn:choreosystem:usermanagement:invitation_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/v2/invitations/{id}:
    delete:
      tags:
        - UserManagement/Invitation
      summary: Delete member invitation by invitation id
      operationId: deleteMemberInvitationById
      parameters:
        - name: orgHandle
          in: path
          description: The organization handle
          required: true
          style: simple
          explode: false
          schema:
            type: string
            example: wso2
        - name: id
          in: path
          description: The id of the invitation
          required: true
          style: simple
          explode: false
          schema:
            type: string
            example: "1"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Invitation deleted successfully
      security:
        - OAuth2Security:
            - invitation_delete
            - invitation_manage
        - default:
            - urn:choreosystem:usermanagement:invitation_delete
            - urn:choreosystem:usermanagement:invitation_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{orgHandle}/v2/invitations/{confirmationKey}:
    get:
      tags:
        - UserManagement/Invitation
      summary: Get member invitation
      operationId: getMemberInvitation
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/confirmationKey'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInviteV2'
        "404":
          description: Not Found
      security:
        - default: []
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
    post:
      tags:
        - UserManagement/Invitation
      summary: Accept member invitation
      operationId: acceptMemberInvitation
      parameters:
        - $ref: '#/components/parameters/orgHandle'
        - $ref: '#/components/parameters/confirmationKey'
      requestBody:
        description: The invitation payload to accept
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserInviteV2'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInviteV2'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default: []
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /orgs/{org}/self-subscribe:
    post:
      tags:
        - UserManagement/Invitation
      summary: Add External User to Choreo System Organization
      description: Adds an external user to the choreosystem organization.
      parameters:
        - name: org
          in: path
          description: The organization ID or name.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties: {}
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{org}/self-unsubscribe:
    post:
      tags:
        - UserManagement/Invitation
      summary: Leave from the Organization
      description: Leave from the organization.
      parameters:
        - name: org
          in: path
          description: The organization handle.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties: {}
        "400":
          description: Bad request
        "403":
          description: Forbidden
        "404":
          description: Not found
        "500":
          description: Internal server error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /validate/orgname:
    get:
      tags:
        - UserManagement/UserValidation
      summary: Validate organization name on registration
      description: Validates the provided organization name.
      operationId: validateOrgName
      parameters:
        - $ref: '#/components/parameters/orgHandleQ'
        - $ref: '#/components/parameters/email'
      responses:
        "200":
          description: Successful validation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidOrg'
        "400":
          description: Invalid request
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /validate/user:
    get:
      tags:
        - UserManagement/UserValidation
      summary: Validates the IDP user
      operationId: validateIDPUser
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        "202":
          description: OK
        "400":
          description: Bad request
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "404":
          description: User does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /add-enterprise-user:
    post:
      tags:
        - UserManagement/UserValidation
      summary: Add Enterprise User to Organization
      operationId: addEnterpriseUserToOrganization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfile'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        "400":
          description: Bad request
        "404":
          description: User does not exist
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /register-user:
    post:
      tags:
        - UserManagement/UserValidation
      summary: Register User with Identity Provider
      operationId: registerIDPUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterUserPayload'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        "400":
          description: Bad request
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /user/salesforce-lead:
    post:
      tags:
        - UserManagement/UserValidation
      summary: Add New Salesforce Lead
      operationId: addNewSalesforceLead
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddNewSFLeadRequest'
        required: true
      responses:
        "200":
          description: Salesforce lead added successfully.
        "400":
          description: Invalid input parameters.
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /consent/tos:
    get:
      tags:
        - UserManagement/Consent
      summary: Get user's terms of service consent
      operationId: getTermsOfServiceConsent
      parameters:
        - $ref: '#/components/parameters/uuidQ'
        - $ref: '#/components/parameters/serviceName'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TermsOfServiceConsent'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - UserManagement/Consent
      summary: Set user's terms of service consent
      operationId: setTermsOfServiceConsent
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TermsOfServiceConsent'
        required: true
      responses:
        "200":
          description: No content
        "400":
          description: Bad request
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /consent/user:
    get:
      tags:
        - UserManagement/Consent
      summary: Get email consent
      description: Retrieves the email consent status for the user
      operationId: getEmailConsent
      parameters:
        - name: email
          in: query
          description: Email address of the user
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Email consent status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailConsent'
        "400":
          description: Invalid email parameter
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - UserManagement/Consent
      summary: Send email consent
      description: Sends an email consent to the user
      operationId: sendEmailConsent
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailConsent'
        required: true
      responses:
        "200":
          description: Email consent sent successfully
        "400":
          description: Invalid request body
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          description: The ID of the user
        uuid:
          type: string
          description: The IDP (Identity Provider) ID of the user
        pictureUrl:
          type: string
          format: uri
          description: The URL of the user's profile picture
        email:
          type: string
          format: email
          description: The email address of the user
        displayName:
          type: string
          description: The display name of the user
        roles:
          type: array
          description: The roles associated with the user
          items:
            $ref: '#/components/schemas/Role'
    UserRoleTag:
      type: object
      properties:
        handle:
          type: string
          description: A unique identifier for the tag
    UserGroupTag:
      type: object
      properties:
        handle:
          type: string
          description: A unique identifier for the tag
    Permissions:
      type: object
      properties:
        domain:
          type: string
        list:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
        checked:
          type: boolean
    Permission:
      type: object
      properties:
        id:
          type: string
        displayName:
          type: string
        handle:
          type: string
        domainArea:
          type: string
        checked:
          type: boolean
    InvalidUser:
      type: object
      properties:
        idp_id:
          type: string
        msg:
          type: string
    ListUsersOfRoleResponse:
      type: object
      properties:
        paginatedUser:
          $ref: '#/components/schemas/PaginatedUser'
    PaginatedUser:
      type: object
      properties:
        count:
          type: integer
          format: int64
        list:
          type: array
          items:
            $ref: '#/components/schemas/User'
        pagination:
          $ref: '#/components/schemas/Pagination'
    GroupRoleMapping:
      type: object
      properties:
        Id:
          type: string
          description: Id
          readOnly: true
        groupName:
          type: string
          description: group name
        roleList:
          type: array
          items:
            type: string
        createdAt:
          type: string
          readOnly: true
        updatedAt:
          type: string
          readOnly: true
      title: GroupRoleMapping
    GroupRoleMappingPayload:
      type: object
      properties:
        groupName:
          type: string
          description: group name
        roleList:
          type: array
          items:
            type: string
    SuccessfulList:
      type: object
      properties:
        successfulList:
          type: array
          items:
            type: string
            format: email
            description: An email address that was successfully processed
    AddNewSFLeadRequest:
      type: object
      properties:
        ip_address:
          type: string
        email:
          type: string
        idp_id:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        visitor_id:
          type: string
        utm_source:
          type: string
        utm_medium:
          type: string
        utm_campaign:
          type: string
        profile:
          type: string
    UserInvite:
      type: object
      properties:
        application:
          type: string
          description: The name of the application sending the invitation
        emails:
          type: array
          description: An array of email addresses for the users being invited
          items:
            type: string
        roles:
          type: array
          description: An array of roles to assign to the invited users
          items:
            type: string
    UserInviteV2:
      type: object
      properties:
        application:
          type: string
          description: The name of the application sending the invitation
        emails:
          type: array
          description: An array of email addresses for the users being invited
          items:
            type: string
        groups:
          type: array
          description: An array of groups to assign to the invited users
          items:
            type: string
    Invitation:
      type: object
      properties:
        email:
          type: string
          format: email
          description: The email address of the user
        handle:
          type: string
          description: The handle of the user
        roles:
          type: array
          items:
            type: string
            description: The role(s) assigned to the user
    InvitationV2:
      type: object
      properties:
        email:
          type: string
          format: email
          description: The email address of the user
          example: <EMAIL>
        id:
          type: integer
          description: The id of the invitation
          example: 1
        handle:
          type: string
          description: The handle of the user
          example: john
        groups:
          type: array
          example:
            - admin
            - moderator
          items:
            type: string
            description: The group(s) assigned to the user
    TermsOfServiceConsent:
      type: object
      properties:
        uuid:
          type: string
          description: The ID of the identity provider that provides the terms of service.
        version:
          type: string
          description: The version of the terms of service.
        serviceName:
          type: string
          description: The name of the service that requires the user's consent to the terms of service.
        isAccepted:
          type: boolean
          description: Whether the user has accepted the terms of service.
    EmailConsent:
      type: object
      properties:
        promptConsent:
          type: boolean
    ValidOrg:
      type: object
      properties:
        isValid:
          type: boolean
          description: Indicates whether the organization is valid or not
        organizationHandle:
          type: string
          description: Handle of the validated organization
    Organizations:
      type: array
      items:
        type: object
        properties:
          id:
            type: string
          uuid:
            type: string
          handle:
            type: string
          name:
            type: string
          owner:
            type: object
            properties:
              id:
                type: string
              uuid:
                type: string
              createdAt:
                type: string
                format: date-time
    UserProfile:
      type: object
      properties:
        displayName:
          type: string
        userEmail:
          type: string
          format: email
        userProfilePictureUrl:
          type: string
          format: uri
        uuid:
          type: string
        organizations:
          $ref: '#/components/schemas/Organizations'
        userId:
          type: string
        userCreatedAt:
          type: string
          format: date-time
    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
        type:
          type: string
        message:
          type: string
        cause:
          type: string
    RolePermission:
      type: object
      properties:
        additionPermissionList:
          type: array
          description: The list of permissions to add to the role
          items:
            $ref: '#/components/schemas/Permission'
        deletionPermissionList:
          type: array
          description: The list of permissions to remove from the role
          items:
            $ref: '#/components/schemas/Permission'
    AdditionalRoleHandles:
      type: object
      properties:
        additionRoleHandles:
          type: array
          description: An array of role handles to add to a user
          items:
            type: string
    AdditionalUserIds:
      type: object
      properties:
        additionalUserIds:
          type: array
          description: An array of user IDs to add to a role
          items:
            type: string
    Roles:
      type: object
      properties:
        count:
          type: integer
        list:
          type: array
          items:
            $ref: '#/components/schemas/Role'
        pagination:
          $ref: '#/components/schemas/Pagination'
    GroupRoleMappings:
      type: object
      properties:
        count:
          type: integer
        list:
          type: array
          items:
            $ref: '#/components/schemas/GroupRoleMapping'
        pagination:
          $ref: '#/components/schemas/Pagination'
    Pagination:
      type: object
      properties:
        limit:
          type: integer
        total:
          type: integer
    RolePayload:
      type: object
      properties:
        description:
          type: string
          description: A description of the role
        displayName:
          type: string
          description: The display name of the role
        tags:
          type: array
          description: Tags associated with the role
          items:
            $ref: '#/components/schemas/UserRoleTag'
        permissions:
          type: array
          description: Permissions associated with the role
          items:
            $ref: '#/components/schemas/Permission'
    GroupPayload:
      type: object
      properties:
        displayName:
          type: string
          description: The display name of the group
        description:
          type: string
          description: A description of the group
        tags:
          type: array
          description: Tags associated with the group
          items:
            $ref: '#/components/schemas/UserGroupTag'
    UserGroupsPayload:
      type: object
      properties:
        groupUUIDs:
          type: array
          items:
            type: string
            format: uuid
    GroupMembersPayload:
      type: object
      properties:
        userIds:
          type: array
          items:
            type: string
            format: uuid
    GroupAssignmentPayload:
      type: object
      properties:
        mappedResourceUUID:
          type: string
          description: The UUID of the resource to which the group is being assigned
        mappingLevel:
          type: string
          description: The level at which the group is being assigned
          enum:
            - ORG
            - PROJECT
        groupUUID:
          type: string
    RoleAssignmentPayload:
      type: object
      properties:
        mappedResourceUUID:
          type: string
          description: The UUID of the resource to which the role is being assigned
        mappingLevel:
          type: string
          description: The level at which the role is being assigned
          enum:
            - ORG
            - PROJECT
        roleHandle:
          type: string
    RoleGroupsAssignmentPayload:
      type: object
      properties:
        mappedResourceUUID:
          type: string
          description: The UUID of the resource to which the role is being assigned
        mappingLevel:
          type: string
          description: The level at which the role is being assigned
          enum:
            - ORG
            - PROJECT
        groupUUIDs:
          type: array
          items:
            type: string
            format: uuid
    GroupsRemovePayload:
      type: object
      properties:
        groupAssociations:
          type: array
          items:
            $ref: '#/components/schemas/GroupAssignmentPayload'
    GroupRolesAssignmentPayload:
      type: object
      properties:
        mappedResourceUUID:
          type: string
          description: The UUID of the resource to which the group is being assigned
        mappingLevel:
          type: string
          description: The level at which the group is being assigned
          enum:
            - ORG
            - PROJECT
        roleUUIDs:
          type: array
          items:
            type: string
            format: uuid
    RolesRemovePayload:
      type: object
      properties:
        roleAssociations:
          type: array
          items:
            $ref: '#/components/schemas/RoleAssignmentPayload'
    RegisterUserPayload:
      type: object
      properties:
        organization:
          type: object
          properties:
            name:
              type: string
              description: The name of the user's organization
        termsAccepted:
          type: boolean
          description: Whether the user has accepted the terms of service
        serviceName:
          type: string
          description: The name of the service the user is registering for
    AddOrganizationUsersRequest:
      type: object
      properties:
        user_idp_id_list:
          type: array
          items:
            type: string
            format: uuid
        organization_name:
          type: string
        group:
          type: string
    FindUsersByOrganizationByListRequest:
      type: object
      properties:
        organization_uuid:
          type: string
          minLength: 1
        user_idp_id_list:
          type: array
          items:
            type: string
            format: uuid
    FindUsersByOrganizationByListResponse:
      type: object
      properties:
        valid_users:
          type: array
          items:
            $ref: '#/components/schemas/User'
        invalid_users:
          type: array
          items:
            $ref: '#/components/schemas/InvalidUser'
    Role:
      type: object
      properties:
        id:
          type: integer
          description: The ID of the role
        description:
          type: string
          description: A description of the role
        defaultRole:
          type: boolean
          description: Whether this is the default role for a user
        displayName:
          type: string
          description: The display name of the role
        handle:
          type: string
          description: A unique identifier for the role
        tags:
          type: array
          description: Tags associated with the role
          items:
            $ref: '#/components/schemas/UserRoleTag'
        permissions:
          type: array
          description: Permissions associated with the role
          items:
            $ref: '#/components/schemas/Permission'
        users:
          type: array
          description: Users associated with the role
          items:
            $ref: '#/components/schemas/User'
        createdBy:
          type: string
          description: The ID of the user who created the role
        updatedBy:
          type: string
          description: The ID of the user who last updated the role
        createdAt:
          type: string
          format: date-time
          description: The date and time the role was created
        updatedAt:
          type: string
          format: date-time
          description: The date and time the role was last updated
        uuid:
          type: string
          description: The UUID of the role
    Group:
      type: object
      properties:
        id:
          type: integer
          description: The ID of the group
        displayName:
          type: string
          description: The display name of the group
        handle:
          type: string
          description: A unique identifier for the group
        tags:
          type: array
          description: Tags associated with the group
          items:
            $ref: '#/components/schemas/UserGroupTag'
        users:
          type: array
          description: Users associated with the group
          items:
            $ref: '#/components/schemas/User'
        createdBy:
          type: string
          description: The ID of the user who created the group
        updatedBy:
          type: string
          description: The ID of the user who last updated the group
        createdAt:
          type: string
          format: date-time
          description: The date and time the group was created
        updatedAt:
          type: string
          format: date-time
          description: The date and time the group was last updated
        uuid:
          type: string
          description: The UUID of the group
    GroupList:
      type: object
      properties:
        count:
          type: integer
          description: The number of groups in the list
        list:
          type: array
          description: The list of groups
          items:
            $ref: '#/components/schemas/Group'
        pagination:
          $ref: '#/components/schemas/Pagination'
    UserGroups:
      type: object
      properties:
        userId:
          type: string
          description: The ID of the user
        groups:
          type: array
          items:
            $ref: '#/components/schemas/Group'
    GroupMembers:
      type: object
      properties:
        groupId:
          type: string
          description: The ID of the group
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
    GroupAssignment:
      type: object
      properties:
        groupHandle:
          type: string
          description: The handle of the group
        groupDisplayName:
          type: string
          description: The display name of the group
        groupUUID:
          type: string
          description: The UUID of the group
        mappingLevel:
          type: string
          description: The level at which the group is being assigned
          enum:
            - ORG
            - PROJECT
        mappedResourceUUID:
          type: string
          description: The UUID of the resource to which the group is being assigned
    RoleAssignment:
      type: object
      properties:
        roleHandle:
          type: string
          description: The handle of the role
        roleDisplayName:
          type: string
          description: The display name of the role
        roleUUID:
          type: string
          description: The UUID of the role
        mappingLevel:
          type: string
          description: The level at which the role is being assigned
          enum:
            - ORG
            - PROJECT
        mappedResourceUUID:
          type: string
          description: The UUID of the resource to which the role is being assigned
    GroupRolesAssignment:
      type: object
      properties:
        groupUUID:
          type: string
          description: The UUID of the group
        roleAssociations:
          type: array
          items:
            $ref: '#/components/schemas/RoleAssignment'
    RoleGroupsAssignment:
      type: object
      properties:
        roleUUID:
          type: string
          description: The UUID of the role
        groupAssociations:
          type: array
          items:
            $ref: '#/components/schemas/GroupAssignment'
    EnterpriseGroupPayload:
      type: object
      properties:
        orgHandle:
          type: string
          description: The organization handle
        enterpriseGroupName:
          type: string
          description: The name of the enterprise group
        choreoGroupUUIDs:
          type: array
          items:
            type: string
            format: uuid
    EnterpriseGroup:
      type: object
      properties:
        orgUUID:
          type: string
          description: The organization UUID
        enterpriseGroupName:
          type: string
          description: The name of the enterprise group
        choreoGroups:
          type: array
          items:
            $ref: '#/components/schemas/Group'
    ListEnterpriseGroups:
      type: object
      properties:
        list:
          type: array
          items:
            $ref: '#/components/schemas/EnterpriseGroup'
  parameters:
    orgHandle:
      name: orgHandle
      in: path
      description: The organization handle
      required: true
      style: simple
      explode: false
      schema:
        type: string
    orgHandleQ:
      name: orgName
      in: query
      description: The organization name of the user
      required: false
      style: form
      explode: true
      schema:
        type: string
    uuid:
      name: uuid
      in: path
      description: The ID of the user
      required: true
      style: simple
      explode: false
      schema:
        type: string
    uuidQ:
      name: idpId
      in: query
      description: The ID of the user
      required: true
      style: form
      explode: true
      schema:
        type: string
    roleHandle:
      name: roleHandle
      in: path
      description: The role handle
      required: true
      style: simple
      explode: false
      schema:
        type: string
    groupHandle:
      name: groupHandle
      in: path
      description: The name of the group
      required: true
      style: simple
      explode: false
      schema:
        type: string
    groupName:
      name: groupName
      in: path
      description: The name of the group
      required: true
      style: simple
      explode: false
      schema:
        type: string
    email:
      name: email
      in: query
      description: The email of the user
      required: false
      style: form
      explode: true
      schema:
        type: string
    limit:
      name: limit
      in: query
      description: Number of items per page
      required: false
      style: form
      explode: true
      schema:
        type: integer
    confirmationKey:
      name: confirmationKey
      in: path
      description: Invitation confirmation key
      required: true
      style: simple
      explode: false
      schema:
        type: string
    serviceName:
      name: serviceName
      in: query
      description: Name of the service
      required: false
      style: form
      explode: true
      schema:
        type: string
  securitySchemes:
    OAuth2Security:
      type: oauth2
      flows:
        password:
          tokenUrl: https://sts.choreo.dev/oauth2/token
          scopes:
            openid: Authorize access to user details
            user_delete: Create user
            user_view: View user
            user_update: Update users
            user_manage: Manage users
            permission_view: View permissions
            role_view: View roles
            role_create: Create roles
            role_delete: Delete roles
            role_update: Update roles
            role_manage: Manage roles
            role_mapping_view: View group role mapping
            role_mapping_create: Create group role mapping
            role_mapping_delete: Delete group role mapping
            role_mapping_update: Update group role mapping
            role_mapping_manage: Manage group role mapping
            invitation_send: Send invitation
            invitation_view: View invitation
            invitation_delete: Delete invitation
            invitation_manage: Manage invitation
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            urn:choreosystem:usermanagement:role_create: Create roles
            urn:choreosystem:usermanagement:permission_view: View permissions
            urn:choreosystem:usermanagement:role_mapping_create: Create group role mapping
            urn:choreosystem:usermanagement:user_update: Update users
            urn:choreosystem:usermanagement:openid: Authorize access to user details
            urn:choreosystem:usermanagement:role_view: View roles
            urn:choreosystem:usermanagement:role_mapping_update: Update group role mapping
            urn:choreosystem:usermanagement:user_view: View user
            urn:choreosystem:usermanagement:role_delete: Delete roles
            urn:choreosystem:usermanagement:role_mapping_manage: Manage group role mapping
            urn:choreosystem:usermanagement:role_mapping_delete: Delete group role mapping
            urn:choreosystem:usermanagement:role_mapping_view: View group role mapping
            urn:choreosystem:usermanagement:user_delete: Create user
            urn:choreosystem:usermanagement:invitation_manage: Manage invitation
            urn:choreosystem:usermanagement:invitation_view: View invitation
            urn:choreosystem:usermanagement:role_manage: Manage roles
            urn:choreosystem:usermanagement:invitation_send: Send invitation
            urn:choreosystem:usermanagement:invitation_delete: Delete invitation
            urn:choreosystem:usermanagement:user_manage: Manage users
            urn:choreosystem:usermanagement:role_update: Update roles
          x-scopes-bindings:
            urn:choreosystem:usermanagement:role_mapping_update: ""
            urn:choreosystem:usermanagement:user_update: ""
            urn:choreosystem:usermanagement:role_mapping_delete: ""
            urn:choreosystem:usermanagement:openid: ""
            urn:choreosystem:usermanagement:invitation_view: ""
            urn:choreosystem:usermanagement:role_mapping_view: ""
            urn:choreosystem:usermanagement:role_update: ""
            urn:choreosystem:usermanagement:user_delete: ""
            urn:choreosystem:usermanagement:role_create: ""
            urn:choreosystem:usermanagement:invitation_delete: ""
            urn:choreosystem:usermanagement:invitation_manage: ""
            urn:choreosystem:usermanagement:user_manage: ""
            urn:choreosystem:usermanagement:role_mapping_manage: ""
            urn:choreosystem:usermanagement:permission_view: ""
            urn:choreosystem:usermanagement:role_mapping_create: ""
            urn:choreosystem:usermanagement:role_manage: ""
            urn:choreosystem:usermanagement:invitation_send: ""
            urn:choreosystem:usermanagement:role_view: ""
            urn:choreosystem:usermanagement:role_delete: ""
            urn:choreosystem:usermanagement:user_view: ""
x-wso2-disable-security: false
x-business-owner:
  name: Sumedha Kodithuwakku
  email: <EMAIL>
x-technical-owner:
  name: Sumedha Kodithuwakku
  email: <EMAIL>
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://api-server.prod-choreo-system.svc.cluster.local:80
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://api-server.prod-choreo-system.svc.cluster.local:80
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/user-mgt/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
