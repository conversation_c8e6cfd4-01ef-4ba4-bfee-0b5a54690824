openapi: 3.0.0
info:
  title: Configuration Schema Service
  version: 0.1.0
servers:
  - url: https://api.choreo.dev
    description: Production Environment
security:
  - default: []
paths:
  /projects/{projectId}/components/{componentId}/env-template/{envTemplateId}/deployment-track/{deploymentTrackId}/configurations:
    get:
      summary: Get configurations for a component version in a specific env
      operationId: getConfigurations
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/envTemplateId'
        - $ref: '#/components/parameters/deploymentTrackId'
        - $ref: '#/components/parameters/commitHash'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConfigurationResponse'
        "204":
          description: NoContent
        "500":
          description: InternalServerError
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    post:
      summary: Upsert Configurations for a component version in a specific env
      operationId: createOrUpdateConfigurations
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/envTemplateId'
        - $ref: '#/components/parameters/deploymentTrackId'
      requestBody:
        description: Configuration Mapping object
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConfigurationPayload'
      responses:
        "200":
          description: Ok
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationResponse'
        "204":
          description: NoContent
        "400":
          description: BadRequest
        "500":
          description: Internal server error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      summary: Delete configurations for a component version in a specific env
      operationId: deleteConfigurations
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
        - $ref: '#/components/parameters/envTemplateId'
        - $ref: '#/components/parameters/deploymentTrackId'
      responses:
        "200":
          description: OK
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    GetConfigurationResponse:
      title: GetConfigurationResponse
      type: object
      properties:
        jsonSchema:
          type: string
          example: ewogICIkc2NoZW1hIjogImh0dHA6Ly9qc29uLXNjaGVtYS5vcmcvZHJhZnQtMDcvc2NoZW1hIyIsCiAgInR5cGUiOiAib2JqZWN0IiwKICAicHJvcGVydGllcyI6IHsKICAgICJSZWRpc19VcmwiOiB7CiAgICAgICJ0eXBlIjogInN0cmluZyIsCiAgICAgICJ0aXRsZSI6ICJSZWRpcyBVUkwiCiAgICB9LAogICAgIkRCX1VzZXIiOiB7CiAgICAgICJ0eXBlIjogInN0cmluZyIsCiAgICAgICJ0aXRsZSI6ICJEQiBVc2VybmFtZSIKICAgIH0sCiAgfSwKICAicmVxdWlyZWQiOiBbCiAgICAiREJfVXNlciIKICBdCn0=
        mappingId:
          type: string
          example: 01ef820b-0a40-11e0-976d-471817738f5b
        configurations:
          type: array
          items:
            $ref: '#/components/schemas/Configuration'
    UpdateConfigurationPayload:
      title: UpdateConfigurationPayload
      type: object
      properties:
        commitHash:
          type: string
          example: 29f45e219f994f81f8247af64ed0b56da35f848d
        mappingId:
          type: string
          example: 01ef820b-0a40-11e0-976d-471817738f5b
        configurations:
          type: array
          items:
            $ref: '#/components/schemas/Configuration'
    ConfigurationResponse:
      type: array
      items:
        $ref: '#/components/schemas/Configuration'
    Configuration:
      required:
        - isRequired
        - key
        - keyId
        - parentKey
        - parentObjectType
        - valueType
        - values
      type: object
      properties:
        keyId:
          type: string
          description: The UUID of the configuration key.
          readOnly: true
          example: a4a44c50-90da-4f73-b22c-90d154d9ecb9
        key:
          type: string
          description: The name of the configuration key.
          example: logging.package
        values:
          type: array
          description: The value for the configuration key for each environment.
          items:
            $ref: '#/components/schemas/Value'
        isRequired:
          type: boolean
          readOnly: true
          example: true
        valueType:
          type: string
          readOnly: true
          example: string
        parentKey:
          type: string
          readOnly: true
          example: logging
        parentObjectType:
          type: string
          readOnly: true
          example: object
    Value:
      required:
        - environmentUuid
        - value
      type: object
      properties:
        value:
          type: string
          description: The value for the configuration key.
          example: info
        valueRef:
          type: string
          description: The KV reference for the configuration key if it is sensitive.
          example: ca5f8026-d06a-4b1a-8fca-1aa31a4d7c6b
        environmentUuid:
          type: string
          description: The template UUID of the environment to which the configuration value is associated.
          example: e7d66e09-2a5d-49cf-afc5-d2213c8a02f0
  parameters:
    projectId:
      name: projectId
      in: path
      description: The UUID of the project.
      required: true
      style: simple
      explode: false
      schema:
        type: string
        example: 8b5ecf84-3de1-4edd-a7fe-a707ee81d5b0
    componentId:
      name: componentId
      in: path
      description: The UUID of the component.
      required: true
      style: simple
      explode: false
      schema:
        type: string
        example: 7812d359-da17-477e-bede-b2bc5a7f6554
    envTemplateId:
      name: envTemplateId
      in: path
      description: The UUID of the env template.
      required: true
      style: simple
      explode: false
      schema:
        type: string
        example: e7d66e09-2a5d-49cf-afc5-d2213c8a02f0
    deploymentTrackId:
      name: deploymentTrackId
      in: path
      description: The UUID of the deployment track
      required: true
      style: simple
      explode: false
      schema:
        type: string
        example: e7d66e09-2a5d-49cf-afc5-d2213c8a02f0
    commitHash:
      name: commitHash
      in: query
      description: The UUID of the deployment track
      required: false
      style: form
      explode: true
      schema:
        type: string
        example: 29f45e219f994f81f8247af64ed0b56da35f848d
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://configuration-schema-service.prod-choreo-system:80
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://configuration-schema-service.prod-choreo-system:80
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/configuration-schema/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
