{"data": {"component": {"id": "5a43c827-4876-4068-a51c-5bc4e5c85847", "name": "Configuration Schema Service", "handler": "Configuration Schema Service", "description": " ", "displayType": "proxy", "displayName": "Configuration Schema Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-10-07T08:28:00.685Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Configuration Schema Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/configuration-schema", "proxyId": "67038cf4f86e5c6a5a320084", "id": "67038cf4f86e5c6a5a320084", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67038cf4f86e5c6a5a320084", "createdAt": "1728285940200", "updatedAt": "2024-10-07 07:25:40.2", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "5a43c827-4876-4068-a51c-5bc4e5c85847", "latest": true, "versionStrategy": ""}]}}}