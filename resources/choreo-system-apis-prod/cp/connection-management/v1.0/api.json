{"id": "6544d9e29a886f6d1afddd22", "name": "Connection Management", "displayName": "Connection Management", "description": "This api is used to connect to the Connection Management service", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections", "version": "v1.0", "provider": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Gold"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1699011042225", "lastUpdatedTime": "2025-07-25 11:56:06.599", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://connection-service.prod-choreo-system:9000/connections/v1"}, "production_endpoints": {"url": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "endpointImplementationType": "ENDPOINT", "subTypeConfiguration": {"subType": "DEFAULT", "egress": false, "configuration": null}, "tokenBasedThrottlingConfiguration": null, "scopes": [], "scopePrefix": "", "operations": [{"id": "", "target": "/configurations/service-configs/third-party-connections", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/third-party-connections", "verb": "POST", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/third-party-connections/{connectionId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/third-party-connections/{connectionId}", "verb": "PUT", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/third-party-connections/{connectionId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/third-party-connections/{connectionId}", "verb": "DELETE", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/choreo-connections", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/choreo-connections", "verb": "POST", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/choreo-connections/{connectionId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/choreo-connections/{connectionId}", "verb": "PUT", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/choreo-connections/{connectionId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/choreo-connections/{connectionId}", "verb": "DELETE", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/choreo-connections/refresh/{connectionId}", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/choreo-connections/refresh/{connectionId}", "verb": "POST", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/connections", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/connections", "verb": "GET", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/connections/{connectionId}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/connections/{connectionId}", "verb": "GET", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/choreo-database-connections", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/choreo-database-connections", "verb": "POST", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}, {"id": "", "target": "/configurations/service-configs/choreo-database-connections/{connectionId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "49cb9e4f-648b-4a7c-9249-933ff42e5f7b", "backendOperation": {"target": "/configurations/service-configs/choreo-database-connections/{connectionId}", "verb": "DELETE", "endpoint": "http://connection-service.prod-choreo-system:9000/connections/v1"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": "http://connection-service.prod-choreo-system:9000/connections/v1", "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "9c4ac4cb-27b6-4aa8-92ed-b90981f6d92a", "versionId": "6544d9e29a886f6d1afddd22"}}