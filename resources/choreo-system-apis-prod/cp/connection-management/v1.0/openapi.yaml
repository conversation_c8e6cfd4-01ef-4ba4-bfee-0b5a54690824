openapi: 3.0.1
info:
  title: Connection Management
  contact: {}
  version: v1.0
servers:
  - url: https://apis.choreo.dev/93tu/connections/v1.0
    variables:
      server:
        default: https://apis.choreo.dev/connections/v1
      port:
        default: "443"
security:
  - default: []
paths:
  /configurations/service-configs/third-party-connections:
    post:
      summary: Creates connection configuration for a third party service.
      operationId: postConfigurationsServiceConfigsThirdPartyConnections
      parameters:
        - name: wellKnownService
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      requestBody:
        description: Connection configuration request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionRequest'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connection'
        "409":
          description: Conflict
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/third-party-connections/{connectionId}:
    put:
      summary: Updates connection configuration for a third party service.
      operationId: putConfigurationsServiceConfigsThirdPartyConnectionsConnectionid
      parameters:
        - name: connectionId
          in: path
          description: Connection configuration id
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Connection configuration request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionRequest'
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Deletes connection configuration for a third party service.
      operationId: deleteConfigurationsServiceConfigsThirdPartyConnectionsConnectionid
      parameters:
        - name: connectionId
          in: path
          description: Connection configuration id
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
        "404":
          description: NotFound
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/third-party-connections/refresh/{connectionId}:
    post:
      summary: Updates connection configuration for a third party service.
      operationId: postConfigurationsServiceConfigsThirdPartyConnectionsRefreshConnectionid
      parameters:
        - name: connectionId
          in: path
          description: Connection configuration id
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: wellKnownService
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: false
      requestBody:
        description: Connection configuration request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionRequest'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connection'
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/choreo-connections:
    post:
      summary: Creates connection configuration for a choreo service.
      operationId: postConfigurationsServiceConfigsChoreoConnections
      parameters:
        - name: generateCreds
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: true
      requestBody:
        description: Connection configuration request payload
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChoreoConnectionRequest'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connection'
        "409":
          description: Conflict
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
        "404":
          description: NotFound
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/choreo-database-connections:
    post:
      summary: Creates connection configuration for a choreo database.
      operationId: postConfigurationsServiceConfigsChoreoDatabaseConnections
      requestBody:
        description: Connection configuration request payload
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceConnectionRequest'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connection'
        "409":
          description: Conflict
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
        "404":
          description: NotFound
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/choreo-database-connections/{connectionId}:
    put:
      summary: Updated connection configuration for a choreo database.
      operationId: putConfigurationsServiceConfigsChoreoDatabaseConnectionsConnectionid
      parameters:
        - name: connectionId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Connection configuration request payload
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceConnectionRequest'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connection'
        "409":
          description: Conflict
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
        "404":
          description: NotFound
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
    delete:
      operationId: deleteConfigurationsServiceConfigsChoreoDatabaseConnectionsConnectionid
      parameters:
        - name: connectionId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
        "404":
          description: NotFound
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/choreo-connections/refresh/{connectionId}:
    post:
      operationId: postConfigurationsServiceConfigsChoreoConnectionsRefreshConnectionid
      parameters:
        - name: connectionId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: generateCreds
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChoreoConnectionRequest'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Connection'
        "409":
          description: Conflict
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/choreo-connections/{connectionId}:
    put:
      summary: Updates connection configuration for a choreo service.
      operationId: putConfigurationsServiceConfigsChoreoConnectionsConnectionid
      parameters:
        - name: connectionId
          in: path
          description: Connection configuration id
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Connection configuration request payload
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChoreoConnectionRequest'
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Deletes connection configuration for a choreo service.
      operationId: deleteConfigurationsServiceConfigsChoreoConnectionsConnectionid
      parameters:
        - name: connectionId
          in: path
          description: Connection configuration id
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            text/plain:
              schema:
                type: string
        "404":
          description: NotFound
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/connections:
    get:
      summary: Searches connection configurations.
      operationId: getConfigurationsServiceConfigsConnections
      parameters:
        - name: serviceId
          in: query
          description: Id of the service to filter connections for
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: schemaId
          in: query
          description: Id of the schema to filter connections for
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: environmentId
          in: query
          description: Id of the environment to filter connections
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: projectId
          in: query
          description: Id of the project to filter connections for. This will return connections visible to the project. If not provided, connections visible to the user organization be returned.
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: componentId
          in: query
          description: Id of the component to filter connections for. This will return connections visible to the component. If not provided, connections visible to the user organization (or if project id is provided, connections visible to project) will be returned.
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: deploymentTrackId
          in: query
          description: Id of the deployment track to filter connections for. This will return connections visible to the deployment track.
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: scope
          in: query
          required: false
          style: form
          explode: true
          schema: {}
        - name: offset
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
        - name: limit
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
        - name: strictVisibilityFilteringEnabled
          in: query
          description: If set to true, only connections matching to the provided visibility is returned. If set to false, connections       consumable (including upper visiblity levels) to the specified visibility is returned. Default value is true.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: true
        - name: resolveServiceName
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: true
      responses:
        "202":
          description: Accepted
        "400":
          description: BadRequest
        "200":
          description: Ok
          content:
            application/json:
              schema:
                oneOf:
                  - type: array
                    items:
                      $ref: '#/components/schemas/Connection'
                  - type: array
                    items:
                      $ref: '#/components/schemas/EnvConnection'
                  - type: array
                    items:
                      $ref: '#/components/schemas/ConnectionListingRecord'
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
        "404":
          description: NotFound
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/connections/{connectionId}:
    get:
      summary: Retrive specific connection configuration.
      operationId: getConfigurationsServiceConfigsConnectionsConnectionid
      parameters:
        - name: connectionId
          in: path
          description: Id of the connection to retrieve
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: environmentId
          in: query
          description: Id of the environment to retrieve connection for. If not provided, configurations for all environments will be returned
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/Connection'
                  - $ref: '#/components/schemas/EnvConnection'
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
        "404":
          description: NotFound
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/connections/{connectionId}/rotate-keys:
    post:
      summary: Regenerate Credentials.
      operationId: postConfigurationsServiceConfigsConnectionsConnectionidRotateKeys
      parameters:
        - name: connectionId
          in: path
          description: Id of the connection to regenerate credentials for.
          required: true
          style: simple
          explode: false
          schema:
            type: string
            example: 01efded3-683b-1f46-8d1c-092fc625f378
        - name: environmentId
          in: query
          description: Id of the environment to regenerate credentials for.
          required: true
          style: form
          explode: true
          schema:
            type: string
            example: c7d84c3b-3c1a-4e77-b7c8-d8a786a341a7
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/Connection'
                  - $ref: '#/components/schemas/EnvConnection'
        "400":
          description: BadRequest
        "403":
          description: Forbidden
        "404":
          description: NotFound
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/choreo-connections:
    delete:
      operationId: deleteConfigurationsChoreoConnections
      parameters:
        - name: projectId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: componentId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: serviceId
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: schemaId
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: environmentId
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: deploymentTrackId
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: strictVisibilityFilteringEnabled
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
            default: true
      responses:
        "200":
          description: Ok
        "500":
          description: InternalServerError
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/choreo-connections/{connectionId}/resiliency:
    get:
      summary: Retrive resiliancy configuraiotn for the connection.
      operationId: getConfigurationsServiceConfigsChoreoConnectionsConnectionidResiliency
      parameters:
        - name: connectionId
          in: path
          description: connection string (which is common to all environments)
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ConnResiliencyResponse'
                  - $ref: '#/components/schemas/EnvConnection'
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
        "404":
          description: NotFound
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Update resiliancy configuraiotn for the connection.
      operationId: postConfigurationsServiceConfigsChoreoConnectionsConnectionidResiliency
      parameters:
        - name: connectionId
          in: path
          description: connection string (which is common to all environments)
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Resiliency configuration request payload
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnResiliencyRequestBody'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ConnResiliencyResponse'
                  - $ref: '#/components/schemas/EnvConnection'
        "400":
          description: BadRequest
        "500":
          description: InternalServerError
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
        "403":
          description: Forbidden
        "404":
          description: NotFound
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /connections/resolve:
    post:
      operationId: postConnectionsResolve
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectionConfig'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConnectionListingRecord'
        "404":
          description: NotFound
        "400":
          description: BadRequest
        "202":
          description: Accepted
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configurations/service-configs/choreo-connections/rotate-keys:
    post:
      operationId: postConfigurationsServiceConfigsChoreoConnectionsRotateKeys
      parameters:
        - name: environmentId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: projectId
          in: query
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: componentId
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Ok
        "500":
          description: InternalServerError
        "403":
          description: Forbidden
        "404":
          description: NotFound
        "400":
          description: BadRequest
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    ChoreoEnvironment:
      type: object
      properties:
        id:
          type: string
        isCritical:
          type: boolean
      required:
        - id
        - isCritical
    VisibilityProject:
      type: object
      properties:
        organizationUuid:
          type: string
        projectUuid:
          type: string
      required:
        - organizationUuid
        - projectUuid
    ConnResiliencyResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ConnResiliencyResponseBody'
      required:
        - data
    ResourceConnectionRequest:
      allOf:
        - $ref: '#/components/schemas/ConnectionMetadata'
        - type: object
          properties:
            envMapping:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/ResourceReference'
          required:
            - envMapping
    ConnectionMetadata:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        schemaReference:
          type: string
        visibilities:
          type: array
          items:
            $ref: '#/components/schemas/ConfigVisibility'
        serviceId:
          type: string
      required:
        - name
        - schemaReference
        - serviceId
        - visibilities
    EnvConnectionConfig:
      type: object
      description: Connection config specific to an environment
      properties:
        environmentUuid:
          type: string
        isCritical:
          type: boolean
        entries:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ConfigKeyEntry'
      required:
        - entries
        - environmentUuid
    Connection:
      allOf:
        - $ref: '#/components/schemas/ConnectionRequest'
        - type: object
          properties:
            groupUuid:
              type: string
            serviceName:
              type: string
            schemaName:
              type: string
            status: {}
            isPartiallyCreated:
              type: boolean
            resourceType:
              oneOf:
                - {}
                - {}
                - {}
                - {}
          required:
            - groupUuid
            - isPartiallyCreated
            - schemaName
            - serviceName
    ConnResiliencyResponseBody:
      type: object
      properties:
        connection_id:
          type: string
        conn_resiliency_data:
          $ref: '#/components/schemas/ConnResiliencyData'
      required:
        - conn_resiliency_data
        - connection_id
    ConfigKeyEntry:
      type: object
      description: Connection config key-value pair
      properties:
        key:
          type: string
        keyUuid:
          type: string
        value:
          type: string
        isSensitive:
          type: boolean
        isFile:
          type: boolean
      required:
        - isFile
        - isSensitive
        - key
        - value
    VisibilityOrganization:
      type: object
      properties:
        organizationUuid:
          type: string
      required:
        - organizationUuid
    VisibilityComponent:
      type: object
      properties:
        organizationUuid:
          type: string
        projectUuid:
          type: string
        componentUuid:
          type: string
      required:
        - componentUuid
        - organizationUuid
        - projectUuid
    EnvConnection:
      allOf:
        - $ref: '#/components/schemas/ConnectionMetadata'
        - type: object
          properties:
            groupUuid:
              type: string
            configuration:
              $ref: '#/components/schemas/EnvConnectionConfig'
            resourceType:
              oneOf:
                - {}
                - {}
                - {}
                - {}
          required:
            - configuration
            - groupUuid
            - resourceType
    ConfigVisibility:
      description: Connection visibility scope
      oneOf:
        - $ref: '#/components/schemas/VisibilityOrganization'
        - $ref: '#/components/schemas/VisibilityProject'
        - $ref: '#/components/schemas/VisibilityComponent'
        - $ref: '#/components/schemas/VisibilityDeploymentTrack'
    ConnResiliencyData:
      type: object
      properties:
        retry_config:
          oneOf:
            - $ref: '#/components/schemas/RetryConfig'
      required:
        - retry_config
    ChoreoConnectionRequest:
      allOf:
        - $ref: '#/components/schemas/ConnectionMetadata'
        - type: object
          properties:
            requestingServiceVisibility:
              type: string
              enum:
                - PUBLIC
                - ORGANIZATION
                - PROJECT
            orgIdInteger:
              type: integer
              format: int64
            environments:
              type: array
              items:
                $ref: '#/components/schemas/ChoreoEnvironment'
            allowedScopes:
              type: array
              description: Optional list of allowed scopes. If omitted, an empty array is used by default.
              example:
                - read:users
                - write:settings
              items:
                type: string
          required:
            - environments
            - orgIdInteger
            - requestingServiceVisibility
    ErrorPayload:
      type: object
      properties:
        reason:
          type: string
          description: Reason phrase
        path:
          type: string
          description: Request path
        method:
          type: string
          description: Method type of the request
        message:
          type: string
          description: Error message
        timestamp:
          type: string
          description: Timestamp of the error
        status:
          type: integer
          format: int32
          description: Relevant HTTP status code
    ConnectionRequest:
      allOf:
        - $ref: '#/components/schemas/ConnectionMetadata'
        - type: object
          properties:
            configurations:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/EnvConnectionConfig'
            envMapping:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/ResourceReference'
          required:
            - configurations
            - envMapping
    RetryConfig:
      type: object
      properties:
        is_enabled:
          type: boolean
        retry_conditions:
          type: array
          items:
            type: string
        retry_count:
          type: integer
          format: int64
        retry_back_off:
          type: integer
          format: int64
        per_try_timeout:
          type: integer
          format: int64
      required:
        - is_enabled
        - per_try_timeout
        - retry_back_off
        - retry_conditions
        - retry_count
    ConnectionListingRecord:
      type: object
      description: |-
        ConnectionListingRecord represents a single entry
        in the connection listing.
      properties:
        name:
          type: string
        description:
          type: string
        groupUuid:
          type: string
        schemaReference:
          type: string
        serviceId:
          type: string
        serviceName:
          type: string
        schemaName:
          type: string
        isPartiallyCreated:
          type: boolean
        status: {}
        componentId:
          type: string
        dependentComponentId:
          type: string
        version:
          type: string
        resourceType:
          oneOf:
            - {}
            - {}
            - {}
            - {}
      required:
        - componentId
        - dependentComponentId
        - description
        - groupUuid
        - name
        - resourceType
        - schemaReference
        - serviceId
    ResourceReference:
      type: object
      properties:
        resourceId:
          type: string
        parameterReference:
          type: string
      required:
        - parameterReference
        - resourceId
    ConnResiliencyRequestBody:
      type: object
      properties:
        conn_resiliency_data:
          $ref: '#/components/schemas/ConnResiliencyData'
        receiver_endpoint_hash:
          type: string
      required:
        - conn_resiliency_data
        - receiver_endpoint_hash
    VisibilityDeploymentTrack:
      type: object
      properties:
        organizationUuid:
          type: string
        projectUuid:
          type: string
        componentUuid:
          type: string
        deploymentTrackUuid:
          type: string
      required:
        - componentUuid
        - deploymentTrackUuid
        - organizationUuid
        - projectUuid
    ConnectionDescriptor:
      type: object
      description: Connection Descriptor contains name and connection identifier of the target component
      properties:
        name:
          type: string
        connectionId:
          type: string
      required:
        - connectionId
        - name
    ConnectionConfig:
      type: object
      description: Connection Configuration
      properties:
        descriptors:
          type: array
          items:
            $ref: '#/components/schemas/ConnectionDescriptor'
        componentId:
          type: string
      required:
        - componentId
        - descriptors
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://connection-service.prod-choreo-system:9000/connections/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://connection-service.prod-choreo-system:9000/connections/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
