{"data": {"component": {"id": "9c4ac4cb-27b6-4aa8-92ed-b90981f6d92a", "name": "connection-management", "handler": "sfnsnn", "description": " ", "displayType": "proxy", "displayName": "Connection Management", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2023-11-03T11:30:43.402Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Connection Management", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/connections", "proxyId": "6544d9e29a886f6d1afddd22", "id": "6544d9e29a886f6d1afddd22", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "6544d9e29a886f6d1afddd22", "createdAt": "1699011042225", "updatedAt": "2023-12-22 06:58:04.0", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "9c4ac4cb-27b6-4aa8-92ed-b90981f6d92a", "latest": true, "versionStrategy": ""}]}}}