{"data": {"component": {"id": "08e3e40f-c17c-4274-b69d-99afc6796f2a", "name": "url-management-api-tn", "handler": "<PERSON><PERSON><PERSON><PERSON>", "description": "Owned by Org-Mgt team", "displayType": "proxy", "displayName": "URL Management API", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-03-06T08:45:56.512Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "URL Management API", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/url-mgt", "proxyId": "65e82d435e87d93a849b8943", "id": "65e82d435e87d93a849b8943", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "65e82d435e87d93a849b8943", "createdAt": "1709714755963", "updatedAt": "2024-07-22 09:37:37.135", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "08e3e40f-c17c-4274-b69d-99afc6796f2a", "latest": true, "versionStrategy": ""}]}}}