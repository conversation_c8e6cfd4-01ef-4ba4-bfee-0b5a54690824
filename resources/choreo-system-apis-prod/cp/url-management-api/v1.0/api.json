{"id": "65e82d435e87d93a849b8943", "name": "URL Management API", "displayName": "URL Management API", "description": "Owned by Org-Mgt team", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/url-mgt", "version": "v1.0", "provider": "d962d47d-f855-4202-be7b-3b31e391d1f4", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": "<PERSON><PERSON><PERSON>", "businessOwnerEmail": "<EMAIL>", "technicalOwner": "<PERSON><PERSON><PERSON>", "technicalOwnerEmail": "<EMAIL>"}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1709714755963", "lastUpdatedTime": "2025-05-16 05:18:05.606", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://choreo-url-management.prod-choreo-system:9090/url-mgt"}, "production_endpoints": {"url": "http://choreo-url-management.prod-choreo-system:9090/url-mgt"}}, "endpointImplementationType": "ENDPOINT", "scopes": [{"scope": {"id": null, "name": "choreo:domain_manage", "displayName": "choreo:domain_manage", "description": "Manage Custom Domains", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "choreo:domain_view", "displayName": "choreo:domain_view", "description": "View Custom Domains", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "choreo:url_mapping_approve", "displayName": "choreo:url_mapping_approve", "description": "Approve URL Mappings", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "choreo:url_mapping_manage", "displayName": "choreo:url_mapping_manage", "description": "Manage URL Mappings", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "choreo:url_mapping_view", "displayName": "choreo:url_mapping_view", "description": "View URL Mappings", "bindings": [], "usageCount": null}, "shared": false}], "scopePrefix": "", "operations": [{"id": "", "target": "/domains", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/domains", "verb": "GET"}}}, {"id": "", "target": "/domains", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:domain_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/domains", "verb": "POST"}}}, {"id": "", "target": "/domains/{id}", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:domain_view", "choreo:domain_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/domains/{id}", "verb": "GET"}}}, {"id": "", "target": "/domains/{id}", "verb": "DELETE", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:domain_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/domains/{id}", "verb": "DELETE"}}}, {"id": "", "target": "/domains/{id}/certificate", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:domain_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/domains/{id}/certificate", "verb": "GET"}}}, {"id": "", "target": "/domains/{id}/certificate", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:domain_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/domains/{id}/certificate", "verb": "POST"}}}, {"id": "", "target": "/domains/{id}/tls", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:domain_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/domains/{id}/tls", "verb": "POST"}}}, {"id": "", "target": "/domains/validate", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:domain_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/domains/validate", "verb": "POST"}}}, {"id": "", "target": "/url-mappings", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:url_mapping_view", "choreo:url_mapping_manage", "choreo:url_mapping_approve"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/url-mappings", "verb": "GET"}}}, {"id": "", "target": "/url-mappings", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:url_mapping_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/url-mappings", "verb": "POST"}}}, {"id": "", "target": "/url-mappings/{id}", "verb": "DELETE", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:url_mapping_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/url-mappings/{id}", "verb": "DELETE"}}}, {"id": "", "target": "/url-mappings/{id}/load-balancer-configs", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:url_mapping_view", "choreo:url_mapping_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/url-mappings/{id}/load-balancer-configs", "verb": "GET"}}}, {"id": "", "target": "/url-mappings/{id}/load-balancer-configs", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:url_mapping_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/url-mappings/{id}/load-balancer-configs", "verb": "POST"}}}, {"id": "", "target": "/url-mappings/{id}/load-balancer-configs/{configId}", "verb": "DELETE", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:url_mapping_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/url-mappings/{id}/load-balancer-configs/{configId}", "verb": "DELETE"}}}, {"id": "", "target": "/url-mappings/deploy", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:url_mapping_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/url-mappings/deploy", "verb": "POST"}}}, {"id": "", "target": "/environments", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/environments", "verb": "GET"}}}, {"id": "", "target": "/load-balancer-configs", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "2599298f-b2ab-439e-9c58-0bf8fc25c8f7", "backendOperation": {"target": "/load-balancer-configs", "verb": "GET"}}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "d962d47d-f855-4202-be7b-3b31e391d1f4", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "08e3e40f-c17c-4274-b69d-99afc6796f2a", "versionId": "65e82d435e87d93a849b8943"}}