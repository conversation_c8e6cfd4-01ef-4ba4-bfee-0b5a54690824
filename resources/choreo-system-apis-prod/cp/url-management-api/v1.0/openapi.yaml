openapi: 3.0.1
info:
  title: URL Management API
  description: REST API for URL management in Choreo
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <EMAIL>
  version: v1.0
servers:
  - url: https://choreoapis.dev/93tu/url-mgt/v1.0
security:
  - default: []
tags:
  - name: Domains
    description: Domain related resources
  - name: URL Mappings
    description: URL Mapping related resources
  - name: Load Balancer Configs
    description: Load Balancer Config related resources
  - name: Common
    description: Common resources
  - name: Admins
    description: Secured Admin-only calls
  - name: Developers
    description: Operations available to developers
paths:
  /domains:
    get:
      tags:
        - Domains
        - Admins
        - Developers
      summary: Retrieve Domains
      description: "This operation provides you a list of available Domains.\nIf a type is specified (api, webapp, devportal), only returns the domains under that type. "
      operationId: getDomains
      parameters:
        - name: type
          in: query
          description: |
            Type of the domain (i.e api, webapp, devportal)
          required: false
          style: form
          explode: true
          schema:
            $ref: '#/components/schemas/Type'
        - name: name
          in: query
          description: |
            Name of the domain required.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: |
            OK.
            List of Domains is returned.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Domain'
        "400":
          $ref: '#/components/responses/BadRequest'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - Domains
        - Admins
      summary: Create new domain
      description: |
        This operation can be used to create a new Domain specifying the details of the Domain in the payload.
      operationId: postDomains
      requestBody:
        description: |
          Domain object that needs to be created
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Domain'
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created object as entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Domain'
        "400":
          $ref: '#/components/responses/BadRequest'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:domain_manage
        - default:
            - choreo:domain_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /domains/{id}:
    get:
      tags:
        - Domains
        - Admins
      summary: Get Details of a Domain
      description: |
        Using this operation, you can retrieve complete details of a single Domain. You need to provide the Id of the Domain to retrive it.
      parameters:
        - name: id
          in: path
          description: |
            ID of the requested domain
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            Requested Domain is returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Domain'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:domain_view
            - choreo:domain_manage
        - default:
            - choreo:domain_view
            - choreo:domain_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - Domains
        - Admins
      summary: Deletes a Domain
      description: |
        This operation can be used to delete an existing Domain proving the Id of the Domain.
      operationId: deleteDomainsId
      parameters:
        - name: id
          in: path
          description: |
            ID of the domain to be deleted
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:domain_manage
        - default:
            - choreo:domain_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /domains/{id}/certificate:
    get:
      tags:
        - Domains
        - Admins
      summary: Retrieves the certificate for a domain with Let's Encrypt TLS provider
      description: |
        This can be used to retrieve the certificate of a domain with Let's Encrypt TLS provider
      operationId: getDomainsIdCertificate
      parameters:
        - name: id
          in: path
          description: |
            ID of the domain whose certificate needs to be retrieved.
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: 943cecb9-f5c0-4c67-9fb6-f02de3af743f
      responses:
        "200":
          description: |
            Ok.
            Returns a JSON object containing the Base64 encoded TLS certificate, private key, and chain.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TLSCertificate'
              example:
                tls_cert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tXG5NSUlCSWpBTkJna3Foa2lHOXcuLi5cbi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0=
                tls_key: LS0tLS1CRUdJTiBSU0EgUFJJVkFURSBLRVktLS0tLVxuTUlJRXZRSUJBREFOQmdrcWhraS4uLlxuLS0tLS1FTkQgUlNBIFBSSVZBVEUgS0VZLS0tLS0=
                tls_chain: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tXG5NSUlEUVRDQ0FpbWdBd0lCQWdJLi4uXG4tLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0t
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:domain_manage
        - default:
            - choreo:domain_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - Domains
        - Admins
      summary: Update the certificate for a domain with custom TLS provider
      description: |
        This can be used to update the certificate of a domain with Custom TLS provider
      operationId: postDomainsIdCertificate
      parameters:
        - name: id
          in: path
          description: |
            ID of the domain the certificate needs to be switched.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: TLS Certificate.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TLSCertificate'
      responses:
        "200":
          description: |
            OK.
            Resource successfully updated.
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:domain_manage
        - default:
            - choreo:domain_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /domains/{id}/tls:
    post:
      tags:
        - Domains
        - Admins
      summary: Update the TLS provider of a Domain
      description: |
        This can be used to update the TLS provider between Let's Encrypt and Custom
      operationId: postDomainsIdTls
      parameters:
        - name: id
          in: path
          description: |
            ID of the domain the TLS provider needs to be switched.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: TLS Provider.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TLSProvider'
      responses:
        "200":
          description: |
            OK.
            Resource successfully updated.
          content: {}
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:domain_manage
        - default:
            - choreo:domain_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /domains/validate:
    post:
      tags:
        - Domains
        - Admins
      summary: Validate a Domain
      description: |
        This operation can be used to validate a domain. There are two validations done;
        1. If there is a domain already existing with the same name
        2. If the cname records are updated properly for the Domain to point to Choreo
      operationId: getDomainsValidate
      requestBody:
        description: |
          Domain Request that needs to be validated
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DomainValidationRequest'
      responses:
        "200":
          description: |
            Ok.
            Domain validation information is returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DomainValidationResponse'
        "400":
          $ref: '#/components/responses/BadRequest'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:domain_manage
        - default:
            - choreo:domain_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /url-mappings:
    get:
      tags:
        - URL Mappings
        - Admins
        - Developers
      summary: Retrieve URL Mappings
      description: |
        This operation provides you a list of available URL Mappings.
        The list can be filtered by domain, component or the status of the URL Mapping
      operationId: getURLMappings
      parameters:
        - name: domainId
          in: query
          description: |
            Domain ID for which the URL Mappings are requested.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: componentId
          in: query
          description: |
            Component ID for which the URL Mappings are requested.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: status
          in: query
          description: |
            Status of the URL Mappings to be filtered.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
        - name: apiId
          in: query
          description: |
            API ID for which the URL Mappings are requested.
          required: false
          style: form
          explode: true
          schema:
            type: string
            nullable: true
      responses:
        "200":
          description: |
            Ok.
            List of URL Mappings is returned.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/URLMapping'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:url_mapping_view
            - choreo:url_mapping_manage
            - choreo:url_mapping_approve
        - default:
            - choreo:url_mapping_view
            - choreo:url_mapping_manage
            - choreo:url_mapping_approve
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - URL Mappings
        - Developers
      summary: Create URL Mapping
      description: |
        This operation can be used to create a new URL Mapping specifying the details of the URL Mapping in the payload.
      operationId: postURLMappings
      requestBody:
        description: |
          URL Mapping object that needs to be created
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/URLMapping'
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created object as entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/URLMapping'
        "400":
          $ref: '#/components/responses/BadRequest'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:url_mapping_manage
        - default:
            - choreo:url_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /url-mappings/{id}:
    delete:
      tags:
        - URL Mappings
        - Developers
      summary: Deletes a URL Mapping
      description: |
        This operation can be used to delete an existing URL Mapping proving the Id of the URL Mapping.
      operationId: deleteURLMappingsId
      parameters:
        - name: id
          in: path
          description: |
            ID of the URL Mapping to be deleted
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            OK.
            Resource successfully deleted.
          content: {}
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:url_mapping_manage
        - default:
            - choreo:url_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /url-mappings/{id}/load-balancer-configs:
    get:
      tags:
        - Load Balancer Configs
        - Admins
        - Developers
      summary: Retrieve Load Balancer Configurations of a URL Mapping
      description: |
        This operation provides you a list of configured Load Balancer Configurations of a URL Mapping.
      operationId: getLoadBalancerConfigsOfURLMapping
      parameters:
        - name: id
          in: path
          description: |
            ID of the URL Mapping in which the Load Balancer Configurations are requested.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            Ok.
            List of Load Balancer Configurations is returned.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LoadBalancerConfigValue'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:url_mapping_manage
            - choreo:url_mapping_view
        - default:
            - choreo:url_mapping_manage
            - choreo:url_mapping_view
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - Load Balancer Configs
        - Admins
        - Developers
      summary: Add or Update Load Balancer Configurations to a URL Mapping
      description: |
        This operation can be used to add or update Load Balancer Configurations to a URL Mapping.
      operationId: postLoadBalancerConfigsToURLMapping
      parameters:
        - name: id
          in: path
          description: |
            ID of the URL Mapping in which the Load Balancer Configurations are requested.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: |
          Load Balancer Configuration Value list that needs to be created/updated
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/LoadBalancerConfigValue'
      responses:
        "200":
          description: |
            Ok.
            URL Mapping is updated with the Load Balancer Configurations
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:url_mapping_manage
        - default:
            - choreo:url_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /url-mappings/{id}/load-balancer-configs/{configId}:
    delete:
      tags:
        - Admins
        - Developers
        - Load Balancer Configs
      summary: Delete a Load Balancer Configuration of a URL Mapping
      description: |
        This operation can be used to delete a Load Balancer Configurations of a URL Mapping.
      operationId: deleteLoadBalancerConfigsOfAURLMapping
      parameters:
        - name: id
          in: path
          description: |
            ID of the URL Mapping in which the Load Balancer Configurations are requested.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: configId
          in: path
          description: |
            ID of the Load Balancer Configuration which needs to be updated
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: |
            Ok.
            URL Mapping is updated with the Load Balancer Configuration removed
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:url_mapping_manage
        - default:
            - choreo:url_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /environments:
    get:
      tags:
        - Common
        - Admins
        - Developers
      summary: Retrieve Environments
      description: |
        This operation can be used to get the list of environments under a particular organization.
      operationId: getEnvironments
      responses:
        "200":
          description: |
            OK.
            List of Environments is returned.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Environment'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /load-balancer-configs:
    get:
      tags:
        - Admins
        - Developers
        - Load Balancer Configs
      summary: Retrieve allowed Load Balancer Configurations
      description: |
        This operation can be used to get the list of Load Balancer Configurations allowed in Choreo.
      operationId: getLoadBalancerConfigurations
      responses:
        "200":
          description: |
            Ok.
            List of Load Balancer Configurations is returned.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LoadBalancerConfig'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /url-mappings/deploy:
    post:
      tags:
        - URL Mappings
        - Developers
      summary: Create and deploy URL Mapping
      description: |
        This operation can be used to create and deploy a URL Mapping in a single operation.
        The mapping will be automatically approved if workflow is not enabled.
      operationId: createAndDeployURLMapping
      requestBody:
        description: |
          URL Mapping object that needs to be created and deployed
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/URLMapping'
      responses:
        "201":
          description: |
            Created.
            Successful response with the newly created and deployed object as entity in the body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/URLMapping'
        "400":
          $ref: '#/components/responses/BadRequest'
        "403":
          $ref: '#/components/responses/Forbidden'
        "409":
          $ref: '#/components/responses/Conflict'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - OAuth2Security:
            - choreo:url_mapping_manage
        - default:
            - choreo:url_mapping_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    ErrorListItem:
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: string
          description: Error code (i.e 400, 404 etc)
        message:
          type: string
          description: |
            Description about individual errors occurred
        description:
          type: string
          description: |
            A detail description about the error message.
      description: |
        Description of individual errors that may have occurred during
         a request.
    Error:
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: integer
          description: Error code (i.e 400, 404 etc)
          format: int64
        message:
          type: string
          description: |
            Error message.
        description:
          type: string
          description: |
            A detail description about the error message.
        moreInfo:
          type: string
          description: |
            Preferably an url with more details about the error.
        error:
          type: array
          description: |-
            If there are more than one error list them out.
            For example, list out validation errors by each field.
          items:
            $ref: '#/components/schemas/ErrorListItem'
      description: |
        Error object returned with 4XX or 5XX HTTP Status codes
    TLSProvider:
      required:
        - name
      type: object
      properties:
        name:
          $ref: '#/components/schemas/TlsProviderName'
        certificate:
          $ref: '#/components/schemas/TLSCertificate'
        expirationDate:
          type: string
          description: |
            Expiration date of the certificate, if the type is set to Custom
          format: date
      description: |
        Represents a TLS Provider in Choreo (Let's Encrypt or Custom)
    TlsProviderName:
      type: string
      description: |
        Name of the TLS Provider
      enum:
        - custom
        - lets_encrypt
    DomainValidationRequest:
      required:
        - domain
        - type
      type: object
      properties:
        domain:
          type: string
          description: |
            Domain to be validated
        type:
          $ref: '#/components/schemas/Type'
        environmentId:
          type: string
          description: |
            ID of the Environment, in which the domain is intended to be used. This is optional for devportal type.
          readOnly: true
      description: |
        Represents a Domain Validation Request in Choreo
    DomainValidationResponse:
      required:
        - isValid
      type: object
      properties:
        isValid:
          type: boolean
          description: |
            Status of the Domain Validation
        message:
          type: string
          description: |
            A message indicating the validation status.
      description: "Represents a Domain Validation Response in Choreo     \n"
    TLSCertificate:
      required:
        - tls_cert
        - tls_key
      type: object
      properties:
        tls_cert:
          type: string
          description: |
            TLS Certificate
        tls_key:
          type: string
          description: |
            TLS Key
        tls_chain:
          type: string
          description: |
            TLS Chain
      description: Represents a TLS Certificate
    Type:
      type: string
      description: |
        Type of the domain (i.e api, webapp, devportal)
      enum:
        - devportal
        - webapp
        - api
    Domain:
      required:
        - name
        - tlsProvider
        - type
      type: object
      properties:
        id:
          type: string
          description: |
            ID of the Custom Domain
          readOnly: true
        orgId:
          type: string
          description: |
            ID of the associated organization
          readOnly: true
        name:
          type: string
          description: |
            Custom Domain
          example: apis.example.com
        type:
          $ref: '#/components/schemas/Type'
        environment:
          $ref: '#/components/schemas/Environment'
        tlsProvider:
          $ref: '#/components/schemas/TLSProvider'
      description: "Represents a Custom Domain in Choreo    \n"
    Environment:
      required:
        - id
      type: object
      properties:
        id:
          type: string
          description: |
            ID of the Environment
        name:
          type: string
          description: |
            Name of the Environment
        region:
          type: string
          description: "Region of the Environment         \n"
        defaultDomain:
          type: string
          description: |
            Default Domain of the Environment
        apiCnameTarget:
          type: string
          description: |
            Target Domain of the Environment, to which user should add the CNAME records for APIs
        webappCnameTarget:
          type: string
          description: |
            Target Domain of the Environment, to which user should add the CNAME records for Webapps
        clusterId:
          type: string
          description: |
            The clusterId associated with the environment
      description: "Represents an Environment in Choreo (i.e This maps to the Environment Template in Choreo).      \n"
    URLMapping:
      required:
        - componentId
        - customPath
        - defaultDomain
        - defaultPath
        - domainId
      type: object
      properties:
        id:
          type: string
          description: |
            Auto generated ID for the URL Mapping
          readOnly: true
        domainId:
          type: string
          description: |
            ID of the domain in which this URL Mapping is being applied
        defaultPath:
          type: string
          description: |
            Default URL of the component for the particular environment
          example: /mesb/leaveapi/v1
        customPath:
          type: string
          description: |
            Default URL of the component for the particular environment
          example: /leave/$version
        defaultDomain:
          type: string
          description: |
            Default Domain of the the component for the particular environment
        componentId:
          type: string
          description: |
            ID of the component to which this URL mapping will be added
        apiId:
          type: string
          description: "ID of the API to which this URL mapping will be configured. For each endpoint in Choreo, there will be an API created     \n"
        environment:
          $ref: '#/components/schemas/Environment'
        status:
          type: string
          description: |
            Approval Status of the URL Mapping. Possible values are `pending`, `approved` and `rejected`
          example: pending
          enum:
            - pending
            - approved
            - rejected
        createdBy:
          type: string
          description: |
            ID of the User who created the URL Mapping
      description: |
        Represents a URL Mapping with basic infomation
    LoadBalancerConfig:
      required:
        - default
        - displayName
        - key
      type: object
      properties:
        key:
          type: string
          description: |
            Unique Name of the Load Balancer Configuration
        displayName:
          type: string
          description: |
            Display Name of the Load Balancer Configuration
        default:
          type: string
          description: |
            Default value of the Load Balancer Configuration
        min:
          type: string
          description: |
            Minimum value of the Load Balancer Configuration
        max:
          type: string
          description: |
            Maximum value of the Load Balancer Configuration
      description: |
        Represents an allowed Load Balancer Configuration in Choreo
    LoadBalancerConfigValue:
      required:
        - key
        - value
      type: object
      properties:
        id:
          type: string
          description: |
            ID of the Load Balancer Configuration Value
        key:
          type: string
          description: |
            Unique Name of the Load Balancer Configuration
        value:
          type: string
          description: |
            Value of the Load Balancer Configuration
      description: |
        Represents a Load Balancer Configuration Value in Choreo
  responses:
    BadRequest:
      description: |
        Bad Request. Invalid request or validation error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Bad Request
            description: Invalid request or validation error
            moreInfo: ""
            error: []
    Conflict:
      description: |
        Conflict. Specified resource already exists.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 409
            message: Conflict
            description: Specified resource already exists
            moreInfo: ""
            error: []
    Forbidden:
      description: |
        Forbidden. The request must be conditional but no condition has
        been specified.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 403
            message: Forbidden
            description: The request must be conditional but no condition has been specified
            moreInfo: ""
            error: []
    NotFound:
      description: |
        Not Found. The specified resource does not exist.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 404
            message: Not Found
            description: The specified resource does not exist
            moreInfo: ""
            error: []
    Unauthorized:
      description: |
        Unauthorized. The user is not authorized.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 401
            message: Unauthorized
            description: The user is not authorized
            moreInfo: ""
            error: []
    InternalServerError:
      description: |
        Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 500
            message: Internal Server Error
            description: The server encountered an internal error. Please contact administrator.
            moreInfo: ""
            error: []
  securitySchemes:
    OAuth2Security:
      type: oauth2
      flows:
        password:
          tokenUrl: https://sts.preview-dv.choreo.dev/oauth2/token
          scopes:
            choreo:domain_view: View Custom Domains
            choreo:domain_manage: Manage Custom Domains
            choreo:url_mapping_view: View URL Mappings
            choreo:url_mapping_approve: Approve URL Mappings
            choreo:url_mapping_manage: Manage URL Mappings
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            choreo:url_mapping_manage: Manage URL Mappings
            choreo:domain_view: View Custom Domains
            choreo:domain_manage: Manage Custom Domains
            choreo:url_mapping_view: View URL Mappings
            choreo:url_mapping_approve: Approve URL Mappings
          x-scopes-bindings:
            choreo:domain_view: ""
            choreo:domain_manage: ""
            choreo:url_mapping_manage: ""
            choreo:url_mapping_view: ""
            choreo:url_mapping_approve: ""
x-wso2-disable-security: false
x-business-owner:
  name: Sumedha Kodithuwakku
  email: <EMAIL>
x-technical-owner:
  name: Sumedha Kodithuwakku
  email: <EMAIL>
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://choreo-url-management.prod-choreo-system:9090/url-mgt
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-url-management.prod-choreo-system:9090/url-mgt
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/url-mgt/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
