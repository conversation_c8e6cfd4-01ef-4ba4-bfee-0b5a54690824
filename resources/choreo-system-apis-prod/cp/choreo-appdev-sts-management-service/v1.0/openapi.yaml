openapi: 3.0.3
info:
  title: Choreo Appdev STS Management Service
  description: API for the Choreo Appdev STS Management Service.
  contact: {}
  version: v1.0
servers:
  - url: https://app.choreo.dev/93tu/choreo-appdev-sts-management-service/v1.0
security:
  - default: []
paths:
  /sts-proxy/oauth-applications/{clientId}:
    get:
      summary: Retrieve OAuth application registration
      description: This endpoint retrieves the OAuth application registration details for the provided client ID.
      parameters:
        - name: clientId
          in: path
          description: The client ID of the OAuth application to be retrieved.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: organizationId
          in: query
          description: Organization ID for the request.
          required: true
          style: form
          explode: false
          schema:
            type: string
        - name: environmentId
          in: query
          description: Environment ID for the request.
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Application retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  clientId:
                    type: string
                    example: 6D59z2c0209UIx9WO7yL1ApTYHIa
                  clientName:
                    type: string
                    example: Example OAuth App
                  grantTypes:
                    type: array
                    example:
                      - client_credentials
                      - authorization_code
                    items:
                      type: string
                  callbackUrls:
                    type: array
                    example:
                      - https://example.com/callback
                    items:
                      type: string
                  userTokenExpiry:
                    type: integer
                    example: 86400
                  appTokenExpiry:
                    type: integer
                    example: 1440
                  refreshTokenExpiry:
                    type: integer
                    example: 604800
                  publicClient:
                    type: boolean
                    example: false
                  pkceMandatory:
                    type: boolean
                    example: false
        "404":
          description: Not Found - Application with the given clientId not found
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      summary: Update OAuth application registration
      description: This endpoint updates the OAuth application registration with the provided client ID.
      parameters:
        - name: clientId
          in: path
          description: The client ID of the OAuth application to be updated.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: X-Organization-Id
          in: header
          description: Organization ID for the request.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: X-Environment-Id
          in: header
          description: Environment ID for the request.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                grantTypes:
                  type: array
                  description: OAuth grant types supported by the application.
                  example:
                    - client_credentials
                    - authorization_code
                    - refresh_token
                  items:
                    type: string
                callbackUrls:
                  type: array
                  description: List of callback URLs for the OAuth application.
                  example:
                    - https://hostname/auth/login/callback
                  items:
                    type: string
                userTokenExpiry:
                  type: integer
                  description: Expiry time for user access tokens in seconds.
                  example: 86400
                appTokenExpiry:
                  type: integer
                  description: Expiry time for application access tokens in seconds.
                  example: 1440
                refreshTokenExpiry:
                  type: integer
                  description: Expiry time for refresh tokens in seconds.
                  example: 604800
                publicClient:
                  type: boolean
                  description: Whether the application is a public client.
                  example: false
                pkceMandatory:
                  type: boolean
                  description: Whether PKCE (Proof Key for Code Exchange) is mandatory.
                  example: false
        required: true
      responses:
        "200":
          description: Application updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  clientId:
                    type: string
                    example: 6D59z2c0209UIx9WO7yL1ApTYHIa
        "400":
          description: Bad Request - Invalid data provided
        "404":
          description: Not Found - Application with the given clientId not found
        "500":
          description: Internal Server Error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /sts-mgt/notification/revoke:
    post:
      summary: Revoke OAuth2 tokens
      operationId: revokeToken
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                accessToken:
                  type: string
                  example: ntjLVu4hymelMbJ3Z2VKOqS5C8ps1zKK.DFH3ur6L7tRtl1fuHuux36BjNfC2Y7Y1.w6xRRcLElFCV12XH27bMbwJemMCFLfUV
      responses:
        "200":
          description: No Content.
          content: {}
        "400":
          description: Invalid input request.
          content:
            application/json:
              schema:
                required:
                  - code
                  - message
                type: object
                properties:
                  code:
                    type: string
                    example: AAA-00000
                  message:
                    type: string
                    example: Some Error Message
                  description:
                    type: string
                    example: Some Error Description
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                required:
                  - code
                  - message
                type: object
                properties:
                  code:
                    type: string
                    example: AAA-00000
                  message:
                    type: string
                    example: Some Error Message
                  description:
                    type: string
                    example: Some Error Description
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                required:
                  - code
                  - message
                type: object
                properties:
                  code:
                    type: string
                    example: AAA-00000
                  message:
                    type: string
                    example: Some Error Message
                  description:
                    type: string
                    example: Some Error Description
        "500":
          description: Internal Server Error.
          content:
            application/json:
              schema:
                required:
                  - code
                  - message
                type: object
                properties:
                  code:
                    type: string
                    example: AAA-00000
                  message:
                    type: string
                    example: Some Error Message
                  description:
                    type: string
                    example: Some Error Description
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://sts-mgt-service.prod-choreo-system.svc.cluster.local:3000/api/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://sts-mgt-service.prod-choreo-system.svc.cluster.local:3000/api/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/choreo-appdev-sts-management-service/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
