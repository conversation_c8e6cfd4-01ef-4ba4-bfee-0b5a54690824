{"id": "65448ed2be658723011579a7", "name": "APIM AppDev APIs", "displayName": "APIM AppDev APIs", "description": "This api is used to connect to the APIM AppDev APIs service", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/apim-appdev", "version": "v1.0", "provider": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1698991826606", "lastUpdatedTime": "2025-01-16 09:50:31.756", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1"}, "production_endpoints": {"url": "http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1"}}, "endpointImplementationType": "ENDPOINT", "scopes": [{"scope": {"id": null, "name": "choreo:component_manage", "displayName": "choreo:component_manage", "description": "Manage component", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "choreo:project_manage", "displayName": "choreo:project_manage", "description": "Manage project", "bindings": [], "usageCount": null}, "shared": false}], "scopePrefix": "", "operations": [{"id": "", "target": "/projects/{projectId}/consumable-scopes", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:project_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/sts/oauth-applications/{id}", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/sts/oauth-applications/{id}", "verb": "PUT", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["choreo:component_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": "http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1", "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "89c19b60-6bdc-4893-becd-ab7a6eff29b2", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "5943e08b-2e60-4a6b-b624-d52ce674ff53", "versionId": "65448ed2be658723011579a7"}}