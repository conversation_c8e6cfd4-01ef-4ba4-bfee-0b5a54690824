openapi: 3.0.1
info:
  title: APIM AppDev APIs
  description: ""
  contact: {}
  version: v1.0
servers:
  - url: https://apis.wso2.com/93tu/apim-appdev/v1.0
paths:
  /projects/{projectId}/consumable-scopes:
    get:
      tags:
        - Scopes
      summary: Get all available API scopes of a given project
      description: |
        This operation can be used to get all the available API Scopes of a given project.
      operationId: getProjectScopes
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/componentId'
      responses:
        "200":
          description: |
            OK.
            APIs Scope list is returned.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectScopesList'
        "400":
          $ref: '#/components/responses/BadRequest'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /sts/oauth-applications/{id}:
    get:
      tags:
        - OAtuth Applications
      summary: |
        Retrieve OAuth application
      description: |
        This operation can be used to retrieve an OAuth application by the ID
      operationId: getOAuthApplication
      parameters:
        - $ref: '#/components/parameters/oauthAppId'
      responses:
        "200":
          description: |
            OK.
            Subscription returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthApp'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - choreo:component_manage
        - default:
            - choreo:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - OAtuth Applications
      summary: Update an OAuth application
      operationId: updateOAuthApplication
      parameters:
        - $ref: '#/components/parameters/oauthAppId'
      requestBody:
        description: |
          OAuth application request object
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OAuthApp'
        required: true
      responses:
        "200":
          description: |
            OK.
            OAuth application updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthApp'
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
      security:
        - OAuth2Security:
            - choreo:component_manage
        - default:
            - choreo:component_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    Error:
      title: Error object returned with 4XX HTTP status
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: integer
          format: int64
        message:
          type: string
          description: Error message.
        description:
          type: string
          description: |
            A detail description about the error message.
        moreInfo:
          type: string
          description: |
            Preferably an url with more details about the error.
        error:
          type: array
          description: |
            If there are more than one error list them out.
            For example, list out validation errors by each field.
          items:
            $ref: '#/components/schemas/ErrorListItem'
    ErrorListItem:
      title: Description of individual errors that may have occurred during a request.
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: string
        message:
          type: string
          description: |
            Description about individual errors occurred
    Pagination:
      title: Pagination
      type: object
      properties:
        offset:
          type: integer
          example: 0
        limit:
          type: integer
          example: 10
        total:
          type: integer
          example: 1
        next:
          type: string
          description: |
            Link to the next subset of resources qualified.
            Empty if no more resources are to be returned.
          example: ""
        previous:
          type: string
          description: |
            Link to the previous subset of resources qualified.
            Empty if current subset is the first subset returned.
          example: ""
    Scope:
      title: Scope
      required:
        - name
      type: object
      properties:
        id:
          type: string
          description: |
            UUID of the Scope.
          readOnly: true
          example: 01234567-0123-0123-0123-012345678901
        name:
          maxLength: 255
          minLength: 1
          type: string
          description: |
            name of Scope
          example: apim:api_view
        displayName:
          maxLength: 255
          type: string
          description: |
            display name of Scope
          example: api_view
        description:
          maxLength: 512
          type: string
          description: |
            description of Scope
          example: This Scope can used to view Apis
    APIScopes:
      title: List of scopes exposed by a version if a API.
      type: object
      properties:
        apiId:
          type: string
          description: |
            API Version ID
          example: 01234567-0123-0123-0123-012345678901
        apiName:
          type: string
          description: |
            API Name
          example: TestAPI
        version:
          type: string
          description: |
            API Version
          example: 1.0.0
        apiVersionId:
          type: string
          description: |
            API Version ID
          example: 01234567-0123-0123-0123-012345678901
        scopes:
          type: array
          description: |
            List of scopes exposed by a version of a API.
          items:
            $ref: '#/components/schemas/Scope'
    ProjectScopesList:
      title: List of API scopes used by components.
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        list:
          type: array
          items:
            $ref: '#/components/schemas/APIScopes'
    OAuthApp:
      title: OAuth application details
      type: object
      properties:
        clientId:
          type: string
          description: Client ID of the OAuth application
          readOnly: true
          example: vYDoc9s7IgAFdkSyNDaswBX7ejoa
        grantTypes:
          type: array
          description: The grant types that are supported by the application
          example:
            - client_credentials
            - password
          items:
            type: string
        callbackUrls:
          type: array
          description: Callback URL
          example:
            - http://sample.com/callback/url
          items:
            type: string
        userTokenExpiry:
          type: integer
          description: Expiry time of user access tokens in seconds
          format: int64
          example: 7200
          default: 3600
        appTokenExpiry:
          type: integer
          description: Expiry time of application access tokens in seconds
          format: int64
          example: 3600
          default: 3600
        refreshTokenExpiry:
          type: integer
          description: Expiry time of refresh tokens in seconds
          format: int64
          example: 86400
          default: 86400
        publicClient:
          type: boolean
          description: Is the application a public client.
          example: true
          default: false
        pkceMandatory:
          type: boolean
          description: Is the application a public client.
          example: false
          default: false
  responses:
    BadRequest:
      description: Bad Request. Invalid request or validation error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Bad Request
            description: Invalid request or validation error
            moreInfo: ""
            error: []
    Conflict:
      description: Conflict. Specified resource already exists.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 409
            message: Conflict
            description: Specified resource already exists
            moreInfo: ""
            error: []
    InternalServerError:
      description: Internal Server Error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 500
            message: Internal Server Error
            description: The server encountered an internal error. Please contact administrator.
            moreInfo: ""
            error: []
    NotAcceptable:
      description: Not Acceptable. The requested media type is not supported.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 406
            message: Not Acceptable
            description: The requested media type is not supported
            moreInfo: ""
            error: []
    NotFound:
      description: Not Found. The specified resource does not exist.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 404
            message: Not Found
            description: The specified resource does not exist
            moreInfo: ""
            error: []
    PreconditionFailed:
      description: Precondition Failed. The request has not been performed because one of the preconditions is not met.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 412
            message: Precondition Failed
            description: The request has not been performed because one of the preconditions is not met
            moreInfo: ""
            error: []
    Unauthorized:
      description: Unauthorized. The user is not authorized.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 401
            message: Unauthorized
            description: The user is not authorized
            moreInfo: ""
            error: []
    Forbidden:
      description: Forbidden. Insufficient permissions.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 403
            message: Forbidden
            description: Insufficient permissions
            moreInfo: ""
            error: []
    UnsupportedMediaType:
      description: Unsupported Media Type. The entity of the request was not in a supported format.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 415
            message: Unsupported media type
            description: The entity of the request was not in a supported format
            moreInfo: ""
            error: []
  parameters:
    applicationId:
      name: applicationId
      in: path
      description: |
        Application Identifier consisting of the UUID of the Application.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    oauthAppId:
      name: id
      in: path
      description: |
        OAuth Application ID.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    keyId:
      name: keyId
      in: path
      description: |
        The Identifier for the Key
      required: true
      style: simple
      explode: false
      schema:
        type: string
      example: 174bf4dd-39e6-4501-afaf-f5184067cdb4
    subscriptionId:
      name: subscriptionId
      in: path
      description: |
        Subscription Id
      required: true
      style: simple
      explode: false
      schema:
        type: string
    If-None-Match:
      name: If-None-Match
      in: header
      description: |
        Validator for conditional requests; based on the ETag of the formerly retrieved
        variant of the resourec.
      required: false
      style: simple
      explode: false
      schema:
        type: string
    apiId-Q:
      name: apiId
      in: query
      description: |
        **API ID** consisting of the **UUID** of the API.
      required: false
      style: form
      explode: true
      schema:
        type: string
    applicationId-Q:
      name: applicationId
      in: query
      description: |
        **Application Identifier** consisting of the UUID of the Application.
      required: false
      style: form
      explode: true
      schema:
        type: string
    limit:
      name: limit
      in: query
      description: |
        Maximum size of resource array to return.
      required: false
      style: form
      explode: true
      schema:
        type: integer
        default: 25
    offset:
      name: offset
      in: query
      description: |
        Starting point within the complete list of items qualified.
      required: false
      style: form
      explode: true
      schema:
        type: integer
        default: 0
    If-Match:
      name: If-Match
      in: header
      description: |
        Validator for conditional requests; based on ETag.
      required: false
      style: simple
      explode: false
      schema:
        type: string
    groupId:
      name: groupId
      in: query
      description: |
        Application Group Id
      required: false
      style: form
      explode: true
      schema:
        type: string
    requestedTenant:
      name: X-WSO2-Tenant
      in: header
      description: |
        For cross-tenant invocations, this is used to specify the tenant domain, where the resource need to be
          retrieved from.
      required: false
      style: simple
      explode: false
      schema:
        type: string
    includeFromVersionRange:
      name: includeFromVersionRange
      in: query
      description: Boolean value to include subscriptions from the version range
      required: false
      style: form
      explode: true
      schema:
        type: boolean
        default: false
    projectId:
      name: projectId
      in: path
      description: |
        **Project ID** consisting of the **UUID** of the project.
      required: true
      style: simple
      explode: false
      schema:
        type: string
    componentId:
      name: componentId
      in: query
      description: |
        **Component ID** consisting the **UUID** of the component.
      required: false
      style: form
      explode: true
      schema:
        type: string
  securitySchemes:
    OAuth2Security:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            choreo:component_manage: Manage component
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            choreo:component_manage: Manage component
          x-scopes-bindings:
            choreo:component_manage: ""
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://choreo-am-service.prod-choreo-apim.svc.cluster.local:9763/api/am/choreo-console/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/apim-appdev/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
