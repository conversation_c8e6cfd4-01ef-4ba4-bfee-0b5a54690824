{"data": {"component": {"id": "5943e08b-2e60-4a6b-b624-d52ce674ff53", "name": "apim-appdev-apis", "handler": "akstpd", "description": " ", "displayType": "proxy", "displayName": "APIM AppDev APIs", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2023-11-03T06:10:27.303Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "APIM AppDev APIs", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/apim-appdev", "proxyId": "65448ed2be658723011579a7", "id": "65448ed2be658723011579a7", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "65448ed2be658723011579a7", "createdAt": "1698991826606", "updatedAt": "2023-11-03 06:36:33.091", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "5943e08b-2e60-4a6b-b624-d52ce674ff53", "latest": true, "versionStrategy": ""}]}}}