openapi: 3.0.1
info:
  title: PDP Uptime Checks API
  version: 1.0.0
servers:
  - url: /api/v1
security:
  - default: []
paths:
  /delay/{duration}:
    post:
      parameters:
        - name: duration
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /dataplanes/{dataplaneId}/uptime/internal-gw-api-invoke/{id}:
    post:
      parameters:
        - name: dataplaneId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
        - name: id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /dataplanes/{dataplaneId}/uptime/internal-gw-api-deploy/{id}:
    post:
      parameters:
        - name: dataplaneId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
        - name: id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /dataplanes/{dataplaneId}/uptime/external-gw-api-invoke/{id}:
    post:
      parameters:
        - name: dataplaneId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
        - name: id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /dataplanes/{dataplaneId}/uptime/external-gw-api-deploy/{id}:
    post:
      parameters:
        - name: dataplaneId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
        - name: id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /dataplanes/{dataplaneId}/uptime/web-app-creation-invocation/{id}:
    get:
      parameters:
        - name: dataplaneId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
        - name: id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /dataplanes/{dataplaneId}/uptime/secret-store-health/{id}:
    get:
      parameters:
        - name: dataplaneId
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
        - name: id
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
            format: string
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /dataplanes/{dataplaneId}/uptime/dp-connection-check/{id}:
    get:
      description: Check the connection to the Dataplane
      parameters:
        - name: dataplaneId
          in: path
          description: |
            Dataplane Id
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: id
          in: path
          description: |
            Id
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Connection to the Dataplane
          content:
            application/json:
              schema:
                title: Custom template details
                type: object
                properties:
                  templateId:
                    type: string
                  description:
                    type: string
                  organization:
                    type: string
                  environment:
                    type: string
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    CustomTemplateDetails:
      title: Custom template details
      type: object
      properties:
        templateId:
          type: string
        description:
          type: string
        organization:
          type: string
        environment:
          type: string
    MonitorSetupResponse:
      title: Monitor setup response
      type: object
      properties:
        message:
          type: string
    UptimeMonitoringResponse:
      required:
        - checks
        - monitorStatus
      type: object
      properties:
        checks:
          type: array
          items:
            required:
              - check
              - status
            type: object
            properties:
              check:
                type: string
                description: Represents different types of uptime checks.
                enum:
                  - Connection to the Dataplane
              status:
                type: string
                description: Represents the status of a check.
                enum:
                  - Unreachable
                  - Reachable
            description: Represents a single check within the uptime monitoring response.
        monitorStatus:
          type: string
          description: Represents the status of the monitor.
          enum:
            - Success
            - Trouble
            - Down
      description: Represents the response for an uptime monitoring request.
    MonitorStatusType:
      type: string
      description: Represents the status of the monitor.
      enum:
        - Success
        - Trouble
        - Down
    Check:
      required:
        - check
        - status
      type: object
      properties:
        check:
          type: string
          description: Represents different types of uptime checks.
          enum:
            - Connection to the Dataplane
        status:
          type: string
          description: Represents the status of a check.
          enum:
            - Unreachable
            - Reachable
      description: Represents a single check within the uptime monitoring response.
    StatusType:
      type: string
      description: Represents the status of a check.
      enum:
        - Unreachable
        - Reachable
    UptimeCheck:
      type: string
      description: Represents different types of uptime checks.
      enum:
        - Connection to the Dataplane
  parameters:
    dataplaneId:
      name: dataplaneId
      in: path
      description: |
        Dataplane Id
      required: true
      style: simple
      explode: false
      schema:
        type: string
    id:
      name: id
      in: path
      description: |
        Id
      required: true
      style: simple
      explode: false
      schema:
        type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://pdp-manager.prod-choreo-system:80
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://pdp-manager.prod-choreo-system:80
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/pdp-uptime-checks/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
