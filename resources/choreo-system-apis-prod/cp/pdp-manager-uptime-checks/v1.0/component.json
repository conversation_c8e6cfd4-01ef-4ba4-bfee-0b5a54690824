{"data": {"component": {"id": "f40f7b53-95e6-4b01-a80b-c424d7c22f38", "name": "PDP Uptime Checks API", "handler": "mjmfia", "description": " ", "displayType": "proxy", "displayName": "PDP Uptime Checks API", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-05-07T10:05:22.506Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "PDP Uptime Checks API", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/pdp-uptime-checks", "proxyId": "6639fce00094fd5dadd63db5", "id": "6639fce00094fd5dadd63db5", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "6639fce00094fd5dadd63db5", "createdAt": "1715076320423", "updatedAt": "2024-05-07 10:05:20.423", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "f40f7b53-95e6-4b01-a80b-c424d7c22f38", "latest": true, "versionStrategy": ""}]}}}