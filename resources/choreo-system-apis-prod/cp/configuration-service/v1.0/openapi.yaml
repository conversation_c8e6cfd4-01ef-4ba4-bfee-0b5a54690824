openapi: 3.0.0
info:
  title: Configuration Service
  contact: {}
  version: v1.0
servers:
  - url: https://api.choreo.dev/93tu/config-svc/v1.0
    description: Production Environment
security:
  - default: []
paths:
  /configs/groups:
    get:
      summary: Get configuration groups.
      description: |
        Retrieve a list of configuration groups. This endpoint allows you to obtain configuration groups based on specified criteria. You can filter groups by project Id, component Id, and deployment track Id using the query parameters. It returns a list of configuration groups that match the provided filter criteria.
      parameters:
        - name: nested_search
          in: query
          description: |
            (Optional) When this is set to true, the API will search for configuration groups that match the specified criteria based on an OR condition for organizationId, projectId, or componentId parameters. This endpoint retrieves a list of configuration groups that meet the search criteria. Default is false.
          required: false
          style: form
          explode: true
          schema:
            type: boolean
        - name: type
          in: query
          description: |
            (Optional) When set to "user", the API will retrieve configuration groups created by users themselves. If set to "internal_user", the API will look for groups created for users by the Choreo system (created by Choreo system for its users). Choreo system components will manage these configuration groups. When this is set to "system", the API will search for configuration groups that were created by the Choreo system for internal use. This endpoint provides a list of configuration groups that fit the search criteria. You can specify multiple values, separated by commas and encoded in the URI. The default type is "user".
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: projectId
          in: query
          description: (Optional) The UUID of the project to filter the results.
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: componentId
          in: query
          description: (Optional) The UUID of the component to filter the results.
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: deploymentTrackId
          in: query
          description: (Optional) The UUID of the deployment track to filter the results.
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Configuration groups retrieved successfully.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigurationGroup'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Create a new configuration group.
      description: |
        Create a new configuration group. Configuration groups are used to organize and manage related configurations. When initiating a configuration group, ensure to include the source of group creation, denoted as the type of the configuration group, within the request body. The default type is set to "user".
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationGroup'
        required: true
      responses:
        "201":
          description: Configuration group and related entities created successfully.
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/ConfigurationGroup'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configs/groups/{groupId}:
    get:
      summary: Get a configuration group.
      description: |
        Retrieve a configuration group.
      parameters:
        - name: groupId
          in: path
          description: The UUID of the configuration group.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Configuration group retrieved successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationGroup'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      summary: Edit an existing configuration group.
      description: |
        Edit an existing configuration group, allowing you to modify its properties. You cannot modify its configuration group type.
      parameters:
        - name: groupId
          in: path
          description: The UUID of the configuration group.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationGroup'
        required: true
      responses:
        "200":
          description: Configuration group and related entities edited successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationGroup'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Edit an existing configuration group (Deprecated).
      description: |
        Edit an existing configuration group, allowing you to modify its properties. You cannot modify its configuration group type.
      parameters:
        - name: groupId
          in: path
          description: The UUID of the configuration group.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationGroup'
        required: true
      responses:
        "204":
          description: Configuration group and related entities edited successfully.
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Delete a configuration group if it has not been used.
      description: |
        Delete a configuration group if it has no associated configurations.
      parameters:
        - name: groupId
          in: path
          description: The UUID of the configuration group.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Configuration group deleted successfully.
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configs/groups/check-group-name:
    get:
      summary: Check configuration group name availability.
      description: |
        Check the availability of a configuration group name. This endpoint allows you to verify whether a configuration group name is available for use.
      parameters:
        - name: candidateGroupName
          in: query
          description: The candidate group name to check for availability.
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfigurationGroupNameAvailabilityResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /configs/groups/{groupId}/resolve-secrets:
    post:
      summary: Resolve secrets for a configuration group.
      operationId: postConfigGroupResolveSecrets
      parameters:
        - name: groupId
          in: path
          description: The UUID of the configuration group.
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        description: Resolve secrets request payload.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfigurationGroupResolveSecretsRequest'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResolveSecretsResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorPayload'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    ErrorPayload:
      type: object
      properties:
        timestamp:
          type: string
          description: Timestamp when the error occurred.
        status:
          type: integer
          description: Relevant HTTP status code.
          format: int32
        reason:
          type: string
          description: Reason phrase.
        message:
          type: string
          description: Error message including status, and exact reason.
        path:
          type: string
          description: Request path.
        method:
          type: string
          description: Method type of the request.
    Value:
      required:
        - environmentUuid
        - value
      type: object
      properties:
        value:
          type: string
          description: The value for the configuration key.
        valueRef:
          type: string
          description: The KV reference for the configuration key if it is sensitive.
          readOnly: true
        environmentUuid:
          type: string
          description: The template UUID of the environment to which the configuration value is associated.
    ConfigurationGroupNameAvailabilityResponse:
      required:
        - isGroupNameUnique
      type: object
      properties:
        isGroupNameUnique:
          type: boolean
          description: Indicates whether the configuration group name is available.
        alternativeGroupName:
          type: string
          description: Suggest an alternative name if the name is not unique.
      example:
        isGroupNameUnique: false
        alternativeGroupName: sample-config-group-name
    Configuration:
      required:
        - isFile
        - isSensitive
        - key
        - values
      type: object
      properties:
        keyUuid:
          type: string
          description: The UUID of the configuration key.
          readOnly: true
        key:
          type: string
          description: The name of the configuration key.
        values:
          type: array
          items:
            $ref: '#/components/schemas/Value'
        isSensitive:
          type: boolean
          description: Indicates whether the value contains sensitive data.
        isFile:
          type: boolean
          description: Indicates whether the configuration is a file.
    ConfigurationScope:
      type: object
      properties:
        projectUuid:
          type: string
          description: The UUID of the project to which the configuration key is associated.
        componentUuid:
          type: string
          description: The UUID of the component to which the configuration key is associated.
        deploymentTrackUuid:
          type: string
          description: The UUID of the deployment track to which the configuration key is associated.
    EnvironmentSet:
      required:
        - environmentTemplates
      type: object
      properties:
        environmentSetUuid:
          type: string
          description: The UUID of the environment set.
        environmentTemplates:
          type: array
          description: Array of environment templates grouped under the environment set.
          items:
            type: string
      example:
        environmentSetUuid: 123e4567-e89b-12d3-a456-************
        environmentTemplates:
          - bc26521c-e0b8-4873-aad6-a42ee66ac5a9
          - 02ed7014-2ebc-4b05-8f1d-5de1c376487a
          - d2e4e46d-eea1-4bb2-b09b-8eb5d534d749
    ConfigurationGroup:
      required:
        - configurations
        - groupName
        - scopes
      type: object
      properties:
        groupUuid:
          type: string
          description: The configuration group UUID (read-only).
          readOnly: true
        groupName:
          type: string
          description: The name of the configuration group.
        groupDisplayName:
          type: string
          description: The display name of the configuration group.
        description:
          type: string
          description: A description of the configuration group.
        properties:
          type: object
          additionalProperties:
            type: string
        scope:
          type: array
          items:
            $ref: '#/components/schemas/ConfigurationScope'
        configurations:
          type: array
          items:
            $ref: '#/components/schemas/Configuration'
        createdAt:
          type: string
          description: Indicating when the configuration group was created.
          readOnly: true
        lastUpdatedAt:
          type: string
          description: Indicating when the configuration group was last updated.
          readOnly: true
        type:
          type: string
          description: This attribute represents the type of the configuration group. It indicates the creator of the configuration group. If it was created by a user, the type is "user". If it was created by internal systems, the type is "internal_user". Otherwise, it is categorized as "system".
          enum:
            - user
            - internal_user
            - system
        environmentSets:
          type: array
          description: List of environment sets associated with the configuration group.
          items:
            $ref: '#/components/schemas/EnvironmentSet'
        marketplaceResourceUuid:
          type: string
          description: Marketplace resource ID of the configuration group.
          nullable: true
    ConfigurationGroupResolveSecretsRequest:
      required:
        - envTemplateId
        - secrets
      type: object
      properties:
        envTemplateId:
          type: string
          description: The UUID of the environment template.
          example: 123e4567-e89b-12d3-a456-************
        secrets:
          type: array
          description: requested secret list.
          example:
            - key: API_KEY
              valueRef: 123e4567-e89b-12d3-a456-************
            - key: DB_PASSWORD
              valueRef: 01f016bc-905a-1870-9bda-8549f8c91a42
          items:
            $ref: '#/components/schemas/Secret'
        componentId:
          type: string
          description: "The UUID of the component. This is optional, \nhowever, if the group is scoped at the component level, the componentId should be provided."
          example: 123e4567-e89b-12d3-a456-************
        projectId:
          type: string
          description: |-
            The UUID of the project. This is optional,
            however, if the group is scoped at the project level, the projectId should be provided.
          example: 123e4567-e89b-12d3-a456-************
    Secret:
      required:
        - key
        - valueRef
      type: object
      properties:
        key:
          type: string
          description: The key of the configuration.
          example: API_KEY
        valueRef:
          type: string
          description: The value ref of the configuration.
          example: 123e4567-e89b-12d3-a456-************
        value:
          type: string
          description: The value of the configuration.
          example: secret value
    ResolveSecretsResponse:
      required:
        - secrets
      type: object
      properties:
        secrets:
          type: array
          example:
            - key: API_KEY
              valueRef: 123e4567-e89b-12d3-a456-************
              value: abcd1234-SECRET
            - key: DB_PASSWORD
              valueRef: 01f016bc-905a-1870-9bda-8549f8c91a42
              value: securepassword
          items:
            $ref: '#/components/schemas/Secret'
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - https://api.choreo.dev
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - https://api.choreo.dev
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-svc/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
