{"data": {"component": {"id": "0f2a33d1-1826-4a6b-af04-f0a728420049", "name": "configuration-service", "handler": "kxxdio", "description": " ", "displayType": "proxy", "displayName": "Configuration Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2023-11-02T07:41:19.606Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Configuration Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/config-svc", "proxyId": "6543529f9e53c83b329ab0a5", "id": "6543529f9e53c83b329ab0a5", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "6543529f9e53c83b329ab0a5", "createdAt": "1698910879023", "updatedAt": "2024-06-26 09:34:08.938", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "0f2a33d1-1826-4a6b-af04-f0a728420049", "latest": true, "versionStrategy": ""}]}}}