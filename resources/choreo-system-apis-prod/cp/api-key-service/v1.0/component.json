{"data": {"component": {"id": "3f6cc7c8-0e7b-4723-8e33-78bca2417db1", "name": "API Key Service", "handler": "API Key Service", "description": " ", "displayType": "proxy", "displayName": "API Key Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-10-10T06:32:59.057Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "API Key Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/api-key-service", "proxyId": "670768cb2584260c4314ce42", "id": "670768cb2584260c4314ce42", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "670768cb2584260c4314ce42", "createdAt": "1728538827190", "updatedAt": "2024-10-10 05:40:27.19", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "3f6cc7c8-0e7b-4723-8e33-78bca2417db1", "latest": true, "versionStrategy": ""}]}}}