openapi: 3.1.0
info:
  title: API Key Service
  description: This is an API definition for API Key Service in Choreo
  contact: {}
  version: v1.0
servers:
  - url: http://localhost:9090/93tu/api-key-service/v1.0
security:
  - default: []
tags:
  - name: PAT Self Management
    description: PAT Management Operations
  - name: Internal APIs
    description: APIs exposed for internal services
paths:
  /public/notify-secret-match/git:
    post:
      tags:
        - Public APIs
      summary: Notify secret matches for Github
      description: Public API to notify secret matches for Github
      operationId: notifySecretMatch
      requestBody:
        description: Secret match notification request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GitSecretMatchNotification'
      responses:
        "200":
          description: Successful
        "400":
          description: Client Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: None
  /pat/introspect:
    post:
      tags:
        - PAT Self Management
      summary: Introspect PAT
      description: Introspect a given PAT
      operationId: introspectPAT
      responses:
        "200":
          description: Successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PATIntrospectResp'
        "400":
          description: Client Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pat/generate:
    post:
      tags:
        - PAT Self Management
      summary: Generate a new PAT
      description: Generate a new PAT for the user
      operationId: generatePAT
      requestBody:
        description: PAT Generation Request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PAT'
      responses:
        "200":
          description: Successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PAT'
        "400":
          description: Client Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "409":
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pat:
    get:
      tags:
        - PAT Self Management
      summary: List PATs of the user
      description: List PATs associated to the user
      operationId: listPATs
      responses:
        "200":
          description: Successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PATList'
        "400":
          description: Client Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pat/{id}:
    get:
      tags:
        - PAT Self Management
      summary: Get PAT by ID
      description: Retrieve user PAT by ID
      operationId: getPATByID
      parameters:
        - name: id
          in: path
          description: ID of the PAT
          required: true
          style: simple
          explode: false
          schema: {}
      responses:
        "200":
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PAT'
        "400":
          description: Client Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "404":
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    put:
      tags:
        - PAT Self Management
      summary: Update PAT by ID
      description: Update PAT associated to the ID
      operationId: updatePAT
      parameters:
        - name: id
          in: path
          description: ID of the PAT
          required: true
          style: simple
          explode: false
          schema: {}
      requestBody:
        description: PAT Update Request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PATUpdateReq'
      responses:
        "200":
          description: Successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PAT'
        "400":
          description: Client Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - PAT Self Management
      summary: Delete PAT by ID
      description: Delete the PAT associated to the ID
      operationId: deletePAT
      parameters:
        - name: id
          in: path
          description: ID of the PAT
          required: true
          style: simple
          explode: false
          schema: {}
      responses:
        "204":
          description: No Content
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pat/{id}/regenerate:
    post:
      tags:
        - PAT Self Management
      summary: Regenerate a new PAT
      description: Regenerate a new PAT for PAT ID
      operationId: regeneratePAT
      parameters:
        - name: id
          in: path
          description: ID of the PAT
          required: true
          style: simple
          explode: false
          schema: {}
      requestBody:
        description: Regenerate Request
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegeneratePATReq'
      responses:
        "200":
          description: Successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PAT'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    PAT:
      properties:
        id:
          readOnly: true
          example: 123e4567-e89b-12d3-a456-************
        organizationUuid:
          readOnly: true
          example: 123e4567-e89b-12d3-a456-************
        userIdpId:
          readOnly: true
          example: 123e4567-e89b-12d3-a456-************
        alias:
          example: My PAT
        description:
          example: This is my personal access token
        allowedScopes:
          items:
            example: view:users
        validity:
          writeOnly: true
          example: 365
        validUntil:
          format: date-time
          readOnly: true
          example: "2024-01-01T00:00:00Z"
        status:
          readOnly: true
          example: ACTIVE
          enum:
            - ACTIVE
            - EXPIRED
        pat:
          readOnly: true
          example: chp_******************************************************
    PATIntrospectResp:
      properties:
        id:
          example: 123e4567-e89b-12d3-a456-************
        organizationUuid:
          example: 123e4567-e89b-12d3-a456-************
        userIdpId:
          example: 123e4567-e89b-12d3-a456-************
        allowedScopes:
          items:
            example: view:users
        validUntil:
          format: date-time
          example: "2024-01-01T00:00:00Z"
        status:
          example: ACTIVE
          enum:
            - ACTIVE
            - EXPIRED
    PATUpdateReq:
      properties:
        alias:
          example: My PAT
        description:
          example: This is my personal access token
        allowedScopes:
          items:
            example: view:users
    PATListItem:
      properties:
        id:
          example: 123e4567-e89b-12d3-a456-************
        organizationUuid:
          example: 123e4567-e89b-12d3-a456-************
        userIdpId:
          example: 123e4567-e89b-12d3-a456-************
        alias:
          example: My PAT
        description:
          example: This is my personal access token
        validUntil:
          format: date-time
          example: "2024-01-01T00:00:00Z"
        status:
          example: ACTIVE
          enum:
            - ACTIVE
            - EXPIRED
    PATList:
      properties:
        total: {}
        list:
          items:
            $ref: '#/components/schemas/PATListItem'
    RegeneratePATReq:
      properties:
        validity:
          example: 365
    UserMetadata:
      properties:
        isEnterpriseUser:
          example: true
        enterpriseUserGroups:
          items:
            example: admin
    GitSecretMatchNotification:
      properties:
        token:
          example: chp_******************************************************
        type:
          example: pat
        url:
          example: https://github.com/test-app/settings/secrets.yaml
        source:
          example: content
    Error:
      properties:
        code:
          example: AKS-10001
        message:
          example: Invalid request
        description:
          example: The request is invalid
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
    - token
    - x-request-id
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://api-key-service.prod-choreo-system.svc.cluster.local:8080/api/v1
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://api-key-service.prod-choreo-system.svc.cluster.local:8080/api/v1
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/api-key-service/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
