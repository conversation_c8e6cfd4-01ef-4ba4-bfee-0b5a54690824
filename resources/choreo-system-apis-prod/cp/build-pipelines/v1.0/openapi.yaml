openapi: 3.0.3
info:
  title: Build Pipelines
  description: API for the Build Pipelines.
  contact: {}
  version: v1.0
servers:
  - url: https://app.choreo.dev/93tu/build-pipelines/v1.0
security:
  - default: []
paths:
  /pipelines:
    get:
      tags:
        - org-level-pipelines
      summary: Get all pipelines for an organization
      parameters:
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
        - name: buildpackId
          in: query
          description: The ID of the buildpack
          required: false
          style: form
          explode: false
          schema:
            type: string
          example: nodejs-18
      responses:
        "200":
          description: A list of pipelines
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationLevelBuildPipelinesResponse'
              examples:
                success:
                  summary: Successful response with multiple pipelines
                  value:
                    success: true
                    message: Pipelines retrieved successfully
                    data:
                      - id: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                        orgUuid: 12345678-1234-1234-1234-123456789012
                        name: Node.js CI/CD Pipeline
                        description: Complete CI/CD pipeline for Node.js applications
                        buildpackId: nodejs-18
                        buildpack:
                          id: nodejs-18
                          buildpackImage: choreo/nodejs:18
                          supportedVersions: 16,18,20
                          displayName: Node.js
                          isDefault: true
                          provider: Choreo
                          language: JavaScript
                          iconUrl: https://nodejs.org/static/images/logo.svg
                        createdByUserId: user-456
                        createdAt: "2023-10-15T10:30:00Z"
                        lastUpdatedAt: "2023-10-15T15:45:00Z"
                        sourceType: YAML
                        isActive: true
                      - id: b2c3d4e5-f6g7-8901-bcde-2345678901bc
                        orgUuid: 12345678-1234-1234-1234-123456789012
                        name: Python FastAPI Pipeline
                        description: CI/CD pipeline for Python FastAPI services
                        buildpackId: python-3.11
                        buildpack:
                          id: python-3.11
                          buildpackImage: choreo/python:3.11
                          supportedVersions: 3.9,3.10,3.11
                          displayName: Python
                          isDefault: false
                          provider: Choreo
                          language: Python
                          iconUrl: https://www.python.org/static/favicon.ico
                        createdByUserId: user-789
                        createdAt: "2023-10-16T09:15:00Z"
                        lastUpdatedAt: "2023-10-16T14:20:00Z"
                        sourceType: GIT
                        isActive: true
                empty:
                  summary: No pipelines found
                  value:
                    success: true
                    message: No pipelines found for the organization
                    data: []
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                missing_org_uuid:
                  summary: Missing organization UUID
                  value:
                    message: Organization UUID is required
                invalid_org_uuid:
                  summary: Invalid organization UUID format
                  value:
                    message: Invalid organization UUID format
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                internal_error:
                  summary: Internal server error
                  value:
                    message: An internal server error occurred
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - org-level-pipelines
      summary: Create a pipeline for an organization
      parameters:
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePipelineData'
            examples:
              yaml_pipeline:
                summary: Create pipeline with YAML source
                value:
                  name: React Frontend Pipeline
                  description: Build and deploy React frontend applications
                  sourceType: yaml
                  createdByUserId: user-123
                  yamlBlob: |
                    steps:
                      - name: Install Dependencies
                        template: choreo/npm-install@v1
                      - name: Run Tests
                        template: choreo/npm-test@v1
                      - name: Build Application
                        template: choreo/npm-build@v1
                      - name: Docker Build
                        template: choreo/docker-build@v1
              git_pipeline:
                summary: Create pipeline with Git source
                value:
                  name: Java Spring Boot Pipeline
                  description: CI/CD pipeline for Spring Boot microservices
                  sourceType: git-repo
                  createdByUserId: user-456
                  gitRepoData:
                    repoOrg: my-company
                    repoName: build-templates
                    branch: main
                    filePath: pipelines/spring-boot.yaml
                    gitProvider: github
                    secretRef: github-token-secret
      responses:
        "200":
          description: Pipeline created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationLevelBuildPipelineResponse'
              examples:
                success:
                  summary: Pipeline created successfully
                  value:
                    success: true
                    message: Pipeline created successfully
                    data:
                      id: c3d4e5f6-g7h8-9012-cdef-3456789012cd
                      orgUuid: 12345678-1234-1234-1234-123456789012
                      name: React Frontend Pipeline
                      description: Build and deploy React frontend applications
                      buildpackId: nodejs-18
                      buildpack:
                        id: nodejs-18
                        buildpackImage: choreo/nodejs:18
                        supportedVersions: 16,18,20
                        displayName: Node.js
                        isDefault: true
                        provider: Choreo
                        language: JavaScript
                        iconUrl: https://nodejs.org/static/images/logo.svg
                      createdByUserId: user-123
                      createdAt: "2023-10-17T08:30:00Z"
                      lastUpdatedAt: "2023-10-17T08:30:00Z"
                      sourceType: YAML
                      isActive: true
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_source_type:
                  summary: Invalid source type
                  value:
                    message: sourceType must be either 'yaml' or 'git-repo'
                missing_yaml:
                  summary: Missing YAML content
                  value:
                    message: yamlBlob is required when sourceType is 'yaml'
                missing_git_data:
                  summary: Missing Git repository data
                  value:
                    message: gitRepoData is required when sourceType is 'git-repo'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                creation_failed:
                  summary: Pipeline creation failed
                  value:
                    message: Failed to create pipeline due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}:
    get:
      tags:
        - org-level-pipelines
      summary: Get a pipeline by ID
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      responses:
        "200":
          description: Pipeline retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationLevelBuildPipelineDataResponse'
              examples:
                success:
                  summary: Pipeline with YAML source
                  value:
                    success: true
                    message: Pipeline retrieved successfully
                    data:
                      id: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                      orgUuid: 12345678-1234-1234-1234-123456789012
                      name: Node.js CI/CD Pipeline
                      description: Complete CI/CD pipeline for Node.js applications
                      buildpackId: nodejs-18
                      buildpack:
                        id: nodejs-18
                        buildpackImage: choreo/nodejs:18
                        supportedVersions: 16,18,20
                        displayName: Node.js
                        isDefault: true
                        provider: Choreo
                        language: JavaScript
                        iconUrl: https://nodejs.org/static/images/logo.svg
                      createdByUserId: user-456
                      createdAt: "2023-10-15T10:30:00Z"
                      lastUpdatedAt: "2023-10-15T15:45:00Z"
                      sourceType: YAML
                      isActive: true
                      yaml:
                        id: yaml-version-1
                        versionNumber: 3
                        yamlBlob: |
                          steps:
                            - name: Install Dependencies
                              template: choreo/npm-install@v1
                            - name: Run Tests
                              template: choreo/npm-test@v1
                            - name: Build Application
                              template: choreo/npm-build@v1
                            - name: Docker Build
                              template: choreo/docker-build@v1
                        createdAt: "2023-10-15T15:45:00Z"
                        createdByUserId: user-456
                      gitRepoData:
                        repoOrg: my-company
                        repoName: build-templates
                        branch: main
                        filePath: pipelines/nodejs.yaml
                        gitProvider: github
                        secretRef: github-token-secret
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Pipeline not found
                  value:
                    message: Pipeline with ID 'pipeline-123' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                retrieval_failed:
                  summary: Failed to retrieve pipeline
                  value:
                    message: Failed to retrieve pipeline due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - org-level-pipelines
      summary: Delete a pipeline by ID
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      responses:
        "200":
          description: Pipeline deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineMinimalResponse'
              examples:
                success:
                  summary: Pipeline deleted successfully
                  value:
                    success: true
                    message: Pipeline deleted successfully
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Pipeline not found
                  value:
                    message: Pipeline with ID 'pipeline-123' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                deletion_failed:
                  summary: Failed to delete pipeline
                  value:
                    message: Failed to delete pipeline due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/update-yaml:
    post:
      tags:
        - org-level-pipelines
      summary: Update the yaml of a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePipelineYamlData'
            examples:
              updated_pipeline:
                summary: Update pipeline YAML
                value:
                  yaml: |
                    steps:
                      - name: Install Dependencies
                        template: choreo/npm-install@v1
                      - name: Run Tests
                        template: choreo/npm-test@v1
                      - name: Security Scan
                        template: choreo/security-scan@v1
                      - name: Build Application
                        template: choreo/npm-build@v1
                      - name: Docker Build
                        template: choreo/docker-build@v1
                      - name: Deploy to Staging
                        template: choreo/deploy@v1
                        env:
                          - name: ENVIRONMENT
                            value: staging
      responses:
        "200":
          description: Pipeline yaml updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineYamlResponse'
              examples:
                success:
                  summary: YAML updated successfully
                  value:
                    success: true
                    message: Pipeline YAML updated successfully
                    data:
                      id: yaml-version-2
                      versionNumber: 4
                      yamlBlob: |
                        steps:
                          - name: Install Dependencies
                            template: choreo/npm-install@v1
                          - name: Run Tests
                            template: choreo/npm-test@v1
                          - name: Security Scan
                            template: choreo/security-scan@v1
                          - name: Build Application
                            template: choreo/npm-build@v1
                          - name: Docker Build
                            template: choreo/docker-build@v1
                          - name: Deploy to Staging
                            template: choreo/deploy@v1
                            env:
                              - name: ENVIRONMENT
                                value: staging
                      createdAt: "2023-10-17T09:15:00Z"
                      createdByUserId: user-456
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_yaml:
                  summary: Invalid YAML format
                  value:
                    message: 'Invalid YAML format: syntax error on line 5'
                missing_yaml:
                  summary: Missing YAML content
                  value:
                    message: YAML content is required
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                update_failed:
                  summary: Failed to update YAML
                  value:
                    message: Failed to update pipeline YAML due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/variables:
    get:
      tags:
        - org-level-pipelines
      summary: Get the variables of a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      responses:
        "200":
          description: A list of variables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
              examples:
                with_variables:
                  summary: Pipeline with variables
                  value:
                    success: true
                    message: Variables retrieved successfully
                    data:
                      - id: v1a2b3c4-d5e6-7890-var1-************
                        name: NODE_ENV
                        value: production
                        pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                        createdAt: "2023-10-15T10:30:00Z"
                        updatedAt: "2023-10-15T10:30:00Z"
                      - id: v2a2r3i4-a5b6-7890-var2-************
                        name: APP_VERSION
                        value: 1.2.3
                        pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                        createdAt: "2023-10-15T10:30:00Z"
                        updatedAt: "2023-10-16T14:20:00Z"
                      - id: v3a3r3i4-a5b6-7890-var3-************
                        name: BUILD_TIMEOUT
                        value: "600"
                        pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                        createdAt: "2023-10-15T10:30:00Z"
                        updatedAt: "2023-10-15T10:30:00Z"
                empty:
                  summary: No variables found
                  value:
                    success: true
                    message: No variables found for pipeline
                    data: []
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                pipeline_not_found:
                  summary: Pipeline not found
                  value:
                    message: Pipeline with ID 'pipeline-123' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                retrieval_failed:
                  summary: Failed to retrieve variables
                  value:
                    message: Failed to retrieve pipeline variables due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - org-level-pipelines
      summary: Upsert variables for a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
            examples:
              multiple_variables:
                summary: Create/update multiple variables
                value:
                  - key: NODE_ENV
                    value: production
                  - key: APP_VERSION
                    value: 2.0.0
                  - key: MAX_MEMORY
                    value: 2GB
                  - key: BUILD_TIMEOUT
                    value: "900"
              single_variable:
                summary: Create/update single variable
                value:
                  - key: DEBUG_MODE
                    value: "false"
      responses:
        "200":
          description: Variables upserted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
              examples:
                success:
                  summary: Variables created/updated successfully
                  value:
                    success: true
                    message: Variables upserted successfully
                    data:
                      - id: v1a2b3c4-d5e6-7890-var1-************
                        name: NODE_ENV
                        value: production
                        pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                        createdAt: "2023-10-15T10:30:00Z"
                        updatedAt: "2023-10-17T09:15:00Z"
                      - id: v4a4r4i4-a5b6-7890-var4-************
                        name: APP_VERSION
                        value: 2.0.0
                        pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                        createdAt: "2023-10-17T09:15:00Z"
                        updatedAt: "2023-10-17T09:15:00Z"
                      - id: v5a5r5i5-a5b6-7890-var5-************
                        name: MAX_MEMORY
                        value: 2GB
                        pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                        createdAt: "2023-10-17T09:15:00Z"
                        updatedAt: "2023-10-17T09:15:00Z"
                      - id: v6a6r6i6-a5b6-7890-var6-************
                        name: BUILD_TIMEOUT
                        value: "900"
                        pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                        createdAt: "2023-10-17T09:15:00Z"
                        updatedAt: "2023-10-17T09:15:00Z"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_format:
                  summary: Invalid variable format
                  value:
                    message: Variable key cannot be empty
                duplicate_keys:
                  summary: Duplicate variable keys
                  value:
                    message: Duplicate variable keys found in request
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                upsert_failed:
                  summary: Failed to upsert variables
                  value:
                    message: Failed to upsert pipeline variables due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/variables/{variableId}:
    put:
      tags:
        - org-level-pipelines
      summary: Update a variable for a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: v1a2b3c4-d5e6-7890-var1-************
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PipelineVariable'
      responses:
        "200":
          description: Variable updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariableResponse'
              examples:
                success:
                  summary: Variable updated successfully
                  value:
                    success: true
                    message: Variable updated successfully
                    data:
                      id: v1a2b3c4-d5e6-7890-var1-************
                      name: NODE_ENV
                      value: production
                      pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                      createdAt: "2023-10-15T10:30:00Z"
                      updatedAt: "2023-10-17T09:15:00Z"
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Pipeline not found
                  value:
                    message: Pipeline with ID 'pipeline-123' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                update_failed:
                  summary: Failed to update variable
                  value:
                    message: Failed to update variable due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - org-level-pipelines
      summary: Delete a variable for a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: v1a2b3c4-d5e6-7890-var1-************
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      responses:
        "200":
          description: Variable deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineMinimalResponse'
              examples:
                success:
                  summary: Variable deleted successfully
                  value:
                    success: true
                    message: Variable deleted successfully
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Variable not found
                  value:
                    message: Variable with ID 'var-123' not found in pipeline
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                deletion_failed:
                  summary: Failed to delete variable
                  value:
                    message: Failed to delete variable due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/secrets:
    get:
      tags:
        - org-level-pipelines
      summary: Get the secrets of a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      responses:
        "200":
          description: A list of secrets
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretsResponse'
              examples:
                with_secrets:
                  summary: Pipeline with secrets
                  value:
                    success: true
                    message: Secrets retrieved successfully
                    data:
                      - id: s1e2c3r4-e5t6-7890-sec1-************
                        name: DATABASE_PASSWORD
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                        createdAt: "2023-10-15T10:30:00Z"
                        updatedAt: "2023-10-15T15:45:00Z"
                      - id: s2e2c3r4-e5t6-7890-sec2-************
                        name: API_KEY
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                        createdAt: "2023-10-16T09:15:00Z"
                        updatedAt: "2023-10-16T14:20:00Z"
                empty:
                  summary: No secrets found
                  value:
                    success: true
                    message: No secrets found for pipeline
                    data: []
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Pipeline not found
                  value:
                    message: Pipeline with ID 'pipeline-123' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                retrieval_failed:
                  summary: Failed to retrieve secrets
                  value:
                    message: Failed to retrieve pipeline secrets due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - org-level-pipelines
      summary: Create a secret for a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
            examples:
              single_secret:
                summary: Create single secret
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
              multiple_secrets:
                summary: Create multiple secrets
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
                  - key: API_KEY
                    value: sk_live_abcdef123456
      responses:
        "200":
          description: Secret created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretResponse'
              examples:
                success:
                  summary: Secret created successfully
                  value:
                    success: true
                    message: Secret created successfully
                    data:
                      id: s2e2c3r4-e5t6-7890-sec2-************
                      name: DATABASE_PASSWORD
                      pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                      createdAt: "2023-10-17T08:30:00Z"
                      updatedAt: "2023-10-17T08:30:00Z"
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Pipeline not found
                  value:
                    message: Pipeline with ID 'pipeline-123' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                creation_failed:
                  summary: Failed to create secret
                  value:
                    message: Failed to create secret due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /pipelines/{pipelineId}/secrets/{secretId}:
    put:
      tags:
        - org-level-pipelines
      summary: Update a secret for a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: s1e2c3r4-e5t6-7890-sec1-************
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PipelineSecret'
      responses:
        "200":
          description: Secret updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretResponse'
              examples:
                success:
                  summary: Secret updated successfully
                  value:
                    success: true
                    message: Secret updated successfully
                    data:
                      id: s1e2c3r4-e5t6-7890-sec1-************
                      name: DATABASE_PASSWORD
                      pipelineId: a1b2c3d4-e5f6-7890-abcd-1234567890ab
                      createdAt: "2023-10-15T10:30:00Z"
                      updatedAt: "2023-10-17T09:15:00Z"
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Pipeline not found
                  value:
                    message: Pipeline with ID 'pipeline-123' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                update_failed:
                  summary: Failed to update secret
                  value:
                    message: Failed to update secret due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      tags:
        - org-level-pipelines
      summary: Delete a secret for a pipeline
      parameters:
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: a1b2c3d4-e5f6-7890-abcd-1234567890ab
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: s1e2c3r4-e5t6-7890-sec1-************
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      responses:
        "200":
          description: Secret deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineMinimalResponse'
              examples:
                success:
                  summary: Secret deleted successfully
                  value:
                    success: true
                    message: Secret deleted successfully
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                not_found:
                  summary: Secret not found
                  value:
                    message: Secret with ID 'secret-123' not found in pipeline
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                deletion_failed:
                  summary: Failed to delete secret
                  value:
                    message: Failed to delete secret due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /projects/{projectId}/pipelines:
    get:
      summary: Get all pipelines for a project
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: p1r2o3j4-e5c6-7890-proj-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
        - name: buildpackId
          in: query
          description: The ID of the buildpack
          required: false
          style: form
          explode: false
          schema:
            type: string
          example: nodejs-18
      responses:
        "200":
          description: A list of pipelines
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectLevelBuildPipelinesResponse'
              examples:
                with_pipelines:
                  summary: Project with attached pipelines
                  value:
                    success: true
                    message: Project pipelines retrieved successfully
                    data:
                      - id: pp123456-**************-************
                        projectId: p1r2o3j4-e5c6-7890-proj-1234567890ab
                        orgBuildPipelineId: b2c3d4e5-f6g7-8901-bcde-2345678901bc
                        orgBuildPipeline:
                          id: b2c3d4e5-f6g7-8901-bcde-2345678901bc
                          orgUuid: 12345678-1234-1234-1234-123456789012
                          name: Node.js Production Pipeline
                          description: Production-ready CI/CD for Node.js apps
                          buildpackId: nodejs-18
                          sourceType: YAML
                          isActive: true
                        addedByUserId: user-123
                        createdAt: "2023-10-16T10:00:00Z"
                        lastUpdatedAt: "2023-10-16T10:00:00Z"
                      - id: pp456789-0123-4567-8901-************
                        projectId: p1r2o3j4-e5c6-7890-proj-1234567890ab
                        orgBuildPipelineId: c3d4e5f6-g7h8-9012-cdef-3456789012cd
                        orgBuildPipeline:
                          id: c3d4e5f6-g7h8-9012-cdef-3456789012cd
                          orgUuid: 12345678-1234-1234-1234-123456789012
                          name: Testing Pipeline
                          description: Development testing pipeline
                          buildpackId: nodejs-18
                          sourceType: GIT
                          isActive: true
                        addedByUserId: user-456
                        createdAt: "2023-10-17T08:30:00Z"
                        lastUpdatedAt: "2023-10-17T08:30:00Z"
                empty:
                  summary: No pipelines found
                  value:
                    success: true
                    message: No pipelines found for project
                    data: []
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                project_not_found:
                  summary: Project not found
                  value:
                    message: Project with ID 'project-789' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                retrieval_failed:
                  summary: Failed to retrieve pipelines
                  value:
                    message: Failed to retrieve project pipelines due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Create a pipeline for a project
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: p1r2o3j4-e5c6-7890-proj-1234567890ab
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
          example: 12345678-1234-1234-1234-123456789012
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProjectLevelBuildPipelineData'
            examples:
              attach_pipeline:
                summary: Attach existing organization pipeline to project
                value:
                  orgBuildPipelineId: b2c3d4e5-f6g7-8901-bcde-2345678901bc
      responses:
        "200":
          description: Pipeline created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectLevelBuildPipelineResponse'
              examples:
                success:
                  summary: Pipeline attached to project successfully
                  value:
                    success: true
                    message: Pipeline attached to project successfully
                    data:
                      id: ppnew123-**************-************
                      projectId: p1r2o3j4-e5c6-7890-proj-1234567890ab
                      orgBuildPipelineId: b2c3d4e5-f6g7-8901-bcde-2345678901bc
                      orgBuildPipeline:
                        id: b2c3d4e5-f6g7-8901-bcde-2345678901bc
                        orgUuid: 12345678-1234-1234-1234-123456789012
                        name: Node.js Production Pipeline
                        description: Production-ready CI/CD for Node.js apps
                        buildpackId: nodejs-18
                        buildpack:
                          id: nodejs-18
                          buildpackImage: choreo/nodejs:18
                          displayName: Node.js
                          language: JavaScript
                        sourceType: YAML
                        isActive: true
                        createdAt: "2023-10-15T10:30:00Z"
                        lastUpdatedAt: "2023-10-15T15:45:00Z"
                      addedByUserId: user-123
                      createdAt: "2023-10-17T10:00:00Z"
                      lastUpdatedAt: "2023-10-17T10:00:00Z"
                      yaml:
                        id: yaml-version-1
                        versionNumber: 2
                        yamlBlob: |
                          steps:
                            - name: Install Dependencies
                              template: choreo/npm-install@v1
                            - name: Run Tests
                              template: choreo/npm-test@v1
                            - name: Build Application
                              template: choreo/npm-build@v1
                            - name: Docker Build
                              template: choreo/docker-build@v1
                        createdAt: "2023-10-15T15:45:00Z"
                        createdByUserId: user-456
                      gitRepoData:
                        repoOrg: my-company
                        repoName: build-templates
                        branch: main
                        filePath: pipelines/nodejs-prod.yaml
                        gitProvider: github
                        secretRef: github-token-secret
                      pipelineYaml: |
                        steps:
                          - name: Install Dependencies
                            template: choreo/npm-install@v1
                          - name: Run Tests
                            template: choreo/npm-test@v1
                          - name: Build Application
                            template: choreo/npm-build@v1
                          - name: Docker Build
                            template: choreo/docker-build@v1
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                project_not_found:
                  summary: Project not found
                  value:
                    message: Project with ID 'project-789' not found
                pipeline_not_found:
                  summary: Organization pipeline not found
                  value:
                    message: Organization pipeline with ID 'pipeline-456' not found
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                creation_failed:
                  summary: Failed to attach pipeline
                  value:
                    message: Failed to attach pipeline to project due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /projects/{projectId}/pipelines/{pipelineId}:
    get:
      summary: Get a pipeline for a project
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: A pipeline
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectLevelBuildPipelineResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
    delete:
      summary: Delete a pipeline for a project
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Pipeline deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineMinimalResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
  /projects/{projectId}/pipelines/{pipelineId}/secrets:
    get:
      summary: Get all secrets for a pipeline
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: A list of secrets
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
    post:
      summary: Create a secret for a pipeline
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
      responses:
        "200":
          description: A list of secrets
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
  /projects/{projectId}/pipelines/{pipelineId}/secrets/{secretId}:
    put:
      summary: Update a secret for a pipeline
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeyValuePair'
      responses:
        "200":
          description: A list of secrets
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
    delete:
      summary: Delete a secret for a pipeline
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: A list of secrets
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
  /projects/{projectId}/pipelines/{pipelineId}/variables:
    get:
      summary: Get all variables for a pipeline
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: A list of variables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
    post:
      summary: Create a variable for a pipeline
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
      responses:
        "200":
          description: A list of variables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
  /projects/{projectId}/pipelines/{pipelineId}/variables/{variableId}:
    put:
      summary: Update a variable for a pipeline
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeyValuePair'
      responses:
        "200":
          description: A list of variables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
    delete:
      summary: Delete a variable for a pipeline
      parameters:
        - name: projectId
          in: path
          description: The ID of the project
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: A list of variables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
  /components/{componentId}/default-pipeline:
    get:
      summary: Get the default pipeline for a component
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: c1o2m3p4-o5n6-7890-comp-1234567890ab
      responses:
        "200":
          description: Base64 encoded default pipeline yaml
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: string
              examples:
                multiple_secrets:
                  summary: Response with multiple secrets
                  value:
                    data: c3RlcHM6CiAgICAgIC0gbmFtZTogRG9ja2VyZmlsZSBTY2FuCiAgICAgICAgdGVtcGxhdGU6IGNob3Jlby9kb2NrZXJmaWxlLXNjYW5AdjEKICAgICAgLSBuYW1lOiBEb2NrZXIgQnVpbGQKICAgICAgICB0ZW1wbGF0ZTogY2hvcmVvL2RvY2tlci1idWlsZEB2MQogICAgICAtIG5hbWU6IFZ1bG5lcmFiaWxpdHkgU2NhbgogICAgICAgIHRlbXBsYXRlOiBjaG9yZW8vdHJpdnktc2NhbkB2MQ==
                empty:
                  summary: Response with no secrets
                  value:
                    data: []
        "404":
          description: Component or deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/pipeline:
    get:
      summary: Get the pipeline for a deployment track
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: c1o2m3p4-o5n6-7890-comp-1234567890ab
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: d1e2p3l4-o5y6-7890-trak-1234567890ab
      responses:
        "200":
          description: Base64 encoded pipeline yaml
          content:
            application/json:
              schema:
                type: object
                example:
                  success: true
                  data:
                    yaml: c3RlcHM6CiAgLSBuYW1lOiBEb2NrZXJmaWxlIFNjYW4KICAgIHRlbXBsYXRlOiBjaG9yZW8vZG9ja2VyZmlsZS1zY2FuQHYxCiAgLSAtIG5hbWU6IEdvIFRlc3QKICAgICAgdGVtcGxhdGU6IGdvLXRlc3QKICAgIC0gbmFtZTogR28gTGludAogICAgICB0ZW1wbGF0ZTogZ28tbGludAogIC0gbmFtZTogRG9ja2VyIEJ1aWxkCiAgICB0ZW1wbGF0ZTogY2hvcmVvL2RvY2tlci1idWlsZEB2MQogIC0gbmFtZTogVnVsbmVyYWJpbGl0eSBTY2FuCiAgICB0ZW1wbGF0ZTogY2hvcmVvL3RyaXZ5LXNjYW5AdjEKICAtIG5hbWU6IFNsYWNrIE5vdGlmaWNhdGlvbgogICAgdGVtcGxhdGU6IHNsYWNrLW5vdGlmaWNhdGlvbgp0ZW1wbGF0ZXM6CiAgLSBuYW1lOiBnby10ZXN0CiAgICBpbmxpbmVTY3JpcHQ6IHwKICAgICAgIyEvYmluL2Jhc2gKICAgICAgZWNobyAiSW5zdGFsbGluZyBHby4uLiIKICAgICAgYXB0IHVwZGF0ZSAmJiBhcHQgaW5zdGFsbCAteSBnb2xhbmcKCiAgICAgIGVjaG8gIkNoYW5naW5nIGRpcmVjdG9yeSB0byByZXBvc2l0b3J5IGRpcmVjdG9yeS4uLiIKICAgICAgY2QgJFJFUE9TSVRPUllfRElSCiAgICAgIGVjaG8gIlJ1bm5pbmcgR28gdGVzdC4uLiIKICAgICAgZ28gdGVzdAogICAgZW52OgogICAgICAtIG5hbWU6IEdPX1ZFUlNJT04KICAgICAgICB2YWx1ZTogIjEuMjAiCiAgLSBuYW1lOiBnby1saW50CiAgICBpbmxpbmVTY3JpcHQ6IHwKICAgICAgIyEvYmluL2Jhc2gKICAgICAgIyBlY2hvICJJbnN0YWxsaW5nIEdvLi4uIgogICAgICAjIGFwdCB1cGRhdGUgJiYgYXB0IGluc3RhbGwgLXkgZ29sYW5nIGdvbGFuZ2NpLWxpbnQKCiAgICAgIGVjaG8gIkNoYW5naW5nIGRpcmVjdG9yeSB0byByZXBvc2l0b3J5IGRpcmVjdG9yeS4uLiIKICAgICAgY2QgJFJFUE9TSVRPUllfRElSCiAgICAgIGVjaG8gIlJ1bm5pbmcgR28gbGludC4uLiIKICAgICAgIyBnb2xhbmdjaS1saW50IHJ1bgogICAgZW52OgogICAgICAtIG5hbWU6IEdPX1ZFUlNJT04KICAgICAgICB2YWx1ZTogIjEuMjAiCiAgLSBuYW1lOiBzbGFjay1ub3RpZmljYXRpb24KICAgIGlubGluZVNjcmlwdDogfAogICAgICAjIS9iaW4vYmFzaAogICAgICBlY2hvICJTZW5kaW5nIFNsYWNrIG5vdGlmaWNhdGlvbi4uLiIKICAgICAgTUVTU0FHRT0iOndoaXRlX2NoZWNrX21hcms6IFBpcGVsaW5lIHN1Y2NlZWRlZCIKICAgICAgY3VybCAtWCBQT1NUIFwKICAgICAgICAtSCAnQ29udGVudC10eXBlOiBhcHBsaWNhdGlvbi9qc29uJyBcCiAgICAgICAgLS1kYXRhICJ7XCJ0ZXh0XCI6XCIkTUVTU0FHRVwifSIgXAogICAgICAgICRTTEFDS19XRUJIT09LX1VSTAogICAgZW52OgogICAgICAtIG5hbWU6IFNMQUNLX1dFQkhPT0tfVVJMCiAgICAgICAgdmFsdWU6ICJ7e1NFQ1JFVFMuU0xBQ0tfV0VCSE9PS19VUkx9fSIK
                    pipelineType: USER_DEFINED
                properties:
                  data:
                    type: object
                    properties:
                      yaml:
                        type: string
                      pipelineType:
                        type: string
                        enum:
                          - USER_DEFINED
                          - CHOREO_DEFINED
        "404":
          description: Component or deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets:
    get:
      summary: Get all secrets for a deployment track
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: A list of secrets
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Secret'
              examples:
                multiple_secrets:
                  summary: Response with multiple secrets
                  value:
                    data:
                      - id: e813c2fd-c312-4b48-83ee-9c38fd54036a
                        name: DATABASE_PASSWORD
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                      - id: a285e0e9-2fb3-4f65-9062-161b71ac0021
                        name: API_KEY
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                empty:
                  summary: Response with no secrets
                  value:
                    data: []
        "404":
          description: Component or deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Create or update multiple secrets
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
            examples:
              single_secret:
                summary: Create single secret
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
              multiple_secrets:
                summary: Create multiple secrets
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
                  - key: API_KEY
                    value: sk_live_abcdef123456
      responses:
        "200":
          description: Secrets created or updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      message:
                        type: string
              examples:
                success:
                  summary: Secrets created or updated successfully
                  value:
                    message: Secrets created or updated successfully
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets/{secretId}:
    put:
      summary: Update a secret
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeyValuePair'
      responses:
        "200":
          description: Secret updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              examples:
                success:
                  summary: Secret updated successfully
                  value:
                    message: Secret updated successfully
        "404":
          description: Secret not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Delete a secret
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Secret deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              examples:
                success:
                  summary: Secret deleted successfully
                  value:
                    message: Secret deleted successfully
        "404":
          description: Secret not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/variables:
    get:
      summary: Get all variables for a deployment track
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Variables retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Variable'
              examples:
                multiple_variables:
                  summary: Response with multiple variables
                  value:
                    data:
                      - id: e813c2fd-c312-4b48-83ee-9c38fd54036a
                        name: DATABASE_PASSWORD
                        value: my-secure-password-123
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                      - id: a285e0e9-2fb3-4f65-9062-161b71ac0021
                        name: API_KEY
                        value: sk_live_abcdef123456
                        componentId: af83ccb1-c10f-48aa-8acf-6d78d5e88a29
                        deploymentTrackId: 91a5a801-f2d0-4078-905b-e18f4fa38f16
                empty:
                  summary: Response with no variables
                  value:
                    data: []
        "404":
          description: Component or deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Create or update multiple variables
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
            examples:
              single_variable:
                summary: Create single variable
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
              multiple_variables:
                summary: Create multiple variables
                value:
                  - key: DATABASE_PASSWORD
                    value: my-secure-password-123
                  - key: API_KEY
                    value: sk_live_abcdef123456
      responses:
        "200":
          description: Variables created or updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              examples:
                success:
                  summary: Variables created or updated successfully
                  value:
                    message: Variables created or updated successfully
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/variables/{variableId}:
    put:
      summary: Update a variable
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: c1o2m3p4-o5n6-7890-comp-1234567890ab
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: d1e2p3l4-o5y6-7890-trak-1234567890ab
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: v2a2r3i4-a5b6-7890-var2-************
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeyValuePair'
            examples:
              update_variable:
                summary: Update variable value
                value:
                  key: MAX_MEMORY
                  value: 4GB
      responses:
        "200":
          description: Variable updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              examples:
                success:
                  summary: Variable updated successfully
                  value:
                    message: Variable updated successfully
        "404":
          description: Variable not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Delete a variable
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Variable deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
              examples:
                success:
                  summary: Variable deleted successfully
                  value:
                    message: Variable deleted successfully
        "404":
          description: Variable not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/active-pipeline:
    get:
      summary: Get the active pipeline for a deployment track
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
        - name: projectId
          in: query
          description: The ID of the project
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Active pipeline retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentTrackLevelBuildPipelineResponse'
        "404":
          description: Deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Attach a pipeline to a deployment track
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
        - name: projectId
          in: query
          description: The ID of the project
          required: true
          style: form
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                orgBuildPipelineId:
                  type: string
              required:
                - orgBuildPipelineId
      responses:
        "200":
          description: YAML retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeploymentTrackLevelBuildPipelineResponse'
        "404":
          description: Deployment track not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets:
    get:
      summary: Get the secrets for a pipeline
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
        - name: projectId
          in: query
          description: The ID of the project
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Secrets retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretsResponse'
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Update the secrets for a pipeline
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
        - name: projectId
          in: query
          description: The ID of the project
          required: true
          style: form
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
      responses:
        "200":
          description: Secrets updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretsResponse'
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets/{secretId}:
    put:
      summary: Update a secret for a pipeline
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeyValuePair'
      responses:
        "200":
          description: A list of secrets
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretResponse'
        "404":
          description: Project not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application user
    delete:
      summary: Delete a secret for a pipeline
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: secretId
          in: path
          description: The ID of the secret
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
        - name: projectId
          in: query
          description: The ID of the project
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Secret deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineSecretsResponse'
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables:
    get:
      summary: Get the variables for a pipeline
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
        - name: projectId
          in: query
          description: The ID of the project
          required: true
          style: form
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Variables retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      summary: Update the variables for a pipeline
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: true
          style: form
          explode: false
          schema:
            type: string
        - name: projectId
          in: query
          description: The ID of the project
          required: true
          style: form
          explode: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/KeyValuePair'
      responses:
        "200":
          description: Variables updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables/{variableId}:
    put:
      summary: Update a variable for a pipeline
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: c1o2m3p4-o5n6-7890-comp-1234567890ab
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: d1e2p3l4-o5y6-7890-trak-1234567890ab
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: b2c3d4e5-f6g7-8901-bcde-2345678901bc
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
          example: v7a7r7i7-a5b6-7890-var7-************
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeyValuePair'
            examples:
              update_variable:
                summary: Update variable for pipeline
                value:
                  key: TIMEOUT_SECONDS
                  value: "300"
      responses:
        "200":
          description: Variable updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
              examples:
                success:
                  summary: Variable updated successfully
                  value:
                    success: true
                    message: Variable updated successfully
                    data:
                      - id: v7a7r7i7-a5b6-7890-var7-************
                        name: TIMEOUT_SECONDS
                        value: "300"
                        pipelineId: b2c3d4e5-f6g7-8901-bcde-2345678901bc
                        createdAt: "2023-10-15T10:30:00Z"
                        updatedAt: "2023-10-17T09:15:00Z"
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                pipeline_not_found:
                  summary: Pipeline not found
                  value:
                    message: Pipeline with ID 'pipeline-456' not found
                variable_not_found:
                  summary: Variable not found
                  value:
                    message: Variable with ID 'var-789' not found in pipeline
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                update_failed:
                  summary: Failed to update variable
                  value:
                    message: Failed to update variable due to internal error
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    delete:
      summary: Delete a variable for a pipeline
      parameters:
        - name: componentId
          in: path
          description: The ID of the component
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: deploymentTrackId
          in: path
          description: The ID of the deployment track
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: pipelineId
          in: path
          description: The ID of the pipeline
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: variableId
          in: path
          description: The ID of the variable
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        "200":
          description: Variables updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PipelineVariablesResponse'
        "404":
          description: Pipeline not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    PipelineMinimalResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
      required:
        - data
        - message
        - success
    OrganizationLevelBuildPipelinesResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/OrganizationLevelBuildPipeline'
        message:
          type: string
      required:
        - data
        - message
        - success
    OrganizationLevelBuildPipelineResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/OrganizationLevelBuildPipeline'
        message:
          type: string
      required:
        - data
        - message
        - success
    OrganizationLevelBuildPipelineDataResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/OrganizationLevelBuildPipelineData'
        message:
          type: string
      required:
        - data
        - message
        - success
    PipelineYamlResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PipelineYaml'
        message:
          type: string
      required:
        - data
        - message
        - success
    UpdatePipelineYamlData:
      type: object
      properties:
        yaml:
          type: string
      required:
        - yaml
    GitRepoData:
      type: object
      properties:
        repoOrg:
          type: string
        repoName:
          type: string
        branch:
          type: string
        filePath:
          type: string
        gitProvider:
          type: string
        secretRef:
          type: string
      required:
        - branch
        - filePath
        - gitProvider
        - repoName
        - repoOrg
        - secretRef
    CreatePipelineData:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        sourceType:
          type: string
          enum:
            - git-repo
            - yaml
        createdByUserId:
          type: string
        yamlBlob:
          type: string
        gitRepoData:
          $ref: '#/components/schemas/GitRepoData'
      required:
        - createdByUserId
        - name
        - sourceType
    PipelineYaml:
      type: object
      properties:
        id:
          type: string
        versionNumber:
          type: integer
          format: int64
        yamlBlob:
          type: string
        createdAt:
          type: string
        createdByUserId:
          type: string
      required:
        - createdAt
        - createdByUserId
        - id
        - yamlBlob
    OrganizationLevelBuildPipelineData:
      allOf:
        - $ref: '#/components/schemas/OrganizationLevelBuildPipeline'
        - type: object
          properties:
            yaml:
              $ref: '#/components/schemas/PipelineYaml'
            gitRepoData:
              $ref: '#/components/schemas/GitRepoData'
    ProjectLevelBuildPipelineResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ProjectLevelBuildPipelineData'
        message:
          type: string
      required:
        - data
        - message
        - success
    ProjectLevelBuildPipelinesResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProjectLevelBuildPipeline'
        message:
          type: string
      required:
        - data
        - message
        - success
    CreateProjectLevelBuildPipelineData:
      type: object
      properties:
        orgBuildPipelineId:
          type: string
    ProjectLevelBuildPipelineData:
      allOf:
        - $ref: '#/components/schemas/ProjectLevelBuildPipeline'
        - type: object
          properties:
            yaml:
              $ref: '#/components/schemas/PipelineYaml'
            gitRepoData:
              $ref: '#/components/schemas/GitRepoData'
            pipelineYaml:
              type: string
    DeploymentTrackLevelBuildPipelineResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeploymentTrackLevelBuildPipeline'
        message:
          type: string
      required:
        - data
        - message
    DeploymentTrackLevelBuildPipeline:
      type: object
      properties:
        id:
          type: string
        componentId:
          type: string
        deploymentTrackId:
          type: string
        orgBuildPipelineId:
          type: string
        orgBuildPipeline:
          $ref: '#/components/schemas/OrganizationLevelBuildPipeline'
        attachedByUserId:
          type: string
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time
        lastUpdatedAt:
          type: string
          format: date-time
    ProjectLevelBuildPipeline:
      type: object
      properties:
        id:
          type: string
        projectId:
          type: string
        orgBuildPipelineId:
          type: string
        orgBuildPipeline:
          $ref: '#/components/schemas/OrganizationLevelBuildPipeline'
        addedByUserId:
          type: string
        createdAt:
          type: string
          format: date-time
        lastUpdatedAt:
          type: string
          format: date-time
    OrganizationLevelBuildPipeline:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the organization level build pipeline
        orgUuid:
          type: string
          description: UUID of the organization
        name:
          type: string
          description: Name of the build pipeline
        description:
          type: string
          description: Description of the build pipeline
        buildpackId:
          type: string
          description: ID of the associated buildpack
        buildpack:
          $ref: '#/components/schemas/Buildpack'
        createdByUserId:
          type: string
          description: ID of the user who created the pipeline
        createdAt:
          type: string
          format: date-time
          description: Timestamp when the pipeline was created
        lastUpdatedAt:
          type: string
          format: date-time
          description: Timestamp when the pipeline was last updated
        sourceType:
          type: string
          description: Source type of the pipeline
          enum:
            - CHOREO_PROVIDED
            - YAML
            - GIT
        isActive:
          type: boolean
          description: Whether the pipeline is active
    Buildpack:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the buildpack
        buildpackImage:
          type: string
          description: Docker image for the buildpack
        supportedVersions:
          type: string
          description: Supported versions for the buildpack
        displayName:
          type: string
          description: Display name of the buildpack
        isDefault:
          type: boolean
          description: Whether this is the default buildpack
        buidpackProviderOrgUuid:
          type: string
          description: UUID of the buildpack provider organization
        language:
          type: string
          description: Programming language or technology supported
        versionEnvVariable:
          type: string
          description: Environment variable for version specification
        provider:
          type: string
          description: Provider of the buildpack
        iconUrl:
          type: string
          description: URL to the buildpack icon
    UpdatePipelineYaml:
      type: object
      properties:
        yaml:
          type: string
      required:
        - yaml
    KeyValuePair:
      type: object
      properties:
        key:
          type: string
        value:
          type: string
    PipelineSecretsResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/PipelineSecret'
        message:
          type: string
      required:
        - data
        - message
        - success
    PipelineSecretResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PipelineSecret'
        message:
          type: string
      required:
        - data
        - message
        - success
    PipelineVariableResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PipelineVariable'
        message:
          type: string
      required:
        - data
        - message
        - success
    PipelineVariablesResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/PipelineVariable'
        message:
          type: string
      required:
        - data
        - message
        - success
    CreatePipelineSecretData:
      type: object
      properties:
        secrets:
          type: array
          items:
            $ref: '#/components/schemas/PipelineSecret'
    PipelineSecret:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        pipelineId:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
    PipelineVariable:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        value:
          type: string
        pipelineId:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
    Secret:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        componentId:
          type: string
        deploymentTrackId:
          type: string
    Variable:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        value:
          type: string
        componentId:
          type: string
        deploymentTrackId:
          type: string
    ErrorResponse:
      type: object
      properties:
        message:
          type: string
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
  corsOverrideEnabled: true
x-wso2-production-endpoints:
  urls:
    - http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipelines/v1.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
