{"data": {"component": {"id": "fa2f426a-0fbb-401f-9578-49f9d6ef79a2", "name": "build-pipelines", "handler": "build-pipelines", "description": " ", "displayType": "proxy", "displayName": "build-pipelines", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-03-06T04:45:56.966Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "Build Pipelines", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipelines", "proxyId": "67c9287304e65a6a11828e40", "id": "67c9287304e65a6a11828e40", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67c9287304e65a6a11828e40", "createdAt": "1741236339290", "updatedAt": "2025-03-06 04:45:39.29", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "fa2f426a-0fbb-401f-9578-49f9d6ef79a2", "latest": true, "versionStrategy": ""}]}}}