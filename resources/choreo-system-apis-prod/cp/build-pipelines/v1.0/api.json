{"id": "67c9287304e65a6a11828e40", "name": "Build Pipelines", "displayName": "Build Pipelines", "description": "API for the Build Pipelines.", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/build-pipelines", "version": "v1.0", "provider": "0228631a-5468-445b-af43-ba78b5468457", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "apiKeyHeader": null, "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PUBLIC", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"], "corsOverrideEnabled": true}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1741236339290", "lastUpdatedTime": "2025-07-28 10:26:38.806", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}, "production_endpoints": {"url": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "endpointImplementationType": "ENDPOINT", "subTypeConfiguration": {"subType": "DEFAULT", "egress": false, "configuration": null}, "tokenBasedThrottlingConfiguration": null, "scopes": [], "scopePrefix": null, "operations": [{"id": "", "target": "/pipelines", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/update-yaml", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/update-yaml", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/variables", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/variables", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/variables", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/variables", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/variables/{variableId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/variables/{variableId}", "verb": "PUT", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/variables/{variableId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/variables/{variableId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/secrets", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/secrets", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/secrets", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/secrets", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/secrets/{secretId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/secrets/{secretId}", "verb": "PUT", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/pipelines/{pipelineId}/secrets/{secretId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/pipelines/{pipelineId}/secrets/{secretId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}/secrets", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}/secrets", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}/secrets", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}/secrets", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}/secrets/{secretId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}/secrets/{secretId}", "verb": "PUT", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}/secrets/{secretId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}/secrets/{secretId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}/variables", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}/variables", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}/variables", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}/variables", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}/variables/{variableId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}/variables/{variableId}", "verb": "PUT", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/projects/{projectId}/pipelines/{pipelineId}/variables/{variableId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/projects/{projectId}/pipelines/{pipelineId}/variables/{variableId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/default-pipeline", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/default-pipeline", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipeline", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipeline", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets/{secretId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets/{secretId}", "verb": "PUT", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets/{secretId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/secrets/{secretId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/variables", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/variables", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/variables", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/variables", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/variables/{variableId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/variables/{variableId}", "verb": "PUT", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/variables/{variableId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/variables/{variableId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/active-pipeline", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/active-pipeline", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/active-pipeline", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/active-pipeline", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets/{secretId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets/{secretId}", "verb": "PUT", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets/{secretId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/secrets/{secretId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables", "verb": "GET", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables", "verb": "GET", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables", "verb": "POST", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables", "verb": "POST", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables/{variableId}", "verb": "PUT", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables/{variableId}", "verb": "PUT", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}, {"id": "", "target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables/{variableId}", "verb": "DELETE", "description": null, "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "dependents": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "schemaDefinition": null, "operationPolicies": {"request": [], "response": [], "fault": []}, "backendOperationMapping": {"backendId": "f4f1b2f6-b1b9-41e6-8879-e6dbad9ababf", "backendOperation": {"target": "/components/{componentId}/deployment-tracks/{deploymentTrackId}/pipelines/{pipelineId}/variables/{variableId}", "verb": "DELETE", "endpoint": "http://devops-portal-api.prod-choreo-system.svc.cluster.local:8089/api/v1/build-pipelines"}}, "operationProxyMapping": null}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "devPortal": null, "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "0228631a-5468-445b-af43-ba78b5468457", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "fa2f426a-0fbb-401f-9578-49f9d6ef79a2", "versionId": "67c9287304e65a6a11828e40"}}