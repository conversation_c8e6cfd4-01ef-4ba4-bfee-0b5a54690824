{"id": "6423d5729382a77e3d50c19e", "name": "OnPremKey Management", "displayName": "OnPremKey Management", "description": "This is the Choreo APIInsights AccessKeys API", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/onprem-key-mgt", "version": "1.0.0", "provider": "417d51b1-f15b-489a-881a-4bfd29a22117", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": false, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1680070002236", "lastUpdatedTime": "2024-07-06 06:11:33.561", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://api-server.prod-choreo-system.svc.cluster.local:80"}, "production_endpoints": {"url": "http://api-server.prod-choreo-system.svc.cluster.local:80"}}, "endpointImplementationType": "ENDPOINT", "scopes": [{"scope": {"id": null, "name": "on_prem_key_create", "displayName": "on_prem_key_create", "description": "Create on prem key", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "on_prem_key_delete", "displayName": "on_prem_key_delete", "description": "Delete on prem key", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "on_prem_key_manage", "displayName": "on_prem_key_manage", "description": "Manage on prem key", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "on_prem_key_update", "displayName": "on_prem_key_update", "description": "Update on prem key", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "on_prem_key_view", "displayName": "on_prem_key_view", "description": "View on prem key", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "openid", "displayName": "openid", "description": "Authorize access to user details", "bindings": [], "usageCount": null}, "shared": false}], "scopePrefix": "urn:choreosystem:onpremkeymanagement:", "operations": [{"id": "", "target": "/orgs/{org}/keys/subscription-info", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["on_prem_key_view", "on_prem_key_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{org}/keys", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["on_prem_key_view", "on_prem_key_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{org}/keys", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["on_prem_key_create", "on_prem_key_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{org}/keys/{keyHandle}/revoke", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["on_prem_key_delete", "on_prem_key_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{org}/keys/{keyHandle}", "verb": "PUT", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["on_prem_key_update", "on_prem_key_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{org}/keys/{keyHandle}/regenerate", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["on_prem_key_create", "on_prem_key_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/keys/introspect", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/onprem-keys", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["on_prem_key_view", "on_prem_key_manage"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "417d51b1-f15b-489a-881a-4bfd29a22117", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "8321e156-485b-4428-aed3-b631d769b642", "versionId": "6423d5729382a77e3d50c19e"}}