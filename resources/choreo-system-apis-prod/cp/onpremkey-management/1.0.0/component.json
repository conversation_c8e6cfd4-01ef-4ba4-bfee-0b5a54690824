{"data": {"component": {"id": "8321e156-485b-4428-aed3-b631d769b642", "name": "onpremkey-management", "handler": "xlvtaa", "description": " ", "displayType": "proxy", "displayName": "OnPremKey Management", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "1.0.0", "labels": [], "createdAt": "2023-03-29T06:06:45.752Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "OnPremKey Management", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/onprem-key-mgt", "proxyId": "6423d5729382a77e3d50c19e", "id": "6423d5729382a77e3d50c19e", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "6423d5729382a77e3d50c19e", "createdAt": "1680070002236", "updatedAt": "2024-07-06 06:11:33.561", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "8321e156-485b-4428-aed3-b631d769b642", "latest": true, "versionStrategy": ""}]}}}