openapi: 3.0.3
info:
  title: OnPremKey Management
  description: This is the Choreo APIInsights AccessKeys API
  contact:
    email: <EMAIL>
  version: 1.0.0
servers:
  - url: https://app.choreo.dev/
security:
  - default: []
paths:
  /orgs/{org}/keys/subscription-info:
    get:
      tags:
        - APIInsights/AccessKeys
      summary: Get subscription information for on-premises keys
      operationId: getOnPremKeySubscriptionInfo
      parameters:
        - $ref: '#/components/parameters/org'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionModel'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - on_prem_key_view
            - on_prem_key_manage
        - default:
            - urn:choreosystem:onpremkeymanagement:on_prem_key_view
            - urn:choreosystem:onpremkeymanagement:on_prem_key_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{org}/keys:
    get:
      tags:
        - APIInsights/AccessKeys
      summary: List on-prem keys
      description: Returns a list of on-prem keys for the specified organization
      parameters:
        - $ref: '#/components/parameters/org'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OnPremKeyResponse'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - on_prem_key_view
            - on_prem_key_manage
        - default:
            - urn:choreosystem:onpremkeymanagement:on_prem_key_view
            - urn:choreosystem:onpremkeymanagement:on_prem_key_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
    post:
      tags:
        - APIInsights/AccessKeys
      summary: Create a new on-prem key
      operationId: createOnPremKey
      parameters:
        - $ref: '#/components/parameters/org'
      requestBody:
        description: Payload for creating a new on-prem key
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnPremKeyPayload'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnPremKeyResponse'
        "400":
          description: Bad Request
        "404":
          description: Not Found
      security:
        - OAuth2Security:
            - on_prem_key_create
            - on_prem_key_manage
        - default:
            - urn:choreosystem:onpremkeymanagement:on_prem_key_create
            - urn:choreosystem:onpremkeymanagement:on_prem_key_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{org}/keys/{keyHandle}/revoke:
    post:
      tags:
        - APIInsights/AccessKeys
      summary: Revoke on-prem key by handle
      description: Revoke an on-prem key for a specific handle.
      operationId: revokeOnPremKeyByHandle
      parameters:
        - $ref: '#/components/parameters/org'
        - $ref: '#/components/parameters/keyHandle'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnPremKeyResponse'
        "400":
          description: Invalid request.
        "404":
          description: On-prem key not found.
      security:
        - OAuth2Security:
            - on_prem_key_delete
            - on_prem_key_manage
        - default:
            - urn:choreosystem:onpremkeymanagement:on_prem_key_delete
            - urn:choreosystem:onpremkeymanagement:on_prem_key_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{org}/keys/{keyHandle}:
    put:
      tags:
        - APIInsights/AccessKeys
      summary: Update information for an on-prem key
      operationId: updateOnPremKeyInfo
      parameters:
        - $ref: '#/components/parameters/org'
        - $ref: '#/components/parameters/keyHandle'
      requestBody:
        description: Payload for creating a new on-prem key
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnPremKeyPayload'
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnPremKeyResponse'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - on_prem_key_update
            - on_prem_key_manage
        - default:
            - urn:choreosystem:onpremkeymanagement:on_prem_key_update
            - urn:choreosystem:onpremkeymanagement:on_prem_key_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/{org}/keys/{keyHandle}/regenerate:
    post:
      tags:
        - APIInsights/AccessKeys
      summary: Regenerate an on-prem key
      operationId: regenerateOnPremKey
      parameters:
        - $ref: '#/components/parameters/org'
        - $ref: '#/components/parameters/keyHandle'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnPremKeyResponse'
        "400":
          description: Bad request
        "404":
          description: Not found
      security:
        - OAuth2Security:
            - on_prem_key_create
            - on_prem_key_manage
        - default:
            - urn:choreosystem:onpremkeymanagement:on_prem_key_create
            - urn:choreosystem:onpremkeymanagement:on_prem_key_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /orgs/keys/introspect:
    post:
      tags:
        - APIInsights/AccessKeys
      summary: Introspect on-prem key
      operationId: validateOnPremKey
      requestBody:
        description: Payload for validating an on-prem key
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateOnPremKeyRequest'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateOnPremKeyResponse'
        "400":
          description: Bad Request
        "404":
          description: Not Found
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /onprem-keys:
    get:
      tags:
        - APIInsights/AccessKeys
      summary: List on-prem keys by organization UUID or name
      operationId: listOnPremKeysByOrgUUIDOrOrgName
      parameters:
        - name: orgUuid
          in: query
          description: The UUID of the organization
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: orgName
          in: query
          description: The name of the organization
          required: false
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListOnPremKeysResponse'
        "400":
          description: Bad Request
        "404":
          description: Not Found
      security:
        - OAuth2Security:
            - on_prem_key_view
            - on_prem_key_manage
        - default:
            - urn:choreosystem:onpremkeymanagement:on_prem_key_view
            - urn:choreosystem:onpremkeymanagement:on_prem_key_manage
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    SubscriptionModel:
      type: object
      properties:
        id:
          type: integer
          description: The unique identifier for the subscription.
        orgName:
          type: string
          description: The name of the organization that the subscription is associated with.
        plan:
          type: string
          description: The name of the subscription plan.
        isExpired:
          type: boolean
          description: Indicates whether the subscription has expired.
        startDate:
          type: string
          description: The date and time when the subscription started.
          format: date-time
        endDate:
          type: string
          description: The date and time when the subscription ends.
          format: date-time
    ValidateOnPremKeyRequest:
      type: object
      properties:
        key:
          type: string
    ValidateOnPremKeyResponse:
      type: object
      properties:
        OnPremKey:
          $ref: '#/components/schemas/OnPremKeyResponse'
    ListOnPremKeysResponse:
      type: object
      properties:
        OnPremKeys:
          type: array
          items:
            $ref: '#/components/schemas/OnPremKeyResponse'
    OnPremKeyPayload:
      required:
        - displayName
        - handle
        - key
        - status
      type: object
      properties:
        displayName:
          type: string
        key:
          type: string
        handle:
          type: string
        status:
          type: string
    OnPremKeyResponse:
      type: object
      properties:
        id:
          type: string
        orgId:
          type: string
        orgName:
          type: string
        key:
          type: string
          format: uuid
        status:
          type: string
        displayName:
          type: string
        handle:
          type: string
          format: uuid
        createdBy:
          type: string
          format: uuid
        updatedBy:
          type: string
          format: uuid
        expiresAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
  parameters:
    org:
      name: org
      in: path
      description: The ID of the organization
      required: true
      style: simple
      explode: false
      schema:
        type: string
    keyHandle:
      name: keyHandle
      in: path
      description: The handle of the on-prem key to revoke
      required: true
      style: simple
      explode: false
      schema:
        type: string
  securitySchemes:
    OAuth2Security:
      type: oauth2
      flows:
        password:
          tokenUrl: https://sts.choreo.dev/oauth2/token
          scopes:
            openid: Authorize access to user details
            on_prem_key_create: Create on prem key
            on_prem_key_view: View on prem key
            on_prem_key_delete: Delete on prem key
            on_prem_key_update: Update on prem key
            on_prem_key_manage: Manage on prem key
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            urn:choreosystem:onpremkeymanagement:on_prem_key_manage: Manage on prem key
            urn:choreosystem:onpremkeymanagement:on_prem_key_create: Create on prem key
            urn:choreosystem:onpremkeymanagement:on_prem_key_delete: Delete on prem key
            urn:choreosystem:onpremkeymanagement:on_prem_key_view: View on prem key
            urn:choreosystem:onpremkeymanagement:openid: Authorize access to user details
            urn:choreosystem:onpremkeymanagement:on_prem_key_update: Update on prem key
          x-scopes-bindings:
            urn:choreosystem:onpremkeymanagement:openid: ""
            urn:choreosystem:onpremkeymanagement:on_prem_key_view: ""
            urn:choreosystem:onpremkeymanagement:on_prem_key_update: ""
            urn:choreosystem:onpremkeymanagement:on_prem_key_manage: ""
            urn:choreosystem:onpremkeymanagement:on_prem_key_create: ""
            urn:choreosystem:onpremkeymanagement:on_prem_key_delete: ""
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://api-server.prod-choreo-system.svc.cluster.local:80
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://api-server.prod-choreo-system.svc.cluster.local:80
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/onprem-key-mgt/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
