openapi: 3.0.0
info:
  title: Choreo Audit Logging API
  description: API for retrieving audit logs for Choreo organizations.
  contact:
    name: <PERSON><PERSON><PERSON>
    email: <PERSON><PERSON><PERSON>
  version: 1.0.0
servers:
  - url: https://apis.choreo.dev/93tu/audit-logging/1.0.0
    description: Live server for accessing audit logs for Choreo organizations.
tags:
  - name: Audit
    description: Endpoints for retrieving audit logs
paths:
  /orgs/{orgUuid}/audit-logs:
    post:
      tags:
        - Audit
      summary: Retrieve filtered audit logs for an organization
      description: |
        Allows advanced filtering of audit logs for an organization using a request body.
      operationId: getFilteredAuditLogs
      parameters:
        - $ref: '#/components/parameters/orgUuid'
      requestBody:
        description: Filter criteria for retrieving audit logs.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuditLogRetrievalRequest'
            examples:
              example1:
                value:
                  startTime: "2024-01-01T00:00:00Z"
                  endTime: "2024-01-31T23:59:59Z"
                  limit: 100
                  sort: desc
                  searchText: component deployment
                  projectIds:
                    - 7f893c69-0b90-4c1f-a8c9-2758237e296c
                    - c344a268-8674-4cba-99d8-00b79308dd76
                  userIdpIds:
                    - cb5da71d-3851-43e3-9739-de006e7ad5f6
                  outcomes:
                    - succeeded
        required: true
      responses:
        "200":
          description: OK. Successfully retrieved filtered audit logs for a given organization.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLogList'
              examples:
                success:
                  value:
                    list:
                      - orgUuid: 5edbc70e-3dbb-4ca6-8138-c8125fb89bca
                        timestamp: "1732614244"
                        userIdpId: cb5da71d-3851-43e3-9739-de006e7ad5f6
                        action: initiate deployment
                        entityType: Component
                        info:
                          componentType: ballerinaService
                          componentId: 1454c477-c57f-42ba-8707-ca43b2eb1d7f
                          componentName: choreo-sample-api
                          componentVersion: v1.0
                          projectName: Samples
                          projectId: c698a268-8674-4cba-99d8-00b79308dd89
                          environmentId: a56cf3bd-50ab-4dc8-a571-e04b61b1f6ea
                          environmentName: Development
                        outcome: succeeded
                        message: Initiating component deployment succeeded
                        eventLoggedTime: "2024-11-26T10:24:54Z"
                      - orgUuid: 5edbc70e-3dbb-4ca6-8138-c8125fb89bca
                        timestamp: "1732614355"
                        userIdpId: cb5da71d-3851-43e3-9739-de006e7ad5f6
                        action: initiate deployment
                        entityType: Component
                        info: {}
                        outcome: failed
                        message: Initiating component deployment failed
                        eventLoggedTime: "2024-11-26T10:25:55Z"
        "400":
          $ref: '#/components/responses/BadRequest'
        "401":
          $ref: '#/components/responses/Unauthorized'
        "403":
          $ref: '#/components/responses/Forbidden'
        "404":
          $ref: '#/components/responses/NotFound'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default:
            - urn:choreosystem:choreoauditloggingapi:audit_logs_manage
            - urn:choreosystem:choreoauditloggingapi:audit_logs_view
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /healthz:
    get:
      tags:
        - Audit
      summary: Check API health
      description: Retrieves the health status of the Choreo Audit Logging API service.
      operationId: checkHealth
      responses:
        "200":
          description: OK. Successful response indicating health status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
              examples:
                healthy:
                  value:
                    status: OK
        "401":
          $ref: '#/components/responses/Unauthorized'
        "500":
          $ref: '#/components/responses/InternalServerError'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  schemas:
    AuditLogList:
      required:
        - list
      type: object
      properties:
        list:
          type: array
          description: List of audit logs
          items:
            $ref: '#/components/schemas/AuditLog'
      description: Object representation of a list of audit logs
    AuditLog:
      required:
        - orgUuid
      type: object
      properties:
        orgUuid:
          type: string
          description: Unique identifier for the Choreo organization.
          example: 5edbc70e-3dbb-4ca6-8138-c8125fb89bca
        timestamp:
          type: string
          description: The Unix timestamp (in seconds) when the event occurred, represented as a string.
          example: "1732614388"
        userIdpId:
          type: string
          description: The UUID of the user who performed the action.
          example: cb5da71d-3851-43e3-9739-de006e7ad5f6
        action:
          type: string
          description: The action performed.
          example: initiate deployment
        entityType:
          type: string
          description: Type of entity affected.
          example: Component
        info:
          type: object
          description: Metadata or additional details about the action in json format.
          example:
            componentType: ballerinaService
            componentId: 1454c477-c57f-42ba-8707-ca43b2eb1d7f
            componentName: choreo-sample-api
            projectName: Samples
            projectId: c698a268-8674-4cba-99d8-00b79308dd89
            environmentId: a56cf3bd-50ab-4dc8-a571-e04b61b1f6ea
            environmentName: Development
            componentVersion: v1.0
        outcome:
          type: string
          description: Result of the action.
          example: succeeded
          enum:
            - succeeded
            - failed
        message:
          type: string
          description: Summarized message about the action.
          example: Project creation succeeded
        eventLoggedTime:
          type: string
          description: The time when the event was logged (ISO 8601 format).
          format: date-time
          example: "2024-11-26T10:24:54Z"
      description: Object representation of an audit log
    AuditLogRetrievalRequest:
      required:
        - endTime
        - startTime
      type: object
      properties:
        startTime:
          type: string
          description: Start of the time range for the logs (ISO 8601 format).
          format: date-time
          example: "2024-01-01T00:00:00Z"
        endTime:
          type: string
          description: End of the time range for the logs (ISO 8601 format).
          format: date-time
          example: "2024-01-31T23:59:59Z"
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          description: |
            The maximum number of logs to be fetched.
            Defaults to the maximum if not provided.
          default: 100
        sort:
          type: string
          description: "Sort order of logs by timestamp. \nDefaults to ascending order if none or invalid value is provided.\n"
          enum:
            - asc
            - desc
          default: desc
        searchText:
          type: string
          description: Search keyword or phrase.
          example: component deployment
        projectIds:
          type: array
          description: List of project IDs.
          example:
            - 7f893c69-0b90-4c1f-a8c9-2758237e296c
            - c344a268-8674-4cba-99d8-00b79308dd76
          items:
            type: string
        userIdpIds:
          type: array
          description: List of user IDP IDs.
          example:
            - cb5da71d-3851-43e3-9739-de006e7ad5f6
          items:
            type: string
        outcomes:
          type: array
          description: List of action outcomes.
          example:
            - succeeded
          items:
            type: string
            enum:
              - succeeded
              - failed
      description: Object representation of an audit logs retrieval request.
    HealthStatus:
      required:
        - status
      type: object
      properties:
        status:
          type: string
          description: The status of the health check
          enum:
            - OK
      description: Object representation of the health check response
    Error:
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code.
          format: int64
          example: 400
        message:
          type: string
          description: Short error message.
          example: Bad Request
        description:
          type: string
          description: Detailed error description.
          example: The request is missing a required parameter.
        moreInfo:
          type: string
          description: Additional information or URL.
          example: https://api.docs.choreo.dev/errors/400
        error:
          type: array
          description: |
            If there are more than one error list them out.
            For example, list out validation errors by each field.
          items:
            $ref: '#/components/schemas/ErrorListItem'
      description: Error object returned with 4XX HTTP Status
    ErrorListItem:
      required:
        - code
        - message
      type: object
      properties:
        code:
          type: string
          description: Error code (i.e 400, 404 etc)
          example: "400"
        message:
          type: string
          description: Description about individual errors occurred
          example: Invalid input
        description:
          type: string
          description: A detail description about the error message.
          example: The input value for the field 'sort' is invalid
      description: Description of individual errors that may have occurred during a request.
  responses:
    BadRequest:
      description: Bad Request. Invalid request or validation error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 400
            message: Bad Request
            description: Invalid request or validation error
            moreInfo: ""
            error: []
    Unauthorized:
      description: Unauthorized. The user is not authorized.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 401
            message: Unauthorized
            description: The user is not authorized
            moreInfo: ""
            error: []
    Forbidden:
      description: Forbidden. User not allowed to perform the operation.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 403
            message: Forbidden
            description: User not allowed to perform the operation.
            moreInfo: ""
            error: []
    NotFound:
      description: Not Found. The specified resource does not exist.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 404
            message: Not Found
            description: The specified resource does not exist
            moreInfo: ""
            error: []
    InternalServerError:
      description: Internal Server Error. The server encountered an internal error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: 500
            message: Internal Server Error
            description: The server encountered an internal error
            moreInfo: ""
            error: []
  parameters:
    orgUuid:
      name: orgUuid
      in: path
      description: Unique identifier for the organization
      required: true
      style: simple
      explode: false
      schema:
        type: string
        example: 5edbc70e-3dbb-4ca6-8138-c8125fb89bca
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes:
            urn:choreosystem:choreoauditloggingapi:audit_logs_view: View audit logs
            urn:choreosystem:choreoauditloggingapi:audit_logs_manage: Manage audit logs
          x-scopes-bindings:
            urn:choreosystem:choreoauditloggingapi:audit_logs_view: ""
            urn:choreosystem:choreoauditloggingapi:audit_logs_manage: ""
x-wso2-disable-security: false
x-business-owner:
  name: Sumedha Kodithuwakku
  email: Sumedha Kodithuwakku
x-technical-owner:
  name: Roland Hewage
  email: Roland Hewage
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://audit-logging.prod-choreo-system.svc.cluster.local:8080/audit-logging
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://audit-logging.prod-choreo-system.svc.cluster.local:8080/audit-logging
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
