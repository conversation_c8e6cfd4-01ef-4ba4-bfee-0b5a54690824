{"id": "6757f66a542ddb34b244bb53", "name": "Choreo Audit Logging API", "displayName": "Choreo Audit Logging API", "description": "Owned by Org-Mgt team", "context": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/audit-logging", "version": "v1.0", "provider": "2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f", "lifeCycleStatus": "PUBLISHED", "wsdlInfo": null, "wsdlUrl": null, "responseCachingEnabled": false, "cacheTimeout": 300, "hasThumbnail": false, "isDefaultVersion": false, "isRevision": false, "enableBackendJWT": true, "backendJWTConfiguration": {"audiences": []}, "revisionedApiId": null, "revisionId": 0, "enableSchemaValidation": false, "type": "HTTP", "audience": null, "transport": ["http", "https"], "tags": [], "policies": ["Bronze", "Unlimited"], "apiThrottlingPolicy": null, "throttlingLimit": null, "authorizationHeader": "Authorization", "securityScheme": ["oauth2", "oauth_basic_auth_api_key_mandatory"], "maxTps": null, "visibility": "PRIVATE", "visibleRoles": [""], "visibleTenants": [], "mediationPolicies": [], "subscriptionAvailability": "CURRENT_TENANT", "subscriptionAvailableTenants": [""], "additionalProperties": [{"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": true}, {"name": "accessibility", "value": "external", "display": true}], "additionalPropertiesMap": {"projectId__display": {"name": "projectId", "value": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "display": false}, "accessibility__display": {"name": "accessibility", "value": "external", "display": false}}, "monetization": null, "accessControl": "NONE", "accessControlRoles": [], "businessInformation": {"businessOwner": "<PERSON><PERSON><PERSON>", "businessOwnerEmail": "<PERSON><PERSON><PERSON>", "technicalOwner": "<PERSON>", "technicalOwnerEmail": "<PERSON>"}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": false, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "websubSubscriptionConfiguration": {"enable": false, "secret": "", "signingAlgorithm": "SHA1", "signatureHeader": "x-hub-signature"}, "workflowStatus": null, "createdTime": "1733817962976", "lastUpdatedTime": "2024-12-22 09:59:46.696", "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://audit-logging.prod-choreo-system.svc.cluster.local:8080/audit-logging"}, "production_endpoints": {"url": "http://audit-logging.prod-choreo-system.svc.cluster.local:8080/audit-logging"}}, "endpointImplementationType": "ENDPOINT", "scopes": [{"scope": {"id": null, "name": "audit_logs_manage", "displayName": "audit_logs_manage", "description": "", "bindings": [], "usageCount": null}, "shared": false}, {"scope": {"id": null, "name": "audit_logs_view", "displayName": "audit_logs_view", "description": "", "bindings": [], "usageCount": null}, "shared": false}], "scopePrefix": "urn:choreosystem:choreoauditloggingapi:", "operations": [{"id": "", "target": "/orgs/{orgUuid}/audit-logs", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["audit_logs_manage", "audit_logs_view"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/orgs/{orgUuid}/audit-logs", "verb": "POST", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": ["audit_logs_manage", "audit_logs_view"], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}, {"id": "", "target": "/healthz", "verb": "GET", "authType": "Application & Application User", "throttlingPolicy": "Unlimited", "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "scopes": [], "usedProductIds": [], "amznResourceName": null, "amznResourceTimeout": null, "payloadSchema": null, "uriMapping": null, "operationPolicies": {"request": [], "response": [], "fault": []}}], "threatProtectionPolicies": null, "categories": [], "keyManagers": ["all"], "serviceInfo": null, "advertiseInfo": {"advertised": false, "apiExternalProductionEndpoint": null, "apiExternalSandboxEndpoint": null, "originalDevPortalUrl": null, "apiOwner": "2fc004e1-fa0c-4d19-8cdc-b6ae0e82265f", "vendor": "WSO2"}, "gatewayVendor": "wso2", "gatewayType": "wso2/synapse", "asyncTransportProtocols": [""], "choreoComponentInfo": {"organizationId": "5659b6b7-1063-41ed-8e39-d91857699255", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "componentId": "7b21fa17-a222-4661-83e7-8ff603e2f4f3", "versionId": "6757f66a542ddb34b244bb53"}}