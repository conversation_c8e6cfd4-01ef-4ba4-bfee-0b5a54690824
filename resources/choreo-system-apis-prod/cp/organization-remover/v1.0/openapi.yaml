openapi: 3.0.0
info:
  title: organizationremover
  version: 1.0.0
servers:
  - url: https://app.choreo.dev
security:
  - default: []
paths:
  /publishOrgRemoverEvents:
    post:
      summary: Publish Org Remover Events
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                orgHandleList:
                  type: array
                  items:
                    type: string
                trigerType:
                  type: string
        required: true
      responses:
        '200':
          description: OK
        '400':
          description: BadRequest
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '409':
          description: Conflict
        '500':
          description: InternalServerError
      security:
        - OAuth2Security: []
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
  /org-resource-check/org/{orgHandle}:
    get:
      summary: Get Organization Resource Check
      parameters:
        - name: orgHandle
          in: path
          required: true
          style: simple
          explode: false
          schema:
            type: string
      responses:
        '200':
          description: OK
        '400':
          description: BadRequest
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '409':
          description: Conflict
        '500':
          description: InternalServerError
      security:
        - OAuth2Security: []
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
      x-auth-type: Application & Application User
components:
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-throttling-tier: Unlimited
x-throttling-limit:
  requestCount: -1
  unit: MINUTE
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://organization-remover.prod-choreo-system:8080
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://organization-remover.prod-choreo-system:8080
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/org-remover/v1.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
