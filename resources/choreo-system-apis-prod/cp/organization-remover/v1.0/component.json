{"data": {"component": {"id": "3777c472-a903-428b-a6c1-f24cab68606e", "name": "organizationremover", "handler": "wwwykp", "description": " ", "displayType": "proxy", "displayName": "Organization Remover", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2024-04-16T06:30:32.598Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "v1.0", "proxyName": "organizationremover", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/org-remover", "proxyId": "661e1b0773243166afb811b1", "id": "661e1b0773243166afb811b1", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "661e1b0773243166afb811b1", "createdAt": "1713249031364", "updatedAt": "2024-04-16 06:33:12.126", "apiVersion": "v1.0", "branch": null, "description": null, "componentId": "3777c472-a903-428b-a6c1-f24cab68606e", "latest": true, "versionStrategy": ""}]}}}