{"name": "Spec Populator", "displayName": "Spec Populator", "description": "Spec Populator service extract the marketplace API details and insert into vector DB.", "context": "/spec-populator", "version": "v1.0", "enableBackendJWT": false, "type": "HTTP", "transport": ["https"], "tags": [], "policies": ["Unlimited"], "throttlingLimit": {"requestCount": -1, "unit": "MINUTE"}, "securityScheme": ["oauth2"], "visibility": "RESTRICTED", "visibleRoles": ["admin"], "subscriptionAvailability": "CURRENT_TENANT", "additionalProperties": [{"name": "accessibility", "value": "external", "display": true}], "businessInformation": {"businessOwner": null, "businessOwnerEmail": null, "technicalOwner": null, "technicalOwnerEmail": null}, "corsConfiguration": {"corsConfigurationEnabled": true, "accessControlAllowOrigins": ["*"], "accessControlAllowCredentials": true, "accessControlAllowHeaders": ["authorization", "Access-Control-Allow-Origin", "Content-Type", "SOAPAction", "apikey", "API-Key", "<PERSON><PERSON><PERSON>", "token", "x-request-id"], "accessControlAllowMethods": ["GET", "PUT", "POST", "DELETE", "PATCH", "OPTIONS"]}, "endpointConfig": {"endpoint_type": "http", "sandbox_endpoints": {"url": "http://spec-populator.choreo-ai:8000/"}, "production_endpoints": {"url": "http://spec-populator.choreo-ai:8000/"}}, "endpointImplementationType": "ENDPOINT", "keyManagers": ["all"]}