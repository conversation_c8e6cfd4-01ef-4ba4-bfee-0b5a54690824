{"data": {"component": {"id": "41d275af-3008-4091-af9d-57fca84f040a", "name": "insights", "handler": "9mxglq", "description": " ", "displayType": "proxy", "displayName": "Insights", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "1.0.0", "labels": [], "createdAt": "2021-12-18T12:33:12.788Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "Insights", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/insights", "proxyId": "61bdd480bd84cd17397efee1", "id": "61bdd480bd84cd17397efee1", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "61bdd480bd84cd17397efee1", "createdAt": "1639830656122", "updatedAt": "2023-06-13 03:59:49.203", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "41d275af-3008-4091-af9d-57fca84f040a", "latest": true, "versionStrategy": ""}]}}}