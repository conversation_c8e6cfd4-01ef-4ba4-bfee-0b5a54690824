openapi: 3.0.1
info:
  title: Insights
  contact:
    name: <PERSON><PERSON><PERSON>
    email: f<PERSON><PERSON><PERSON>@wso2.com
  version: 1.0.0
servers:
  - url: https://choreoapis.dev/93tu/insights/1.0.0
security:
  - default: []
paths:
  /query-api:
    post:
      parameters: []
      responses:
        "200":
          description: ok
      security:
        - default: []
      x-auth-type: Application & Application User
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: true
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: true
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - testKey
    - content-type
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - https://controlplane.choreoanalytics.internal
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - https://controlplane.choreoanalytics.internal
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/insights/1.0.0
x-wso2-transports:
  - http
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
