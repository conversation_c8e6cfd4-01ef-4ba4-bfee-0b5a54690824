{"data": {"component": {"id": "02e5f69f-0980-48a2-b0b9-d666c0553eb3", "name": "oas-src-provider-service", "handler": "oas-src-provider-service", "description": " ", "displayType": "proxy", "displayName": "OAS Source Provider Service", "ownerName": null, "orgId": 1885, "orgHandler": "choreosystem", "version": "v1.0", "labels": [], "createdAt": "2025-03-07T15:51:12.521Z", "projectId": "2ffe28dd-5ce3-493f-b0ab-fcf9e09a07a8", "apiId": null, "httpBased": true, "isMigrationCompleted": true, "skipDeploy": false, "isUnifiedConfigMapping": false, "apiVersions": [{"apiVersion": "1.0.0", "proxyName": "OAS Source Provider Service", "proxyUrl": "/5659b6b7-1063-41ed-8e39-d91857699255/93tu/oas-provider-service", "proxyId": "67cb15de8f66af757b4daa37", "id": "67cb15de8f66af757b4daa37", "state": null, "latest": true, "branch": null, "accessibility": "external", "appEnvVersions": []}], "deploymentTracks": [{"id": "67cb15de8f66af757b4daa37", "createdAt": "1741362654526", "updatedAt": "2025-03-07 15:50:54.526", "apiVersion": "1.0.0", "branch": null, "description": null, "componentId": "02e5f69f-0980-48a2-b0b9-d666c0553eb3", "latest": true, "versionStrategy": ""}]}}}