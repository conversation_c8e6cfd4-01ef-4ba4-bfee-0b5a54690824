openapi: 3.0.3
info:
  title: OAS Source Provider Service
  description: API to retrieve organizations, projects, APIs, API versions and OpenAPI definitions from OAS Providers
  contact: {}
  version: 1.0.0
servers:
  - url: https://app.choreo.dev/93tu/oas-provider-service/1.0.0
security:
  - default: []
paths:
  /organizations:
    get:
      summary: Get organizations
      description: Fetch the list of organizations.
      parameters:
        - name: credentialId
          in: query
          description: Credential ID for authentication..
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: orgId
          in: query
          description: Organization ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: integer
        - name: orgUuid
          in: query
          description: Organization UUID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully fetched organizations.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Organization'
        "400":
          description: Bad request. Missing or invalid parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /organizations/{organization}/projects:
    get:
      summary: Get projects
      description: Fetch the list of projects in an organization.
      parameters:
        - name: organization
          in: path
          description: Organization name in OAS provider.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: credentialId
          in: query
          description: Credential ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: orgId
          in: query
          description: Organization ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: integer
        - name: orgUuid
          in: query
          description: Organization UUID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully fetched projects.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Project'
        "400":
          description: Bad request. Missing or invalid parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /organizations/{organization}/projects/{project}/apis:
    get:
      summary: Get APIs
      description: Fetch the list of APIs in a project.
      parameters:
        - name: organization
          in: path
          description: Organization name in OAS provider.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: project
          in: path
          description: Project name in organization.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: credentialId
          in: query
          description: Credential ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: orgId
          in: query
          description: Organization ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: integer
        - name: orgUuid
          in: query
          description: Organization UUID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully fetched APIs.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/API'
        "400":
          description: Bad request. Missing or invalid parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /organizations/{organization}/projects/{project}/apis/{apiName}/versions:
    get:
      summary: Get API versions
      description: Fetch the available versions of an API.
      parameters:
        - name: organization
          in: path
          description: Organization name.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: project
          in: path
          description: Project name.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: apiName
          in: path
          description: API name.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: credentialId
          in: query
          description: Credential ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: orgId
          in: query
          description: Organization ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: integer
        - name: orgUuid
          in: query
          description: Organization UUID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully fetched API versions.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/APIVersion'
        "400":
          description: Bad request. Missing or invalid parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
  /organizations/{organization}/projects/{project}/apis/{apiName}/versions/{apiVersion}/oas-defintion:
    get:
      summary: Fetch OpenAPI definition
      description: Fetch the OpenAPI definition for a specific API version.
      parameters:
        - name: organization
          in: path
          description: Organization name.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: project
          in: path
          description: Project name.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: apiName
          in: path
          description: API name.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: apiVersion
          in: path
          description: API version.
          required: true
          style: simple
          explode: false
          schema:
            type: string
        - name: credentialId
          in: query
          description: Credential ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
        - name: orgId
          in: query
          description: Organization ID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: integer
        - name: orgUuid
          in: query
          description: Organization UUID for authentication.
          required: true
          style: form
          explode: true
          schema:
            type: string
      responses:
        "200":
          description: Successfully fetched OpenAPI definition.
          content:
            application/json:
              schema:
                type: string
        "400":
          description: Bad request. Missing or invalid parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - default: []
      x-throttling-tier: Unlimited
      x-throttling-limit:
        requestCount: -1
        unit: MINUTE
components:
  schemas:
    Organization:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the organization.
        name:
          type: string
          description: Name of the organization.
        description:
          type: string
          description: Optional description of the organization.
        email:
          type: string
          description: Optional email address of the organization.
          format: email
        memberCount:
          type: integer
          description: Optional count of members in the organization.
    Project:
      type: object
      properties:
        organization:
          type: string
          description: Organization the project belongs to.
        name:
          type: string
          description: Name of the project.
        description:
          type: string
          description: Optional description of the project.
        apis:
          type: array
          description: List of API names in the project.
          items:
            type: string
        domains:
          type: array
          description: Optional list of domains associated with the project.
          items:
            type: string
        templates:
          type: array
          description: Optional list of templates associated with the project.
          items:
            type: string
    API:
      type: object
      properties:
        name:
          type: string
          description: Name of the API.
        description:
          type: string
          description: Optional description of the API.
        summary:
          type: string
          description: Optional summary of the API.
        tags:
          type: array
          description: Optional list of tags associated with the API.
          items:
            type: string
        properties:
          type: array
          description: Optional list of additional properties for the API.
          items:
            type: object
            properties:
              type:
                type: string
                description: The type of the property.
              url:
                type: string
                description: Optional URL of the property.
              value:
                type: string
                description: Optional value of the property.
    APIVersion:
      type: object
      properties:
        version:
          type: string
          description: Version of the API.
        url:
          type: string
          description: Optional URL of the API version.
    ErrorResponse:
      type: object
      properties:
        code:
          type: string
          description: Error code.
        message:
          type: string
          description: Error message.
  securitySchemes:
    default:
      type: oauth2
      flows:
        implicit:
          authorizationUrl: https://test.com
          scopes: {}
x-wso2-disable-security: false
x-wso2-auth-header: Authorization
x-wso2-cors:
  corsConfigurationEnabled: false
  accessControlAllowOrigins:
    - '*'
  accessControlAllowCredentials: false
  accessControlAllowHeaders:
    - authorization
    - Access-Control-Allow-Origin
    - Content-Type
    - SOAPAction
    - apikey
    - API-Key
    - testKey
  accessControlAllowMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - PATCH
    - OPTIONS
x-wso2-production-endpoints:
  urls:
    - http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1/oas-service
  type: http
x-wso2-sandbox-endpoints:
  urls:
    - http://dp-cicd.prod-choreo-system.svc.cluster.local:80/api/v1/oas-service
  type: http
x-wso2-basePath: /5659b6b7-1063-41ed-8e39-d91857699255/93tu/oas-provider-service/1.0.0
x-wso2-transports:
  - https
x-wso2-response-cache:
  enabled: false
  cacheTimeoutInSeconds: 300
